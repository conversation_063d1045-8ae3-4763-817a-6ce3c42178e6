#!/usr/bin/env python3
"""
Test the fixed T5810 integration
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
import time

def test_fixed_t5810():
    """Test T5810 with the fixed endpoint configuration"""
    print("🧪 Testing Fixed T5810 Integration")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    test_queries = [
        "List all available codebases",
        "Find memory management functions in the utils codebase",
        "What programming languages are used in the z80emu codebase?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] Testing: '{query}'")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = analyzer.chat_with_tools(query)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"📝 Result length: {len(result)} characters")
            print(f"📊 Result preview:")
            print(result[:400] + "..." if len(result) > 400 else result)
            
            # Check for hallucination indicators
            hallucination_indicators = [
                "I don't have access",
                "I cannot access", 
                "I'm unable to",
                "I don't have information",
                "I cannot provide",
                "without access to",
                "does not exist"
            ]
            
            is_hallucinating = any(indicator.lower() in result.lower() for indicator in hallucination_indicators)
            
            if is_hallucinating:
                print(f"🚨 STILL HALLUCINATING!")
            else:
                # Check for real content
                if query.lower().startswith("list"):
                    if "utils" in result.lower() and "z80emu" in result.lower():
                        print(f"✅ SUCCESS: Found real codebase names")
                    else:
                        print(f"⚠️ May not have real codebase data")
                elif "memory" in query.lower():
                    if any(term in result.lower() for term in ["malloc", "free", "alloc", "void", "int"]):
                        print(f"✅ SUCCESS: Found actual memory management content")
                    else:
                        print(f"⚠️ May not have real memory management content")
                else:
                    print(f"✅ SUCCESS: No hallucination detected")
            
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_tool_execution_directly():
    """Test the fixed tool execution directly"""
    print("\n🔧 Testing Fixed Tool Execution")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Test 1: List codebases
    print("\n1️⃣ Testing list_codebases tool:")
    result = analyzer.execute_tool('list_codebases', {})
    print(f"Result: {result}")
    
    # Test 2: Query utils codebase
    print("\n2️⃣ Testing query_codebase tool:")
    result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'memory management malloc free',
        'max_results': 5
    })
    print(f"Result type: {type(result)}")
    print(f"Result keys: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
    
    if isinstance(result, dict) and 'results' in result:
        results = result['results']
        print(f"Found {len(results)} results")
        for i, res in enumerate(results[:2]):
            content = res.get('content', 'No content')[:200]
            print(f"  Result {i+1}: {content}...")
    else:
        print(f"Result preview: {str(result)[:300]}...")

def main():
    print("🚀 Testing Fixed T5810 Memory Management Query")
    print("=" * 70)
    
    # Test tool execution directly first
    test_tool_execution_directly()
    
    # Test full integration
    test_fixed_t5810()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print("If the tests above show real codebase data and memory management")
    print("functions, then T5810 should no longer hallucinate!")

if __name__ == "__main__":
    main()
