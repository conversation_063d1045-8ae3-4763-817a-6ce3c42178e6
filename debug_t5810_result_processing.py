#!/usr/bin/env python3
"""
Debug why T5810 ignores real timer code results
"""

import requests
import json

def test_t5810_step_by_step():
    """Test T5810 step by step to see where it goes wrong"""
    print("🔍 Testing T5810 Step by Step")
    print("=" * 60)
    
    t5810_ollama = "http://************:11434"
    
    # Create the exact tool
    tools = [{
        'type': 'function',
        'function': {
            'name': 'query_codebase',
            'description': 'Search for specific code patterns or functions in a codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {'type': 'string', 'description': 'Name of the codebase to search in'},
                    'query': {'type': 'string', 'description': 'What to search for'},
                    'max_results': {'type': 'integer', 'description': 'Maximum results', 'default': 10}
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }]
    
    # Test the exact problematic query
    messages = [
        {'role': 'user', 'content': 'show timer management in utils codebase'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print("📤 Step 1: Sending query to T5810...")
    
    try:
        # Step 1: Initial request
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ T5810 responded")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Initial content: {content[:200]}...")
            
            if not tool_calls:
                print(f"❌ T5810 didn't call any tools - this is the problem!")
                return
            
            # Step 2: Execute the tool and see what T5810 gets
            messages.append(message)
            
            for tool_call in tool_calls:
                function = tool_call.get('function', {})
                tool_name = function.get('name', '')
                arguments_raw = function.get('arguments', '{}')
                
                print(f"\n📤 Step 2: Tool call details:")
                print(f"   Tool: {tool_name}")
                print(f"   Arguments: {arguments_raw}")
                
                # Parse arguments (handle both dict and string)
                if isinstance(arguments_raw, dict):
                    arguments = arguments_raw
                else:
                    try:
                        arguments = json.loads(arguments_raw)
                    except:
                        arguments = {}
                
                print(f"   Parsed: {arguments}")
                
                # Execute the tool manually to see what T5810 should get
                if tool_name == 'query_codebase':
                    try:
                        tool_response = requests.post(
                            "http://192.168.0.77:5002/search",
                            json={
                                "query": arguments.get('query', ''),
                                "codebase_name": arguments.get('codebase_name', ''),
                                "n_results": arguments.get('max_results', 10)
                            },
                            timeout=60
                        )
                        
                        if tool_response.status_code == 200:
                            tool_result = tool_response.json()
                            print(f"   ✅ Tool executed successfully")
                            
                            results = tool_result.get('results', [])
                            print(f"   📊 Found {len(results)} results")
                            
                            if results:
                                first_content = results[0].get('content', '')
                                print(f"   📄 First result length: {len(first_content)}")
                                print(f"   📝 First result preview: {first_content[:200]}...")
                                
                                # Check if it contains timer code
                                if any(term in first_content.lower() for term in ['timer', 'tmwtimer', 'timeout']):
                                    print(f"   ✅ Contains real timer code")
                                else:
                                    print(f"   ⚠️ May not contain timer code")
                        else:
                            tool_result = {"error": f"HTTP {tool_response.status_code}"}
                            print(f"   ❌ Tool execution failed")
                            
                    except Exception as e:
                        tool_result = {"error": str(e)}
                        print(f"   ❌ Tool execution exception: {e}")
                else:
                    tool_result = {"error": f"Unknown tool: {tool_name}"}
                
                # Add tool result to messages (this is what T5810 will see)
                tool_result_json = json.dumps(tool_result)
                print(f"\n📥 Tool result JSON length: {len(tool_result_json)}")
                print(f"📥 Tool result preview: {tool_result_json[:300]}...")
                
                messages.append({
                    'role': 'tool',
                    'content': tool_result_json
                })
            
            # Step 3: Get final response from T5810
            print(f"\n📤 Step 3: Getting final response from T5810...")
            
            final_payload = {
                'model': 'llama3.1:latest',
                'messages': messages,
                'stream': False
            }
            
            final_response = requests.post(f"{t5810_ollama}/api/chat", json=final_payload, timeout=120)
            
            if final_response.status_code == 200:
                final_data = final_response.json()
                final_content = final_data.get('message', {}).get('content', '')
                
                print(f"✅ Final response received")
                print(f"📝 Final content length: {len(final_content)}")
                print(f"📊 Final content:")
                print(final_content)
                
                # Analyze the final response
                hallucination_indicators = [
                    "import time",
                    "class Timer:",
                    "def start(self):",
                    "Example usage:",
                    "This code defines"
                ]
                
                real_code_indicators = [
                    "tmwtimer",
                    "Triangle MicroWorks",
                    "_restartSystemTimer",
                    "TMWTIMER"
                ]
                
                found_hallucination = [ind for ind in hallucination_indicators if ind in final_content]
                found_real_code = [ind for ind in real_code_indicators if ind in final_content]
                
                print(f"\n🔍 ANALYSIS:")
                print(f"Hallucination indicators: {found_hallucination}")
                print(f"Real code indicators: {found_real_code}")
                
                if found_real_code and not found_hallucination:
                    print(f"✅ SUCCESS: T5810 used the real tool results!")
                elif found_hallucination:
                    print(f"❌ FAILURE: T5810 ignored real results and hallucinated")
                    print(f"🔍 This means T5810 got real timer code but chose to generate examples instead")
                else:
                    print(f"⚠️ UNCLEAR: Neither clear hallucination nor real code")
            else:
                print(f"❌ Final response error: {final_response.status_code}")
                
        else:
            print(f"❌ Initial response error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    print("🔍 Debugging T5810 Result Processing Issue")
    print("=" * 70)
    print("Goal: Understand why T5810 ignores real timer code and hallucinates")
    
    test_t5810_step_by_step()
    
    print(f"\n💡 HYPOTHESIS:")
    print("T5810 is getting real timer code from the search but is interpreting")
    print("the 'show timer management' query as a request for educational examples")
    print("rather than a request to analyze the real code it found.")

if __name__ == "__main__":
    main()
