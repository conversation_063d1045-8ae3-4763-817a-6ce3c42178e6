#!/usr/bin/env python3
"""
Enhance a single codebase with enhanced features
"""

import requests
import sys

def enhance_single_codebase(codebase_name):
    """Enable enhanced features for a specific codebase"""
    print(f"🔄 Enabling enhanced features for: {codebase_name}")
    print("-" * 50)
    
    try:
        payload = {
            "codebase_name": codebase_name,
            "enable_enhanced_features": True,
            "force_reprocess": True
        }
        
        print("📤 Sending enhancement request...")
        response = requests.post(
            "http://192.168.0.77:5002/tools/process_codebase",
            json=payload,
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"📊 Result: {result}")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python enhance_single_codebase.py <codebase_name>")
        print("\nAvailable codebases:")
        print("• utils")
        print("• go-example-master") 
        print("• rust-starter-master")
        print("• test_project")
        print("• z80emu")
        print("• bookstore")
        print("• vga-to-spi-tft")
        print("• library-management-system")
        print("• modbus")
        print("• TypeScript-Node-Starter-master")
        print("• networking_project")
        return
    
    codebase_name = sys.argv[1]
    enhance_single_codebase(codebase_name)

if __name__ == "__main__":
    main()
