#!/usr/bin/env python3
"""
Re-process codebases to get enhanced features (current system already has them)
"""

import requests
import time

def reprocess_codebase(codebase_name):
    """Re-process a codebase with current enhanced system"""
    print(f"🔄 Re-processing {codebase_name} with enhanced features...")
    
    try:
        payload = {
            "codebase_name": codebase_name,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        }
        
        response = requests.post(
            "http://192.168.0.77:5002/tools/process_codebase",
            json=payload,
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {codebase_name}: Re-processed successfully")
            
            # Check if result indicates enhanced features
            result_text = result.get('result', '')
            if 'Enhanced Multi-Language Processing' in result_text:
                print(f"   ✅ Enhanced features confirmed")
            else:
                print(f"   ⚠️ Check if enhanced features are active")
            
            return True
        else:
            print(f"❌ {codebase_name}: Error {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏳ {codebase_name}: Processing timeout (may still be running)")
        return False
    except Exception as e:
        print(f"❌ {codebase_name}: Error - {e}")
        return False

def check_enhanced_status():
    """Check current enhanced status"""
    try:
        response = requests.get("http://192.168.0.77:5002/health", timeout=30)
        if response.status_code == 200:
            data = response.json()
            enhanced_count = data.get('enhanced_codebases', 0)
            total_count = data.get('available_codebases', 0)
            
            print(f"📊 Current Status: {enhanced_count}/{total_count} codebases with enhanced features")
            return enhanced_count, total_count
        else:
            print(f"Error checking status: {response.status_code}")
            return 0, 0
    except Exception as e:
        print(f"Error: {e}")
        return 0, 0

def get_codebase_list():
    """Get list of codebases that need enhanced features"""
    codebases = [
        "utils",
        "go-example-master", 
        "rust-starter-master",
        "test_project",
        "z80emu",
        "bookstore",
        "vga-to-spi-tft",
        "library-management-system",
        "modbus",
        "TypeScript-Node-Starter-master",
        "networking_project"
    ]
    return codebases

def main():
    print("🚀 Re-processing Codebases for Enhanced Features")
    print("=" * 60)
    print("The current system already includes enhanced features by default.")
    print("This script re-processes codebases to upgrade from v1.0_basic to enhanced metadata.")
    
    # Check current status
    enhanced_before, total_before = check_enhanced_status()
    
    # Get codebases to process
    codebases = get_codebase_list()
    print(f"\n📋 Found {len(codebases)} codebases to re-process:")
    for cb in codebases:
        print(f"   • {cb}")
    
    # Ask for confirmation
    print(f"\n⚠️ This will re-process all codebases and may take 10-30 minutes total.")
    confirm = input("Continue? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ Cancelled")
        return
    
    # Process each codebase
    print(f"\n🔄 Re-processing codebases...")
    successful = 0
    failed = 0
    
    for i, codebase in enumerate(codebases, 1):
        print(f"\n[{i}/{len(codebases)}] Processing {codebase}...")
        
        if reprocess_codebase(codebase):
            successful += 1
        else:
            failed += 1
        
        # Small delay between processing
        if i < len(codebases):
            print("   Waiting 3 seconds before next codebase...")
            time.sleep(3)
    
    # Check final status
    print(f"\n📊 Final Results:")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    
    print(f"\n🔍 Checking final enhanced status...")
    enhanced_after, total_after = check_enhanced_status()
    
    if enhanced_after > enhanced_before:
        print(f"🎉 Success! Enhanced codebases increased from {enhanced_before} to {enhanced_after}")
    else:
        print(f"⚠️ Enhanced count unchanged: {enhanced_after}")
        print("💡 The system may already be using enhanced features by default")
    
    print(f"\n💡 Next steps:")
    print("1. Check codebase status with: list codebases")
    print("2. Look for 'Enhanced Metadata: Yes' in the status")
    print("3. Test enhanced search capabilities")
    print("4. Check the web interface at http://home-ai-server.local:5003")

if __name__ == "__main__":
    main()
