"""
Framework Integration Module
Integrates all components of the Language-Agnostic Core Framework
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from language_framework import CodeAnalysisFramework
from language_registry import create_language_registry, validate_language_coverage
from processing_pipeline import ProcessingPipeline, ProcessingStage, StageResult, StageStatus
from chunk_system import ChunkGeneratorRegistry, create_default_chunk_registry
from gpu_infrastructure import BasicGPUManager, BasicProcessingCoordinator, GPUProcessingStage
from vector_db_creator import VectorDBPipelineStage
from tree_sitter_chunker import TreeSitterChunker

logger = logging.getLogger(__name__)

class CodeAnalysisStage(ProcessingStage):
    """Stage for basic code analysis using language processors"""
    
    def __init__(self, framework: CodeAnalysisFramework):
        self.framework = framework
    
    def get_stage_name(self) -> str:
        return "code_analysis"
    
    def get_dependencies(self) -> List[str]:
        return []  # No dependencies - this is the first stage
    
    def get_stage_description(self) -> str:
        return "Analyze code files using language-specific processors"
    
    def get_required_input_keys(self) -> List[str]:
        return ["file_paths"]
    
    async def process(self, input_data: Any, context: Dict[str, Any]) -> Any:
        """Process code files and extract language-specific contexts"""
        # Extract file paths from pipeline input structure
        if isinstance(input_data, dict) and "input" in input_data:
            # First stage - get from pipeline input
            file_paths = input_data["input"].get("file_paths", [])
        elif isinstance(input_data, dict) and "file_paths" in input_data:
            # Direct input
            file_paths = input_data["file_paths"]
        else:
            file_paths = []

        if not file_paths:
            raise ValueError("No file paths provided for code analysis")
        
        # Analyze file relationships
        relationships = self.framework.analyze_file_relationships(file_paths)
        
        # Extract contexts
        contexts = self.framework.extract_contexts(file_paths)
        
        # Generate architectural insights
        insights = {}
        for language in self.framework.get_supported_languages():
            language_contexts = [ctx for ctx in contexts.values() if ctx.language == language]
            if language_contexts:
                processor = self.framework.language_processors[language]
                language_insights = processor.generate_architectural_insights(language_contexts)
                insights[language] = language_insights
        
        return {
            "file_relationships": relationships,
            "language_contexts": contexts,
            "architectural_insights": insights,
            "processed_files": len(file_paths),
            "supported_languages": list(insights.keys())
        }

class ChunkGenerationStage(ProcessingStage):
    """Stage for generating chunks from analyzed code"""
    
    def __init__(self, chunk_registry: ChunkGeneratorRegistry, llm_client: Any):
        self.chunk_registry = chunk_registry
        self.llm_client = llm_client
    
    def get_stage_name(self) -> str:
        return "chunk_generation"
    
    def get_dependencies(self) -> List[str]:
        return ["code_analysis"]
    
    def get_stage_description(self) -> str:
        return "Generate searchable chunks from analyzed code"
    
    async def process(self, input_data: Any, context: Dict[str, Any]) -> Any:
        """Generate chunks from code analysis results with Tree-sitter function-level chunking"""
        code_analysis = input_data["code_analysis"]
        language_contexts = code_analysis["language_contexts"]

        chunks = []

        # Initialize Tree-sitter chunker
        chunker = TreeSitterChunker()
        print("🔍 Using Universal Tree-sitter chunking for ALL languages!")

        # Generate code implementation chunks using Tree-sitter for ALL languages
        for file_path, lang_context in language_contexts.items():
            try:
                # Try Tree-sitter chunking for ALL languages
                language_chunks = chunker.chunk_file(file_path, lang_context.content, lang_context.language)

                for lang_chunk in language_chunks:
                    chunk_context = {
                        "code_content": lang_chunk['content'],
                        "file_path": lang_chunk['file_path'],
                        "language": lang_context.language,
                        "start_line": lang_chunk['start_line'],
                        "end_line": lang_chunk['end_line'],
                        "function_name": lang_chunk['function_name'],
                        "chunk_type": lang_chunk['chunk_type']
                    }

                    chunk = await self.chunk_registry.generate_chunk(
                        "code_implementation", chunk_context, self.llm_client
                    )
                    chunks.append(chunk)

                print(f"🔍 Tree-sitter: Created {len(language_chunks)} {lang_context.language} chunks for {file_path}")

            except Exception as e:
                logger.error(f"Failed to generate Tree-sitter chunks for {file_path}: {e}")
                # Fallback to original file-level method
                chunk_context = {
                    "code_content": lang_context.content,
                    "file_path": lang_context.file_path,
                    "language": lang_context.language
                }
                try:
                    chunk = await self.chunk_registry.generate_chunk(
                        "code_implementation", chunk_context, self.llm_client
                    )
                    chunks.append(chunk)
                    print(f"⚠️ Fallback: Created 1 file-level chunk for {file_path}")
                except Exception as e2:
                    logger.error(f"Fallback chunk generation failed for {file_path}: {e2}")
        
        # Generate architectural chunks
        architectural_insights = code_analysis["architectural_insights"]
        for language, insights in architectural_insights.items():
            if insights:
                chunk_context = {
                    "architectural_analysis": insights,
                    "source_files": [ctx.file_path for ctx in language_contexts.values() if ctx.language == language]
                }
                
                try:
                    chunk = await self.chunk_registry.generate_chunk(
                        "architectural_pattern", chunk_context, self.llm_client
                    )
                    chunks.append(chunk)
                except Exception as e:
                    logger.error(f"Failed to generate architectural chunk for {language}: {e}")
        
        return {
            "generated_chunks": [chunk.to_dict() for chunk in chunks],  # Convert Chunk objects to dicts for JSON serialization
            "chunk_count": len(chunks),
            "chunk_types": list(set(chunk.metadata.chunk_type for chunk in chunks))
        }

class IntegratedCodeAnalysisSystem:
    """Integrated system combining all framework components"""
    
    def __init__(self, llm_client: Any = None):
        self.framework = create_language_registry()
        self.chunk_registry = create_default_chunk_registry()
        self.pipeline = ProcessingPipeline("integrated_analysis")
        self.llm_client = llm_client or self._create_mock_llm_client()

        # Initialize GPU infrastructure
        self.gpu_manager = BasicGPUManager()
        self.processing_coordinator = BasicProcessingCoordinator()
        self.gpu_stage = GPUProcessingStage(self.processing_coordinator)

        # Register processing stages
        self._setup_pipeline()

        logger.info("Initialized integrated code analysis system with GPU infrastructure")
    
    def _create_mock_llm_client(self):
        """Create a mock LLM client for testing"""
        class MockLLMClient:
            async def generate(self, prompt):
                return f"Mock analysis for prompt: {prompt[:100]}..."
        
        return MockLLMClient()
    
    def _setup_pipeline(self):
        """Setup the processing pipeline with stages"""
        # Register core stages
        code_analysis_stage = CodeAnalysisStage(self.framework)
        chunk_generation_stage = ChunkGenerationStage(self.chunk_registry, self.llm_client)
        vector_db_stage = VectorDBPipelineStage()

        self.pipeline.register_stage(code_analysis_stage)
        self.pipeline.register_stage(chunk_generation_stage)
        self.pipeline.register_stage(vector_db_stage)
    
    async def analyze_codebase(self, codebase_path: str, file_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Analyze an entire codebase"""
        if file_patterns is None:
            file_patterns = ["**/*"]
        
        # Discover files
        file_paths = self._discover_files(codebase_path, file_patterns)
        
        if not file_paths:
            raise ValueError(f"No supported files found in {codebase_path}")
        
        logger.info(f"Analyzing {len(file_paths)} files from {codebase_path}")
        
        # Execute pipeline
        input_data = {"file_paths": file_paths}
        results = await self.pipeline.execute_pipeline(input_data)
        
        # Compile final results
        analysis_result = {
            "codebase_path": codebase_path,
            "total_files": len(file_paths),
            "pipeline_results": results,
            "success": all(result.status == StageStatus.COMPLETED for result in results.values()),
            "processing_summary": self._create_processing_summary(results)
        }
        
        return analysis_result
    
    def _discover_files(self, codebase_path: str, patterns: List[str]) -> List[str]:
        """Discover files matching patterns in codebase"""
        codebase_dir = Path(codebase_path)
        supported_extensions = self.framework.get_supported_extensions()

        discovered_files = []

        logger.debug(f"Supported extensions: {sorted(supported_extensions)}")

        for pattern in patterns:
            for file_path in codebase_dir.glob(pattern):
                if file_path.is_file():
                    file_ext = file_path.suffix.lower()
                    logger.debug(f"Checking file: {file_path}, extension: {file_ext}")
                    if file_ext in supported_extensions:
                        discovered_files.append(str(file_path))
                        logger.debug(f"Added file: {file_path}")
                    else:
                        logger.debug(f"Skipped file: {file_path} (extension {file_ext} not supported)")

        logger.info(f"Discovered {len(discovered_files)} supported files")
        return discovered_files
    
    def _create_processing_summary(self, results: Dict[str, StageResult]) -> Dict[str, Any]:
        """Create summary of processing results"""
        summary: Dict[str, Any] = {
            "stages_completed": sum(1 for result in results.values() if result.status == StageStatus.COMPLETED),
            "stages_failed": sum(1 for result in results.values() if result.status == StageStatus.FAILED),
            "total_processing_time": sum(result.execution_time for result in results.values()),
            "stage_details": {}
        }
        
        for stage_name, result in results.items():
            summary["stage_details"][stage_name] = {
                "status": result.status.value,
                "execution_time": result.execution_time,
                "error_message": result.error_message
            }
        
        return summary
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about the integrated system"""
        return {
            "framework_info": {
                "supported_languages": self.framework.get_supported_languages(),
                "supported_extensions": list(self.framework.get_supported_extensions()),
                "total_processors": len(self.framework.language_processors)
            },
            "chunk_registry_info": self.chunk_registry.get_registry_info(),
            "pipeline_info": self.pipeline.get_pipeline_info(),
            "system_status": "ready"
        }
    
    def validate_system(self) -> Dict[str, Any]:
        """Validate the entire system configuration"""
        validation_results = {
            "language_coverage": validate_language_coverage(),
            "pipeline_validation": self.pipeline.validate_pipeline(),
            "chunk_registry_status": {
                "registered_types": len(self.chunk_registry.generators),
                "available_types": self.chunk_registry.get_registered_types()
            }
        }
        
        # Overall system validation
        validation_results["system_valid"] = (
            validation_results["language_coverage"]["coverage_complete"] and
            validation_results["pipeline_validation"]["valid"] and
            validation_results["chunk_registry_status"]["registered_types"] > 0
        )
        
        return validation_results

    async def get_gpu_status(self) -> Dict[str, Any]:
        """Get current GPU infrastructure status"""
        try:
            available_gpus = await self.gpu_manager.discover_available_gpus()
            supported_types = self.gpu_manager.get_supported_gpu_types()

            return {
                "gpu_infrastructure_available": True,
                "available_gpus": len(available_gpus),
                "supported_gpu_types": len(supported_types),
                "gpu_details": available_gpus,
                "supported_types": supported_types
            }
        except Exception as e:
            return {
                "gpu_infrastructure_available": False,
                "error": str(e),
                "fallback_mode": True
            }

    async def get_processing_recommendations(self, chunk_count: int) -> Dict[str, Any]:
        """Get GPU processing recommendations for workload"""
        try:
            recommendations = await self.processing_coordinator.get_processing_recommendations(chunk_count)
            return {
                "recommendations_available": True,
                "recommendations": recommendations
            }
        except Exception as e:
            return {
                "recommendations_available": False,
                "error": str(e),
                "fallback_to_local": True
            }

    async def create_vector_database(self, codebase_path: str, collection_name: Optional[str] = None,
                                   file_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create a vector database from a codebase using the full framework pipeline"""
        try:
            logger.info(f"Creating vector database for codebase: {codebase_path}")

            # Set collection name
            if collection_name is None:
                collection_name = Path(codebase_path).name

            # Analyze the codebase first
            analysis_result = await self.analyze_codebase(codebase_path, file_patterns)

            if not analysis_result.get("success"):
                return {
                    "success": False,
                    "error": f"Codebase analysis failed: {analysis_result.get('error', 'Unknown error')}",
                    "collection_name": collection_name
                }

            # Extract chunks from analysis
            chunks = analysis_result.get("chunks", [])
            if not chunks:
                return {
                    "success": False,
                    "error": "No chunks generated from codebase analysis",
                    "collection_name": collection_name
                }

            # Create vector database using VectorDB pipeline stage
            vector_db_stage = VectorDBPipelineStage(collection_name=collection_name)

            # Initialize the stage with framework components
            context = {
                "language_registry": self.framework,
                "chunk_registry": self.chunk_registry,
                "gpu_manager": self.gpu_manager,
                "framework_version": "language_agnostic_v1.0"
            }

            await vector_db_stage.initialize(context)

            # Process chunks to create vector database
            vector_result = await vector_db_stage.process({"chunks": chunks, "codebase_name": collection_name}, context)

            if vector_result.get("success"):
                return {
                    "success": True,
                    "collection_name": collection_name,
                    "collection_info": vector_result.get("collection_info", {}),
                    "chunks_processed": vector_result.get("chunks_processed", 0),
                    "processing_time": vector_result.get("processing_time", 0),
                    "framework_version": "language_agnostic_v1.0",
                    "gpu_distributed": vector_result.get("collection_info", {}).get("processing_mode") == "gpu_distributed"
                }
            else:
                return {
                    "success": False,
                    "error": vector_result.get("error", "Vector database creation failed"),
                    "collection_name": collection_name
                }

        except Exception as e:
            logger.error(f"Vector database creation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "collection_name": collection_name
            }

    async def process_query(self, query: str, codebase_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process query using integrated framework with query intelligence"""
        try:
            # Use query intelligence to classify and route the query
            from language_framework import QueryIntelligenceEngine
            query_engine = QueryIntelligenceEngine()

            query_classification = query_engine.classify_query(query)
            routing_info = self.framework.route_query(query)

            # Get GPU status and recommendations
            gpu_status = await self.get_gpu_status()

            # Extract codebase information
            codebase_name = codebase_context.get("codebase_name", "") if codebase_context else ""
            n_results = codebase_context.get("n_results", 10) if codebase_context else 10

            # Prepare GPU processing if available
            gpu_preparation = await self.gpu_stage.prepare_gpu_processing(n_results)

            # Generate sample chunks using the chunk system for demonstration
            sample_chunks = []
            try:
                # Create a sample chunk for demonstration
                from chunk_system import Chunk, ChunkContent, ChunkMetadata, ChunkPriority
                from datetime import datetime
                import uuid

                chunk_content = ChunkContent(
                    primary_content=f"# Sample code context for query: {query}\n# This demonstrates framework integration",
                    summary="Framework integration demonstration",
                    keywords=["framework", "integration", "sample"]
                )

                chunk_metadata = ChunkMetadata(
                    chunk_id=str(uuid.uuid4()),
                    chunk_type="code_implementation",
                    source_files=[f"sample/{codebase_name}.py"],
                    language="python",
                    created_at=datetime.now(),
                    priority=ChunkPriority.MEDIUM,
                    tags={"framework", "sample", "integration"},
                    relationships={},
                    quality_score=0.95,
                    processing_time=0.001,
                    custom_attributes={
                        "query_relevance": 0.95,
                        "framework_processed": True,
                        "start_line": 1,
                        "end_line": 2
                    }
                )

                sample_chunk = Chunk(content=chunk_content, metadata=chunk_metadata)
                sample_chunks.append(sample_chunk)
            except Exception as chunk_error:
                logger.warning(f"Chunk generation failed: {chunk_error}")

            return {
                "success": True,
                "query": query,
                "query_classification": query_classification,
                "routing_info": routing_info,
                "processing_strategy": routing_info.get("processing_strategy", "intelligent_routing"),
                "recommended_processors": routing_info.get("recommended_processors", []),
                "system_ready": True,
                "gpu_info": gpu_status,
                "gpu_preparation": gpu_preparation,
                "results": {
                    "chunks": [chunk.to_dict() for chunk in sample_chunks],  # Convert Chunk objects to dicts for JSON serialization
                    "metadata": {
                        "codebase_name": codebase_name,
                        "n_results": n_results,
                        "processing_method": "framework_integrated",
                        "chunk_count": len(sample_chunks)
                    }
                },
                "recommendations": await self.get_processing_recommendations(n_results)
            }

        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "processing_strategy": "error_fallback",
                "recommended_processors": [],
                "system_ready": False,
                "fallback_required": True
            }

async def main():
    """Main function to demonstrate the integrated system"""
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Initializing Integrated Code Analysis System...")
    system = IntegratedCodeAnalysisSystem()
    
    print("\n📊 System Information:")
    info = system.get_system_info()
    print(f"Supported Languages: {len(info['framework_info']['supported_languages'])}")
    print(f"Supported Extensions: {len(info['framework_info']['supported_extensions'])}")
    print(f"Registered Chunk Types: {len(info['chunk_registry_info']['registered_types'])}")
    print(f"Pipeline Stages: {info['pipeline_info']['total_stages']}")
    
    print("\n✅ Validating System...")
    validation = system.validate_system()
    print(f"System Valid: {validation['system_valid']}")
    print(f"Language Coverage Complete: {validation['language_coverage']['coverage_complete']}")
    print(f"Pipeline Valid: {validation['pipeline_validation']['valid']}")
    
    if validation['language_coverage']['missing_languages']:
        print(f"Missing Languages: {validation['language_coverage']['missing_languages']}")
    
    print("\n🔍 Testing Query Processing...")
    test_queries = [
        "How does the authentication system work?",
        "Find all Python functions that handle errors",
        "What design patterns are used in this codebase?",
        "Show me the C++ memory management code"
    ]
    
    for query in test_queries:
        result = await system.process_query(query)
        print(f"Query: {query}")
        print(f"  Strategy: {result['processing_strategy']}")
        print(f"  Recommended: {result['recommended_processors'][:3]}...")  # Show first 3
        print()
    
    print("✨ System initialization and validation complete!")

if __name__ == "__main__":
    asyncio.run(main())
