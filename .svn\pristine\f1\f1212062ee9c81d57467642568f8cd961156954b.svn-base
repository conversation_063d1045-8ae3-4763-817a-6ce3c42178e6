"""
Language-Agnostic Core Framework with Plugin Architecture
Implements Phase 0, Section 1 from TODO_SOFTWARE.md

This module provides the foundational architecture for supporting 27+ programming languages
through a modular plugin system.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Set
import logging
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class AnalysisScope(Enum):
    """Defines the scope of analysis for different language contexts"""
    STANDALONE = "standalone"
    HEADER_IMPLEMENTATION_PAIR = "header_implementation_pair"
    MODULE_GROUP = "module_group"
    PACKAGE_HIERARCHY = "package_hierarchy"
    CROSS_LANGUAGE = "cross_language"

@dataclass
class FileRelationship:
    """Represents relationships between files in a codebase"""
    file_path: str
    relationship_type: str
    related_files: List[str]
    analysis_scope: AnalysisScope
    metadata: Dict[str, Any]

@dataclass
class LanguageContext:
    """Context information for language-specific processing"""
    language: str
    file_path: str
    content: str
    relationships: List[FileRelationship]
    metadata: Dict[str, Any]

class LanguageProcessor(ABC):
    """Abstract base class for language-specific processors"""
    
    @abstractmethod
    def get_language_name(self) -> str:
        """Return the name of the programming language this processor handles"""
        pass
    
    @abstractmethod
    def get_supported_extensions(self) -> Set[str]:
        """Return file extensions supported by this processor"""
        pass
    
    @abstractmethod
    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Detect language-specific file relationships"""
        pass
    
    @abstractmethod
    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract language-specific context from file content"""
        pass
    
    @abstractmethod
    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate language-specific architectural insights"""
        pass
    
    @abstractmethod
    def get_processing_priority(self) -> int:
        """Return processing priority (lower numbers = higher priority)"""
        pass
    
    def can_process_file(self, file_path: str) -> bool:
        """Check if this processor can handle the given file"""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in self.get_supported_extensions()

class QueryIntelligenceEngine:
    """Intelligent query classification and routing system"""
    
    def __init__(self):
        self.query_classifiers: Dict[str, Any] = {}
        self.routing_strategies: Dict[str, Any] = {}
        
    def classify_query(self, query: str) -> Dict[str, Any]:
        """Classify query type and determine processing strategy"""
        classification = {
            "query_type": self._determine_query_type(query),
            "complexity_level": self._assess_complexity(query),
            "target_scope": self._determine_scope(query),
            "required_chunk_types": self._identify_chunk_types(query),
            "language_hints": self._extract_language_hints(query)
        }
        return classification
    
    def _determine_query_type(self, query: str) -> str:
        """Determine if query is code-level, architectural, or system-level"""
        query_lower = query.lower()
        
        # Code-level indicators
        if any(keyword in query_lower for keyword in ['function', 'method', 'variable', 'class']):
            return "code_level"
        
        # System-level indicators
        if any(keyword in query_lower for keyword in ['system', 'architecture', 'design', 'overall']):
            return "system_level"
        
        # Architectural indicators
        if any(keyword in query_lower for keyword in ['pattern', 'structure', 'component', 'module']):
            return "architectural_level"
        
        return "general"
    
    def _assess_complexity(self, query: str) -> str:
        """Assess query complexity"""
        word_count = len(query.split())
        if word_count <= 3:
            return "simple"
        elif word_count <= 10:
            return "moderate"
        else:
            return "complex"
    
    def _determine_scope(self, query: str) -> str:
        """Determine the scope of the query"""
        query_lower = query.lower()
        
        if any(keyword in query_lower for keyword in ['across', 'between', 'all']):
            return "cross_cutting"
        elif any(keyword in query_lower for keyword in ['specific', 'this', 'particular']):
            return "targeted"
        else:
            return "general"
    
    def _identify_chunk_types(self, query: str) -> List[str]:
        """Identify what types of chunks would be relevant"""
        chunk_types = []
        query_lower = query.lower()
        
        if any(keyword in query_lower for keyword in ['implement', 'code', 'function']):
            chunk_types.append("code_implementation")
        
        if any(keyword in query_lower for keyword in ['architecture', 'design', 'pattern']):
            chunk_types.append("architectural_pattern")
        
        if any(keyword in query_lower for keyword in ['system', 'overall', 'purpose']):
            chunk_types.append("system_design")
        
        return chunk_types if chunk_types else ["code_implementation"]
    
    def _extract_language_hints(self, query: str) -> List[str]:
        """Extract programming language hints from query"""
        language_keywords = {
            'python': ['python', 'py', 'django', 'flask', 'pandas'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue'],
            'java': ['java', 'spring', 'maven', 'gradle'],
            'cpp': ['c++', 'cpp', 'cxx', 'stl'],
            'c': ['c language', 'malloc', 'stdio'],
            'csharp': ['c#', 'csharp', '.net', 'dotnet'],
            'go': ['golang', 'go lang'],
            'rust': ['rust', 'cargo'],
            'typescript': ['typescript', 'ts'],
            'sql': ['sql', 'database', 'query']
        }
        
        query_lower = query.lower()
        detected_languages = []
        
        for lang, keywords in language_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                detected_languages.append(lang)
        
        return detected_languages

class CodeAnalysisFramework:
    """Core framework supporting pluggable language processors"""
    
    def __init__(self):
        self.language_processors: Dict[str, LanguageProcessor] = {}
        self.analysis_stages: List[Any] = []
        self.chunk_generators: Dict[str, Any] = {}
        self.query_router = QueryIntelligenceEngine()
        self._extension_to_processor: Dict[str, str] = {}
        
    def register_language_processor(self, processor: LanguageProcessor) -> None:
        """Register language-specific processor plugin"""
        language = processor.get_language_name()
        self.language_processors[language] = processor
        
        # Build extension mapping for fast lookup
        for ext in processor.get_supported_extensions():
            self._extension_to_processor[ext] = language
        
        logger.info(f"Registered language processor for {language}")
    
    def register_analysis_stage(self, stage: Any) -> None:
        """Register configurable analysis stage"""
        self.analysis_stages.append(stage)
        logger.info(f"Registered analysis stage: {stage.__class__.__name__}")
    
    def register_chunk_generator(self, chunk_type: str, generator: Any) -> None:
        """Register extensible chunk type generator"""
        self.chunk_generators[chunk_type] = generator
        logger.info(f"Registered chunk generator for type: {chunk_type}")
    
    def get_processor_for_file(self, file_path: str) -> Optional[LanguageProcessor]:
        """Get the appropriate processor for a file"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in self._extension_to_processor:
            language = self._extension_to_processor[file_ext]
            return self.language_processors[language]
        
        # Fallback: check all processors
        for processor in self.language_processors.values():
            if processor.can_process_file(file_path):
                return processor
        
        return None
    
    def get_supported_languages(self) -> List[str]:
        """Get list of all supported languages"""
        return list(self.language_processors.keys())
    
    def get_supported_extensions(self) -> Set[str]:
        """Get all supported file extensions"""
        extensions = set()
        for processor in self.language_processors.values():
            extensions.update(processor.get_supported_extensions())
        return extensions
    
    def analyze_file_relationships(self, file_paths: List[str]) -> Dict[str, List[FileRelationship]]:
        """Analyze relationships between files using appropriate processors"""
        relationships = {}
        
        for file_path in file_paths:
            processor = self.get_processor_for_file(file_path)
            if processor:
                try:
                    file_relationships = processor.detect_file_relationships(file_path)
                    relationships[file_path] = file_relationships
                except Exception as e:
                    logger.error(f"Error analyzing relationships for {file_path}: {e}")
                    relationships[file_path] = []
            else:
                logger.warning(f"No processor found for file: {file_path}")
                relationships[file_path] = []
        
        return relationships
    
    def extract_contexts(self, file_paths: List[str]) -> Dict[str, LanguageContext]:
        """Extract language-specific contexts from files"""
        contexts = {}
        
        for file_path in file_paths:
            processor = self.get_processor_for_file(file_path)
            if processor:
                try:
                    # Read file content
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # Extract context using processor
                    context = processor.extract_context(file_path, content)
                    contexts[file_path] = context
                except Exception as e:
                    logger.error(f"Error extracting context for {file_path}: {e}")
            else:
                logger.warning(f"No processor found for file: {file_path}")
        
        return contexts
    
    def route_query(self, query: str) -> Dict[str, Any]:
        """Route query to appropriate processing strategy"""
        classification = self.query_router.classify_query(query)
        
        # Add framework-specific routing logic here
        routing_info = {
            "classification": classification,
            "recommended_processors": self._get_recommended_processors(classification),
            "processing_strategy": self._determine_processing_strategy(classification)
        }
        
        return routing_info
    
    def _get_recommended_processors(self, classification: Dict[str, Any]) -> List[str]:
        """Get recommended processors based on query classification"""
        language_hints = classification.get("language_hints", [])
        
        if language_hints:
            # Return processors for hinted languages
            return [lang for lang in language_hints if lang in self.language_processors]
        else:
            # Return all processors for broad queries
            return list(self.language_processors.keys())
    
    def _determine_processing_strategy(self, classification: Dict[str, Any]) -> str:
        """Determine the processing strategy based on classification"""
        query_type = classification.get("query_type", "general")
        complexity = classification.get("complexity_level", "moderate")
        
        if query_type == "system_level":
            return "system_wide_analysis"
        elif query_type == "architectural_level":
            return "architectural_analysis"
        elif complexity == "complex":
            return "comprehensive_analysis"
        else:
            return "standard_analysis"
