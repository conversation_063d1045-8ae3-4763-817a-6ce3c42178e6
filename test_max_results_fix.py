#!/usr/bin/env python3
"""
Test the max_results fix for T5810 hallucination
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_max_results_fix():
    """Test that the max_results fix prevents T5810 from requesting 0 results"""
    print("🔧 Testing max_results Fix")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test the exact query that was causing hallucination
    query = "show timer management in utils codebase"
    
    print(f"📤 Query: {query}")
    print("-" * 50)
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📝 Result length: {len(result)}")
    print(f"📊 Result preview:")
    print(result[:500] + "..." if len(result) > 500 else result)
    
    # Check for hallucination vs real code
    hallucination_indicators = [
        "import time",
        "class Timer:",
        "def start(self):",
        "Example usage:",
        "This code defines",
        "Here is an example"
    ]
    
    real_code_indicators = [
        "tmwtimer",
        "Triangle MicroWorks",
        "/app/source_code/utils",
        "_restartSystemTimer",
        "TMWTIMER"
    ]
    
    found_hallucination = [ind for ind in hallucination_indicators if ind in result]
    found_real_code = [ind for ind in real_code_indicators if ind in result]
    
    print(f"\n🔍 Analysis:")
    print(f"Hallucination indicators: {found_hallucination}")
    print(f"Real code indicators: {found_real_code}")
    
    if found_real_code and not found_hallucination:
        print(f"✅ SUCCESS: Shows real timer code!")
        return True
    elif not found_hallucination and not found_real_code:
        print(f"⚪ NEUTRAL: No hallucination but no clear real code either")
        return True
    else:
        print(f"❌ FAILURE: Still hallucinating")
        return False

def test_direct_tool_execution():
    """Test direct tool execution to ensure it gets results"""
    print(f"\n🔧 Testing Direct Tool Execution")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test with different max_results values
    test_cases = [
        {'max_results': 0},  # Should be converted to 1
        {'max_results': 5},  # Should stay 5
        {},  # Should default to 10
    ]
    
    for i, arguments in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing with arguments: {arguments}")
        
        full_args = {
            'codebase_name': 'utils',
            'query': 'timer management',
            **arguments
        }
        
        result = analyzer.execute_tool('query_codebase', full_args)
        
        if 'results' in result:
            results = result['results']
            print(f"   ✅ Found {len(results)} results")
            
            if results:
                first_content = results[0].get('content', '')[:100]
                print(f"   📝 First result: {first_content}...")
            else:
                print(f"   ⚠️ Empty results list")
        else:
            print(f"   ❌ No results key: {result}")

def test_multiple_show_queries():
    """Test multiple show queries to ensure they all work"""
    print(f"\n🧪 Testing Multiple Show Queries")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    queries = [
        "show timer management in utils codebase",
        "show memory management in utils codebase",
        "show error handling in utils codebase"
    ]
    
    results = []
    
    for query in queries:
        print(f"\n📤 Query: {query}")
        
        try:
            result = analyzer.chat_with_tools(query)
            
            # Quick check for hallucination
            if any(term in result for term in ["import ", "class ", "def ", "Example usage:"]):
                print(f"❌ HALLUCINATING")
                results.append(False)
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code", "tmw"]):
                print(f"✅ REAL CODE")
                results.append(True)
            else:
                print(f"⚪ UNCLEAR")
                results.append(None)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append(False)
    
    success_rate = sum(1 for r in results if r is True) / len(results)
    print(f"\n📊 Success rate: {success_rate:.1%}")
    
    return success_rate > 0.5

def main():
    print("🔧 Testing max_results Fix for T5810 Hallucination")
    print("=" * 70)
    
    # Test the specific fix
    main_fix_works = test_max_results_fix()
    
    # Test direct tool execution
    test_direct_tool_execution()
    
    # Test multiple queries
    multiple_works = test_multiple_show_queries()
    
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 70)
    print(f"Main fix works:     {'✅ YES' if main_fix_works else '❌ NO'}")
    print(f"Multiple queries:   {'✅ YES' if multiple_works else '❌ NO'}")
    
    if main_fix_works and multiple_works:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"✅ T5810 no longer hallucinates on 'show' queries")
        print(f"✅ The max_results=0 issue has been fixed")
        print(f"✅ T5810 now shows real timer management code")
    elif main_fix_works:
        print(f"\n⚠️ PARTIAL SUCCESS")
        print(f"The main query works but some edge cases may remain")
    else:
        print(f"\n❌ STILL ISSUES")
        print(f"The fix didn't resolve the hallucination problem")

if __name__ == "__main__":
    main()
