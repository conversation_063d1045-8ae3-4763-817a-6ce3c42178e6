#!/usr/bin/env python3
"""
Test the fixed DirectOllamaCodeAnalyzer
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_fixed_analyzer():
    """Test the fixed analyzer"""
    print("🔧 Testing Fixed DirectOllamaCodeAnalyzer")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Test the specific memory management query
    query = "Find memory management functions in the utils codebase. Use the query_codebase tool to search for malloc, free, and alloc functions."
    
    print(f"📤 Query: {query}")
    
    try:
        result = analyzer.chat_with_tools(query)
        
        print(f"📝 Result length: {len(result)}")
        print(f"📊 Result:")
        print(result)
        
        # Check for real code indicators
        real_indicators = [
            'tmwmem',
            'Triangle MicroWorks',
            '/app/source_code/utils',
            'TMWMEM_ALLOC',
            'TMWMEM_FREE',
            'Copyright'
        ]
        
        found_indicators = [ind for ind in real_indicators if ind in result]
        
        print(f"\n🔍 Real code indicators found: {found_indicators}")
        
        if found_indicators:
            print(f"✅ SUCCESS: T5810 is now showing real code!")
            return True
        else:
            print(f"❌ Still not showing real code")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_simple_memory_query():
    """Test with a simple memory query"""
    print(f"\n🧪 Testing Simple Memory Query")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    query = "Show me memory management functions from utils codebase"
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result: {result}")
    
    # Check if it contains actual function names or file paths
    if any(term in result for term in ['tmwmem', 'malloc', 'TMWMEM']):
        print(f"✅ Contains real memory management content")
        return True
    else:
        print(f"❌ Still generic content")
        return False

def main():
    print("🔧 Testing Fixed T5810 Memory Management Query")
    print("=" * 70)
    
    # Test the fix
    fixed_works = test_fixed_analyzer()
    simple_works = test_simple_memory_query()
    
    print(f"\n🎯 RESULTS")
    print("=" * 70)
    print(f"Fixed analyzer:    {'✅ Working' if fixed_works else '❌ Not working'}")
    print(f"Simple query:      {'✅ Working' if simple_works else '❌ Not working'}")
    
    if fixed_works or simple_works:
        print(f"\n🎉 SUCCESS: The fix worked!")
        print(f"T5810 should now show real memory management functions from utils codebase")
    else:
        print(f"\n❌ The fix didn't work")
        print(f"Need to investigate further")

if __name__ == "__main__":
    main()
