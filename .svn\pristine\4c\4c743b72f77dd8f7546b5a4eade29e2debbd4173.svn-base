# Adding New Languages to the Framework

## 🎯 Overview

The Language-Agnostic Framework supports three levels of language integration:

1. **🚀 Quick Addition** - Basic support with generic processor (5 minutes)
2. **⚡ Enhanced Addition** - Custom semantic patterns (15 minutes)  
3. **🔬 Specialized Processor** - Full custom analysis (1-2 hours)

## 📋 Prerequisites

- Basic understanding of the target language syntax
- Knowledge of common language constructs (functions, classes, imports, etc.)
- Access to language documentation for pattern creation

## 🚀 Option 1: Quick Addition (Generic Processor)

**Best for:** Getting basic language support quickly

### Step 1: Add Language Configuration

Edit `language_registry.py`:

```python
language_configs = [
    # ... existing languages ...
    
    # Your new languages
    ("metta", {".metta", ".mta"}, 3),           # OpenCog Hyperon Metta
    ("prolog", {".pl", ".pro", ".prolog"}, 3), # Prolog
    ("erlang", {".erl", ".hrl"}, 3),           # Erlang  
    ("elixir", {".ex", ".exs"}, 3),            # Elixir
    ("crystal", {".cr"}, 3),                   # Crystal
    ("nim", {".nim"}, 3),                      # Nim
    ("zig", {".zig"}, 3),                      # Zig
]
```

### Step 2: Test Basic Support

```python
from language_registry import create_language_registry

framework = create_language_registry()
processor = framework.get_processor_for_file("test.metta")
print(f"Language: {processor.get_language_name()}")  # Output: metta
```

**✅ Result:** Language is now supported with basic file detection and processing.

## ⚡ Option 2: Enhanced Addition (Custom Semantic Patterns)

**Best for:** Languages you want comprehensive analysis for

### Step 1: Add Language Configuration (same as Option 1)

### Step 2: Add Semantic Patterns

Edit `semantic_patterns.py`:

```python
'function_patterns': {
    # ... existing patterns ...
    
    'metta': [
        r'(?:^|\n)\s*\(\s*=\s+(\w+)',           # (= function_name ...)
        r'(?:^|\n)\s*\(\s*:\s+(\w+)',           # (: function_name ...)
        r'(?:^|\n)\s*\(\s*lambda\s+\(\s*(\w+)', # (lambda (param) ...)
        r'(?:^|\n)\s*\(\s*define\s+(\w+)',      # (define name ...)
    ],
    'prolog': [
        r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*:-',   # predicate(args) :-
        r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*\.',   # predicate(args).
        r'(?:^|\n)\s*(\w+)\s*:-',               # predicate :-
        r'(?:^|\n)\s*(\w+)\s*\.',               # predicate.
    ],
},

'class_patterns': {
    # ... existing patterns ...
    
    'metta': [
        r'(?:^|\n)\s*\(\s*:\s+(\w+)\s+Type\)',  # (: TypeName Type)
        r'(?:^|\n)\s*\(\s*:\s+(\w+)\s+Atom\)',  # (: AtomName Atom)
    ],
    'prolog': [
        r'(?:^|\n)\s*:-\s*module\s*\(\s*(\w+)', # :- module(name, [...]).
        r'(?:^|\n)\s*:-\s*dynamic\s+(\w+)',     # :- dynamic predicate/arity.
    ],
},

'import_patterns': {
    # ... existing patterns ...
    
    'metta': [
        r'(?:^|\n)\s*\(\s*import!\s+(\w+)',     # (import! module)
        r'(?:^|\n)\s*\(\s*include\s+(\w+)',     # (include file)
    ],
    'prolog': [
        r'(?:^|\n)\s*:-\s*use_module\s*\(\s*(\w+)', # :- use_module(name).
        r'(?:^|\n)\s*:-\s*include\s*\(\s*(\w+)',    # :- include(file).
    ],
},
```

### Step 3: Add Language-Specific Features

Edit `language_registry.py` in the `_identify_language_features` method:

```python
elif self.language_name == "metta":
    if "match" in content:
        features.append("pattern_matching")
    if "lambda" in content:
        features.append("functional_programming")
    if "Atom" in content:
        features.append("symbolic_ai")

elif self.language_name == "prolog":
    if ":-" in content:
        features.append("logic_programming")
    if "findall" in content:
        features.append("meta_predicates")
    if "assert" in content:
        features.append("dynamic_predicates")
```

**✅ Result:** Language gets comprehensive semantic analysis with pattern recognition.

## 🔬 Option 3: Specialized Processor (Full Custom Analysis)

**Best for:** Languages requiring advanced domain-specific analysis

### Step 1: Create Specialized Processor

Create `your_language_processor.py`:

```python
from language_framework import LanguageProcessor, FileRelationship, LanguageContext, AnalysisScope
from semantic_patterns import semantic_registry

class YourLanguageProcessor(LanguageProcessor):
    def get_language_name(self) -> str:
        return "your_language"
    
    def get_supported_extensions(self) -> Set[str]:
        return {".ext1", ".ext2"}
    
    def get_processing_priority(self) -> int:
        return 2  # High priority
    
    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        # Custom relationship detection
        pass
    
    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        # Custom context extraction with domain-specific analysis
        semantic_elements = semantic_registry.extract_semantic_elements(content, "your_language")
        
        metadata = {
            "functions": semantic_elements.get("function_patterns", []),
            "classes": semantic_elements.get("class_patterns", []),
            # Add domain-specific metadata
            "custom_constructs": self._extract_custom_constructs(content),
            "domain_complexity": self._calculate_domain_complexity(content),
        }
        
        return LanguageContext(
            language="your_language",
            file_path=file_path,
            content=content,
            relationships=self.detect_file_relationships(file_path),
            metadata=metadata
        )
    
    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        # Custom architectural analysis
        pass
```

### Step 2: Register Specialized Processor

Edit `language_registry.py`:

```python
from your_language_processor import YourLanguageProcessor

def create_language_registry() -> CodeAnalysisFramework:
    framework = CodeAnalysisFramework()

    # Register specialized processors
    framework.register_language_processor(CCppProcessor())
    framework.register_language_processor(PythonProcessor())
    framework.register_language_processor(CSharpProcessor())
    framework.register_language_processor(JavaScriptProcessor())
    framework.register_language_processor(MettaProcessor())        # ✅ Already added
    framework.register_language_processor(YourLanguageProcessor()) # Add here

    # ... rest of the function
```

### Step 3: Remove from Generic Configs (Important!)

Remove the language from generic configs to avoid conflicts:

```python
language_configs = [
    # ... other languages ...
    # ("your_language", {".ext"}, 3),  # ❌ Remove this line
]
```

**✅ Result:** Language gets full custom analysis with domain-specific insights.

## 🧪 Testing New Languages

### Test Basic Functionality

```python
from language_registry import create_language_registry

# Test language detection
framework = create_language_registry()
processor = framework.get_processor_for_file("test.metta")
assert processor.get_language_name() == "metta"

# Test semantic pattern extraction
from semantic_patterns import semantic_registry
elements = semantic_registry.extract_semantic_elements(
    "(= factorial (lambda (n) (if (= n 0) 1 (* n (factorial (- n 1))))))",
    "metta"
)
print(f"Functions found: {elements.get('function_patterns', [])}")
```

### Test with Framework Integration

```python
from code_preprocessor import MultiLanguageCodeProcessor

# Test with actual file processing
processor = MultiLanguageCodeProcessor("/path/to/metta/project")
chunks = processor.process_repository()

# Check for metta files
metta_chunks = [c for c in chunks if c["metadata"]["language"] == "metta"]
print(f"Processed {len(metta_chunks)} Metta chunks")
```

## 📊 Language Support Matrix

| **Language** | **Extensions** | **Support Level** | **Time to Add** |
|-------------|---------------|------------------|-----------------|
| **Metta** | .metta, .mta | ✅ Specialized | ✅ Added |
| **Prolog** | .pl, .pro, .prolog | ✅ Enhanced | ✅ Added |
| **Erlang** | .erl, .hrl | 🔄 Quick | 5 minutes |
| **Elixir** | .ex, .exs | 🔄 Quick | 5 minutes |
| **Crystal** | .cr | 🔄 Quick | 5 minutes |
| **Nim** | .nim | 🔄 Quick | 5 minutes |
| **Zig** | .zig | 🔄 Quick | 5 minutes |
| **COBOL** | .cob, .cbl | 🔄 Quick | 5 minutes |
| **Ada** | .ada, .adb | 🔄 Quick | 5 minutes |

## 🎯 Best Practices

### 1. Start Simple
- Begin with **Quick Addition** to get basic support
- Upgrade to **Enhanced Addition** when you need better analysis
- Create **Specialized Processor** only for complex domain-specific needs

### 2. Pattern Quality
- Test patterns with real code samples
- Include edge cases and variations
- Use non-greedy matching where appropriate

### 3. Performance Considerations
- Keep patterns efficient (avoid catastrophic backtracking)
- Limit complex analysis to specialized processors
- Use caching for expensive operations

### 4. Documentation
- Document language-specific features
- Provide example code snippets
- Include pattern explanations

## 🚀 Quick Start Examples

### Adding Metta (Already Done)
```bash
# Test Metta support
echo '(= hello (lambda (name) (print (concat "Hello " name))))' > test.metta
python -c "
from code_preprocessor import MultiLanguageCodeProcessor
proc = MultiLanguageCodeProcessor('.')
chunks = proc.process_file('test.metta')
print(f'Metta analysis: {chunks[0][\"metadata\"]}')
"
```

### Adding Your Language
```python
# 1. Add to language_configs in language_registry.py
("mylang", {".ml", ".mylang"}, 3),

# 2. Add patterns to semantic_patterns.py
'mylang': [
    r'func\s+(\w+)\s*\(',  # func name(
    r'def\s+(\w+)\s*=',    # def name =
],

# 3. Test
framework = create_language_registry()
assert "mylang" in framework.get_supported_languages()
```

## ✅ Validation Checklist

- [ ] Language added to `language_configs`
- [ ] File extensions properly mapped
- [ ] Semantic patterns added (if using Enhanced)
- [ ] Language-specific features identified
- [ ] Test files processed successfully
- [ ] Patterns match expected constructs
- [ ] No performance regressions
- [ ] Documentation updated

**🎉 Your language is now fully integrated into the framework!**
