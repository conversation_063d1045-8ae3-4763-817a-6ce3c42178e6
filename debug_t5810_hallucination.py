#!/usr/bin/env python3
"""
Debug T5810 hallucination issue with memory management queries
"""

import requests
import json
from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_tool_execution_directly():
    """Test if the code analyzer server tools work correctly"""
    print("🔍 Testing Code Analyzer Server Tools Directly")
    print("=" * 60)
    
    code_analyzer_url = "http://192.168.0.77:5002"
    
    # Test 1: List codebases
    print("\n1️⃣ Testing list_codebases:")
    try:
        response = requests.post(f"{code_analyzer_url}/tools/list_codebases", json={}, timeout=30)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {len(data.get('codebases', []))} codebases found")
            codebases = data.get('codebases', [])
            if 'utils' in codebases:
                print(f"✅ 'utils' codebase is available")
            else:
                print(f"❌ 'utils' codebase NOT found in: {codebases}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Query utils codebase for memory management
    print("\n2️⃣ Testing query_codebase for memory management:")
    try:
        query_payload = {
            "codebase_name": "utils",
            "query": "memory management functions malloc free alloc",
            "max_results": 10
        }
        
        response = requests.post(f"{code_analyzer_url}/tools/query_codebase", 
                               json=query_payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Query successful")
            
            # Check if we got actual results
            results = data.get('results', [])
            print(f"📊 Results count: {len(results)}")
            
            if results:
                print(f"📝 Sample results:")
                for i, result in enumerate(results[:3]):
                    content = result.get('content', 'No content')[:200]
                    metadata = result.get('metadata', {})
                    file_path = metadata.get('file_path', 'Unknown file')
                    print(f"   Result {i+1}: {file_path}")
                    print(f"   Content: {content}...")
                    print()
            else:
                print(f"⚠️ No results returned for memory management query")
                print(f"Raw response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_t5810_direct_tool_calling():
    """Test T5810's direct tool calling step by step"""
    print("\n🧪 Testing T5810 Direct Tool Calling")
    print("=" * 60)
    
    t5810_ollama = "http://************:11434"
    
    # Create the exact tools that T5810 should use
    tools = [{
        'type': 'function',
        'function': {
            'name': 'query_codebase',
            'description': 'Search for specific code patterns or functions in a codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {
                        'type': 'string',
                        'description': 'Name of the codebase to search in',
                    },
                    'query': {
                        'type': 'string',
                        'description': 'What to search for (functions, patterns, etc.)',
                    },
                    'max_results': {
                        'type': 'integer',
                        'description': 'Maximum number of results to return (default: 10)',
                        'default': 10
                    }
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Find memory management functions in the utils codebase. Use the query_codebase tool to search for malloc, free, alloc, and other memory functions.'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print(f"📤 Sending request to T5810...")
    
    try:
        # Step 1: Initial request
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ T5810 responded")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content: {content[:200]}...")
            
            if tool_calls:
                print(f"\n🔧 Tool calls made:")
                for i, tc in enumerate(tool_calls):
                    func = tc.get('function', {})
                    name = func.get('name', 'unknown')
                    args = func.get('arguments', '{}')
                    print(f"   Tool {i+1}: {name}")
                    print(f"   Arguments: {args}")
                    
                    # Parse and execute the tool
                    try:
                        arguments = json.loads(args)
                        print(f"   Parsed args: {arguments}")
                        
                        # Execute the tool manually to see what it returns
                        if name == 'query_codebase':
                            print(f"   🔍 Executing tool manually...")
                            tool_result = execute_query_codebase_tool(arguments)
                            print(f"   📊 Tool result: {str(tool_result)[:300]}...")
                            
                    except Exception as e:
                        print(f"   ❌ Error parsing/executing tool: {e}")
                
                return True
            else:
                print(f"⚠️ T5810 didn't call any tools")
                print(f"This means it's generating responses without using the code analyzer")
                return False
                
        else:
            print(f"❌ T5810 HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ T5810 Exception: {e}")
        return False

def execute_query_codebase_tool(arguments):
    """Execute the query_codebase tool manually"""
    code_analyzer_url = "http://192.168.0.77:5002"
    
    try:
        response = requests.post(f"{code_analyzer_url}/tools/query_codebase", 
                               json=arguments, timeout=60)
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
    except Exception as e:
        return {"error": str(e)}

def test_hybrid_analyzer_on_t5810():
    """Test the hybrid analyzer specifically on T5810"""
    print("\n🚀 Testing Hybrid Analyzer on T5810")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    query = "Find memory management functions in the utils codebase"
    
    print(f"📤 Query: {query}")
    
    try:
        result = analyzer.chat_with_tools(query)
        
        print(f"📥 Result length: {len(result)} characters")
        print(f"📝 Result preview: {result[:500]}...")
        
        # Check for hallucination indicators
        hallucination_indicators = [
            "I don't have access",
            "I cannot access",
            "I'm unable to",
            "I don't have information",
            "I cannot provide",
            "without access to",
            "I would need access"
        ]
        
        is_hallucinating = any(indicator.lower() in result.lower() for indicator in hallucination_indicators)
        
        if is_hallucinating:
            print(f"🚨 HALLUCINATION DETECTED!")
            print(f"T5810 is claiming it can't access the codebase")
        else:
            # Check if it contains actual code or function names
            code_indicators = ["malloc", "free", "alloc", "void", "int", "char", "struct"]
            has_code = any(indicator in result.lower() for indicator in code_indicators)
            
            if has_code:
                print(f"✅ Result appears to contain actual code content")
            else:
                print(f"⚠️ Result doesn't contain expected code content")
        
        return result
        
    except Exception as e:
        print(f"❌ Hybrid analyzer exception: {e}")
        return None

def main():
    print("🔍 Debugging T5810 Hallucination Issue")
    print("=" * 70)
    print("Investigating why T5810 hallucinates on memory management queries")
    
    # Test 1: Direct tool execution
    test_tool_execution_directly()
    
    # Test 2: T5810 tool calling
    tool_calling_works = test_t5810_direct_tool_calling()
    
    # Test 3: Hybrid analyzer
    hybrid_result = test_hybrid_analyzer_on_t5810()
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    
    if not tool_calling_works:
        print("❌ ROOT CAUSE: T5810 is not calling tools properly")
        print("💡 SOLUTION: Fix tool calling mechanism")
    elif hybrid_result and "I don't have access" in hybrid_result:
        print("❌ ROOT CAUSE: T5810 is calling tools but ignoring results")
        print("💡 SOLUTION: Fix tool result integration")
    else:
        print("✅ Tool calling appears to work")
        print("🔍 Need to investigate response generation")

if __name__ == "__main__":
    main()
