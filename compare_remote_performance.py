#!/usr/bin/env python3
"""
Performance comparison between lynn-pc and T5810 for code analysis
"""

import time
import requests
import json
from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def benchmark_server(server_name, server_url, test_queries):
    """Benchmark a specific server with multiple queries"""
    print(f"\n🧪 Benchmarking {server_name}")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer(server_url)
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] Testing: '{query[:50]}...'")
        
        start_time = time.time()
        
        try:
            result = analyzer.chat_with_tools(query)
            end_time = time.time()
            response_time = end_time - start_time
            
            # Analyze response quality
            quality_score = 0
            if len(result) > 100:
                quality_score += 20  # Substantial response
            if any(cb in result.lower() for cb in ["utils", "z80emu", "go-example", "typescript"]):
                quality_score += 30  # Found real codebases
            if "error" not in result.lower():
                quality_score += 25  # No errors
            if len(result) > 300:
                quality_score += 25  # Detailed response
            
            results.append({
                'query': query,
                'response_time': response_time,
                'response_length': len(result),
                'quality_score': quality_score,
                'success': True
            })
            
            print(f"   ⏱️  Time: {response_time:.2f}s")
            print(f"   📝 Length: {len(result)} chars")
            print(f"   ⭐ Quality: {quality_score}/100")
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            results.append({
                'query': query,
                'response_time': response_time,
                'response_length': 0,
                'quality_score': 0,
                'success': False,
                'error': str(e)
            })
            
            print(f"   ❌ Error: {e}")
    
    return results

def analyze_performance(server_name, results):
    """Analyze performance results for a server"""
    successful = [r for r in results if r['success']]
    
    if not successful:
        return {
            'server': server_name,
            'success_rate': 0,
            'avg_response_time': 0,
            'avg_quality': 0,
            'total_queries': len(results)
        }
    
    avg_time = sum(r['response_time'] for r in successful) / len(successful)
    avg_quality = sum(r['quality_score'] for r in successful) / len(successful)
    success_rate = len(successful) / len(results) * 100
    
    return {
        'server': server_name,
        'success_rate': success_rate,
        'avg_response_time': avg_time,
        'avg_quality': avg_quality,
        'total_queries': len(results),
        'successful_queries': len(successful)
    }

def compare_servers():
    """Compare performance between lynn-pc and T5810"""
    print("🚀 Remote Server Performance Comparison")
    print("=" * 60)
    
    # Test queries of varying complexity
    test_queries = [
        "List all available codebases",
        "Find memory management functions in the utils codebase",
        "What programming languages are used in the z80emu codebase?",
        "Get statistics for the TypeScript-Node-Starter-master codebase",
        "Search for networking functions in the networking_project codebase"
    ]
    
    servers = [
        ("lynn-pc (RTX 3050)", "http://************:11434"),
        ("T5810 (RTX 3090)", "http://************:11434")
    ]
    
    all_results = {}
    
    # Benchmark each server
    for server_name, server_url in servers:
        results = benchmark_server(server_name, server_url, test_queries)
        all_results[server_name] = results
    
    # Analyze results
    print(f"\n📊 PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    performance_stats = {}
    
    for server_name, results in all_results.items():
        stats = analyze_performance(server_name, results)
        performance_stats[server_name] = stats
        
        print(f"\n🖥️  {server_name}")
        print("-" * 40)
        print(f"Success Rate:     {stats['success_rate']:.1f}%")
        print(f"Avg Response Time: {stats['avg_response_time']:.2f}s")
        print(f"Avg Quality Score: {stats['avg_quality']:.1f}/100")
        print(f"Successful Queries: {stats['successful_queries']}/{stats['total_queries']}")
    
    # Head-to-head comparison
    print(f"\n🏆 HEAD-TO-HEAD COMPARISON")
    print("=" * 60)
    
    servers_list = list(performance_stats.keys())
    if len(servers_list) == 2:
        server1, server2 = servers_list
        stats1, stats2 = performance_stats[server1], performance_stats[server2]
        
        print(f"🏃 Speed Winner:")
        if stats1['avg_response_time'] < stats2['avg_response_time']:
            speedup = (stats2['avg_response_time'] - stats1['avg_response_time']) / stats2['avg_response_time'] * 100
            print(f"   🥇 {server1}: {speedup:.1f}% faster")
        else:
            speedup = (stats1['avg_response_time'] - stats2['avg_response_time']) / stats1['avg_response_time'] * 100
            print(f"   🥇 {server2}: {speedup:.1f}% faster")
        
        print(f"\n🎯 Quality Winner:")
        if stats1['avg_quality'] > stats2['avg_quality']:
            quality_diff = stats1['avg_quality'] - stats2['avg_quality']
            print(f"   🥇 {server1}: +{quality_diff:.1f} points higher")
        else:
            quality_diff = stats2['avg_quality'] - stats1['avg_quality']
            print(f"   🥇 {server2}: +{quality_diff:.1f} points higher")
        
        print(f"\n🔄 Reliability Winner:")
        if stats1['success_rate'] > stats2['success_rate']:
            print(f"   🥇 {server1}: {stats1['success_rate']:.1f}% success rate")
        else:
            print(f"   🥇 {server2}: {stats2['success_rate']:.1f}% success rate")
    
    # Overall recommendation
    print(f"\n💡 RECOMMENDATION")
    print("=" * 60)
    
    # Calculate overall score (speed + quality + reliability)
    best_server = None
    best_score = 0
    
    for server_name, stats in performance_stats.items():
        # Normalize scores (lower time is better, higher quality/success is better)
        speed_score = max(0, 100 - stats['avg_response_time'] * 10)  # Penalize slow responses
        quality_score = stats['avg_quality']
        reliability_score = stats['success_rate']
        
        overall_score = (speed_score + quality_score + reliability_score) / 3
        
        print(f"{server_name}:")
        print(f"   Speed Score:      {speed_score:.1f}/100")
        print(f"   Quality Score:    {quality_score:.1f}/100") 
        print(f"   Reliability Score: {reliability_score:.1f}/100")
        print(f"   Overall Score:    {overall_score:.1f}/100")
        
        if overall_score > best_score:
            best_score = overall_score
            best_server = server_name
    
    if best_server:
        print(f"\n🏆 WINNER: {best_server}")
        print(f"   Overall Score: {best_score:.1f}/100")
        
        # GPU-specific insights
        if "RTX 3090" in best_server:
            print("   💪 RTX 3090's superior compute power shows!")
        elif "RTX 3050" in best_server:
            print("   ⚡ RTX 3050's efficiency wins despite lower specs!")

def main():
    print("🎯 Remote GPU Performance Comparison for Code Analysis")
    print("=" * 70)
    print("Testing lynn-pc (RTX 3050) vs T5810 (RTX 3090)")
    
    compare_servers()

if __name__ == "__main__":
    main()
