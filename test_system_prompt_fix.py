#!/usr/bin/env python3
"""
Test the system prompt fix for remote models
"""

import requests

def test_system_prompt_fix():
    """Test if system prompt enables remote model tool usage"""
    print("🧪 Testing System Prompt Fix")
    print("=" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Simple test queries
    test_queries = [
        "list codebases",
        "what codebases are available?",
        "show me the available code repositories"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 30)
        
        payload = {
            "model": "LYNN-PC.llama3:latest",
            "messages": [{"role": "user", "content": query}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                message = data.get("choices", [{}])[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                # Check for success
                real_codebases = ["utils", "z80emu", "library-management-system", "test_project"]
                found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
                
                generic_indicators = ["wordpress", "react", "popular", "example"]
                found_generic = [gi for gi in generic_indicators if gi.lower() in content.lower()]
                
                if len(tool_calls) > 0 or found_real:
                    print(f"✅ SUCCESS: Tool working")
                    print(f"   Tool calls: {len(tool_calls)}")
                    print(f"   Real data: {found_real}")
                elif found_generic:
                    print(f"❌ FAILED: Still generic response")
                    print(f"   Generic indicators: {found_generic}")
                else:
                    print(f"❓ UNCLEAR: No clear indicators")
                
                print(f"   Preview: {content[:100]}...")
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🚀 System Prompt Fix Test")
    print("=" * 40)
    print("Testing if system prompt enables LYNN-PC model to use tools")
    
    test_system_prompt_fix()
    
    print(f"\n💡 Instructions:")
    print("1. Apply system prompt to LYNN-PC model in OpenWebUI")
    print("2. Run this test to verify it works")
    print("3. Apply same approach to T5810 model")

if __name__ == "__main__":
    main()
