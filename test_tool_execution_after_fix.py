#!/usr/bin/env python3
"""
Test tool execution after the fix
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_tool_execution_directly():
    """Test tool execution directly"""
    print("🔧 Testing Tool Execution After Fix")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Test the execute_tool method directly
    print("1️⃣ Testing execute_tool directly:")
    
    result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'malloc free alloc',
        'max_results': 5
    })
    
    print(f"Tool result type: {type(result)}")
    print(f"Tool result keys: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
    
    if 'error' in result:
        print(f"❌ Tool execution error: {result['error']}")
        return False
    elif 'results' in result and result['results']:
        print(f"✅ Tool execution successful: {len(result['results'])} results")
        
        # Show first result
        first_result = result['results'][0]
        content = first_result.get('content', '')[:300]
        print(f"First result preview: {content}...")
        
        return True
    else:
        print(f"⚠️ Tool executed but no results found")
        print(f"Full result: {result}")
        return False

def test_with_explicit_tool_call():
    """Test with explicit tool call instruction"""
    print(f"\n🎯 Testing Explicit Tool Call")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    query = """IMPORTANT: You must use the query_codebase tool to search the utils codebase.

Call query_codebase with:
- codebase_name: "utils"
- query: "malloc"
- max_results: 3

Then show me what the tool actually returns. Do not make up any code."""
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result: {result}")
    
    # Check if it mentions real file paths or code
    if any(term in result for term in ['/app/source_code', 'tmwmem', 'Triangle MicroWorks']):
        print(f"✅ Shows real tool results")
        return True
    else:
        print(f"❌ Still not using real tool results")
        return False

def main():
    print("🔧 Testing Tool Execution After Argument Fix")
    print("=" * 60)
    
    # Test direct tool execution
    direct_works = test_tool_execution_directly()
    
    # Test with explicit instruction
    if direct_works:
        explicit_works = test_with_explicit_tool_call()
    else:
        print("❌ Skipping explicit test - direct tool execution failed")
        explicit_works = False
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Direct tool execution: {'✅ Working' if direct_works else '❌ Not working'}")
    print(f"Explicit tool call:    {'✅ Working' if explicit_works else '❌ Not working'}")
    
    if direct_works and explicit_works:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"The argument parsing fix resolved the issue")
        print(f"T5810 should now show real memory management functions")
    elif direct_works:
        print(f"\n⚠️ PARTIAL SUCCESS")
        print(f"Tool execution works but T5810 still needs better prompting")
    else:
        print(f"\n❌ STILL ISSUES")
        print(f"Tool execution itself has problems")

if __name__ == "__main__":
    main()
