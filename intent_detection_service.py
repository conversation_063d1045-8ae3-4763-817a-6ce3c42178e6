#!/usr/bin/env python3
"""
Intent Detection Service for Code Analyzer Server
Provides server-side intent detection using JSON5 configuration
"""

import json
import re
import os
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import logging

# Try to import json5, fallback to json
try:
    import json5
    HAS_JSON5 = True
except ImportError:
    import json as json5  # type: ignore
    HAS_JSON5 = False

logger = logging.getLogger(__name__)

@dataclass
class IntentResult:
    """Result of intent detection analysis"""
    intent: str
    confidence: float
    matched_keywords: List[str]
    matched_patterns: List[str]
    suggested_action: str
    routing_info: Dict[str, Any]
    debug_info: Dict[str, Any]

class IntentDetectionService:
    """Server-side intent detection using configurable JSON5 configuration"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize intent detection service with configuration"""
        self.config_path = config_path or self._find_config_file()
        self.config = self._load_config()
        self._compile_regex_patterns()
        
        logger.info(f"Intent detection service initialized with config: {self.config_path}")
    
    def _find_config_file(self) -> str:
        """Find configuration file using hierarchical search"""
        search_paths = [
            os.environ.get('INTENT_DETECTION_CONFIG'),
            './config/intent_detection.json5',
            './intent_detection.json5',
            os.path.expanduser('~/.config/code_analyzer/intent_detection.json5')
        ]
        
        for path in search_paths:
            if path and os.path.exists(path):
                return path
        
        # Return default path even if it doesn't exist (will use defaults)
        return './config/intent_detection.json5'
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON5 file with fallback to defaults"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    if HAS_JSON5:
                        config = json5.load(f)
                        logger.info("Loaded configuration using json5 parser")
                    else:
                        # Remove comments for standard JSON parser
                        content = self._strip_json_comments(f.read())
                        config = json.loads(content)
                        logger.warning("json5 not available, using json parser (comments stripped)")
                    
                return config
            except Exception as e:
                logger.error(f"Error loading config from {self.config_path}: {e}")
                return self._get_default_config()
        else:
            logger.warning(f"Config file not found: {self.config_path}, using defaults")
            return self._get_default_config()
    
    def _strip_json_comments(self, content: str) -> str:
        """Strip // comments from JSON content for standard JSON parser"""
        lines = content.split('\n')
        cleaned_lines = []
        for line in lines:
            # Remove // comments but preserve strings
            in_string = False
            escaped = False
            result = []
            i = 0
            while i < len(line):
                char = line[i]
                if escaped:
                    result.append(char)
                    escaped = False
                elif char == '\\' and in_string:
                    result.append(char)
                    escaped = True
                elif char == '"':
                    result.append(char)
                    in_string = not in_string
                elif char == '/' and i + 1 < len(line) and line[i + 1] == '/' and not in_string:
                    break  # Rest of line is comment
                else:
                    result.append(char)
                i += 1
            cleaned_lines.append(''.join(result).rstrip())
        return '\n'.join(cleaned_lines)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file is not available"""
        return {
            "keywords": {
                "programming": ["function", "method", "class", "variable", "code"],
                "technical": ["config", "setting", "parameter"],
                "analysis": ["complexity", "analysis", "metrics"],
                "protocol": ["modbus", "register", "packet"],
                "languages": ["python", "javascript", "cpp"]
            },
            "patterns": {
                "code_questions": ["how does", "what is", "show me"],
                "management": ["list codebase", "select codebase"],
                "help": ["analyzer help", "documentation"]
            },
            "routing_rules": {
                "complexity_analysis": {
                    "triggers": ["analyze complexity", "complexity analysis"],
                    "action": "route_to_complexity_analyzer",
                    "extract_codebase": True
                },
                "code_context": {
                    "triggers": ["code_related_detected"],
                    "action": "route_to_context_retrieval"
                }
            },
            "detection_settings": {
                "min_confidence": 0.6,
                "case_sensitive": False,
                "max_debug_keywords": 10
            }
        }
    
    def _compile_regex_patterns(self):
        """Compile regex patterns for efficient matching"""
        self.regex_patterns = {}
        patterns = self.config.get("regex_patterns", {})
        
        for name, pattern in patterns.items():
            try:
                flags = 0 if self.config.get("detection_settings", {}).get("case_sensitive", False) else re.IGNORECASE
                self.regex_patterns[name] = re.compile(pattern, flags)
            except re.error as e:
                logger.error(f"Invalid regex pattern '{name}': {pattern} - {e}")
    
    def analyze_query(self, query: str) -> IntentResult:
        """Analyze query and determine intent with confidence score"""
        if not query or not query.strip():
            return IntentResult(
                intent="general",
                confidence=0.0,
                matched_keywords=[],
                matched_patterns=[],
                suggested_action="return_empty",
                routing_info={},
                debug_info={"error": "Empty query"}
            )
        
        query_lower = query.lower()
        matched_keywords = []
        matched_patterns = []
        confidence_scores: dict[str, float] = {}
        
        # Check for help intent first (highest priority)
        help_patterns = self.config.get("patterns", {}).get("help", [])
        for pattern in help_patterns:
            if pattern.lower() in query_lower:
                matched_patterns.append(f"{pattern}(help)")
                confidence_scores["help"] = confidence_scores.get("help", 0) + 0.9
        
        # Check for management intent
        mgmt_patterns = self.config.get("patterns", {}).get("management", [])
        for pattern in mgmt_patterns:
            if pattern.lower() in query_lower:
                matched_patterns.append(f"{pattern}(management)")
                confidence_scores["management"] = confidence_scores.get("management", 0) + 0.8
        
        # Check for complexity analysis (specific routing rule)
        complexity_triggers = self.config.get("routing_rules", {}).get("complexity_analysis", {}).get("triggers", [])
        for trigger in complexity_triggers:
            if trigger.lower() in query_lower:
                matched_patterns.append(f"{trigger}(complexity)")
                confidence_scores["complexity_analysis"] = confidence_scores.get("complexity_analysis", 0) + 0.9
        
        # Check for code-related keywords
        code_related_score = 0.0
        for category, keywords in self.config.get("keywords", {}).items():
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    matched_keywords.append(f"{keyword}({category})")
                    code_related_score += 0.1
        
        # Check for code question patterns
        code_patterns = self.config.get("patterns", {}).get("code_questions", [])
        for pattern in code_patterns:
            if pattern.lower() in query_lower:
                matched_patterns.append(f"{pattern}(code_question)")
                code_related_score += 0.2
        
        # Check regex patterns
        for pattern_name, compiled_pattern in self.regex_patterns.items():
            if compiled_pattern.search(query):
                matched_patterns.append(f"regex:{pattern_name}")
                code_related_score += 0.3
        
        if code_related_score > 0:
            confidence_scores["code_analysis"] = min(code_related_score, 1.0)
        
        # Determine final intent and confidence
        if not confidence_scores:
            intent = "general"
            confidence = 0.0
            suggested_action = "return_empty"
        else:
            intent = max(confidence_scores.keys(), key=lambda k: confidence_scores[k])
            confidence = confidence_scores[intent]
            suggested_action = self._get_suggested_action(intent, query)
        
        # Limit debug keywords
        max_debug = self.config.get("detection_settings", {}).get("max_debug_keywords", 10)
        if len(matched_keywords) > max_debug:
            matched_keywords = matched_keywords[:max_debug] + [f"...and {len(matched_keywords) - max_debug} more"]
        
        return IntentResult(
            intent=intent,
            confidence=confidence,
            matched_keywords=matched_keywords,
            matched_patterns=matched_patterns,
            suggested_action=suggested_action,
            routing_info=self._get_routing_info(intent, query),
            debug_info={
                "query_length": len(query),
                "confidence_scores": confidence_scores,
                "config_path": self.config_path
            }
        )
    
    def _get_suggested_action(self, intent: str, query: str) -> str:
        """Get suggested action based on intent"""
        action_map = {
            "help": "route_to_help",
            "management": "route_to_management", 
            "complexity_analysis": "route_to_complexity_analyzer",
            "code_analysis": "route_to_context_retrieval",
            "general": "return_empty"
        }
        return action_map.get(intent, "return_empty")
    
    def _get_routing_info(self, intent: str, query: str) -> Dict[str, Any]:
        """Get routing information for the detected intent"""
        routing_info: Dict[str, Any] = {"intent": intent}
        
        if intent == "complexity_analysis":
            # Try to extract codebase name for complexity analysis
            routing_info["extract_codebase"] = True
            routing_info["analysis_type"] = "complexity"
        elif intent == "code_analysis":
            routing_info["auto_inject_context"] = True
        
        return routing_info
    
    def reload_config(self) -> bool:
        """Reload configuration from file"""
        try:
            old_config_path = self.config_path
            self.config = self._load_config()
            self._compile_regex_patterns()
            logger.info(f"Configuration reloaded from {old_config_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get summary of current configuration"""
        keyword_counts = {k: len(v) for k, v in self.config.get("keywords", {}).items()}
        pattern_counts = {k: len(v) for k, v in self.config.get("patterns", {}).items()}
        
        return {
            "config_path": self.config_path,
            "config_exists": os.path.exists(self.config_path),
            "keyword_categories": keyword_counts,
            "pattern_categories": pattern_counts,
            "routing_rules": list(self.config.get("routing_rules", {}).keys()),
            "has_json5_support": HAS_JSON5
        }

# Global instance
intent_detection_service = IntentDetectionService()
