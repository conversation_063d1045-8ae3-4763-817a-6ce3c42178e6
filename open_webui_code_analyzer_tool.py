"""
title: Code Analysis Tool
author: Your Name
author_url: https://github.com/yourusername
description: Multi-language codebase analysis tool with intelligent code search, context retrieval, vector database management, and automated optimization
version: 3.2.0
license: MIT
requirements: requests
"""

import requests
import aiohttp
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import hashlib
import time
import pickle
import os
import threading
import json
from dataclasses import dataclass, field
from collections import OrderedDict

# Remote Ollama integration
class RemoteOllamaAnalyzer:
    """Direct Ollama analyzer for remote instances"""
    def __init__(self, ollama_url):
        self.ollama_url = ollama_url
        self.code_analyzer_url = "http://************:5002"

    def _preprocess_query(self, query: str) -> str:
        """Preprocess queries to prevent hallucination on 'show' queries"""
        # Convert "show X in Y codebase" to "find X in Y codebase" to prevent hallucination
        if query.lower().startswith("show ") and " in " in query.lower() and "codebase" in query.lower():
            # Replace "show" with "find" and add explicit instruction
            modified_query = query.replace("show ", "find ", 1)
            modified_query += ". Use the query_codebase tool to search the codebase and show actual code results."
            return modified_query
        return query

    def create_tools(self):
        """Create tool definitions for remote Ollama"""
        return [
            {
                'type': 'function',
                'function': {
                    'name': 'list_codebases',
                    'description': 'List all available codebases',
                    'parameters': {'type': 'object', 'properties': {}, 'required': []},
                },
            },
            {
                'type': 'function',
                'function': {
                    'name': 'query_codebase',
                    'description': 'Search code in a specific codebase',
                    'parameters': {
                        'type': 'object',
                        'properties': {
                            'codebase_name': {'type': 'string', 'description': 'Codebase name'},
                            'query': {'type': 'string', 'description': 'Search query'},
                            'max_results': {'type': 'integer', 'description': 'Maximum number of results (default: 10)'}
                        },
                        'required': ['codebase_name', 'query'],
                    },
                },
            }
        ]

    def execute_tool(self, tool_name, arguments):
        """Execute tool via code analyzer API"""
        try:
            if tool_name == 'list_codebases':
                # Use the working endpoint that actually returns codebases
                response = requests.get(f"{self.code_analyzer_url}/codebases", timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    # Handle the actual response format: {"codebases": [...], "total": 11, ...}
                    if isinstance(data, dict) and 'codebases' in data:
                        codebase_list = data['codebases']
                        codebases = [cb['name'] for cb in codebase_list if isinstance(cb, dict) and 'name' in cb]
                        return {"codebases": codebases}
                    else:
                        # Fallback for unexpected format
                        codebases = [cb['name'] for cb in data if isinstance(cb, dict) and 'name' in cb]
                        return {"codebases": codebases}
                else:
                    return {"error": f"List codebases failed: {response.status_code}"}

            elif tool_name == 'query_codebase':
                # Use the search endpoint which should work
                response = requests.post(
                    f"{self.code_analyzer_url}/search",
                    json={
                        "query": arguments.get('query', ''),
                        "codebase_name": arguments.get('codebase_name', ''),
                        "n_results": max(1, int(arguments.get('max_results', 10)))
                    },
                    timeout=60
                )
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Query codebase failed: {response.status_code} - {response.text}"}
            else:
                return {"error": f"Unknown tool: {tool_name}"}

        except Exception as e:
            return {"error": str(e)}

    def chat_with_tools(self, message, model="llama3.1:latest"):
        """Chat with remote Ollama using native tool calling"""
        # Preprocess query to prevent hallucination
        processed_message = self._preprocess_query(message)

        tools = self.create_tools()
        messages = [{'role': 'user', 'content': processed_message}]

        # Initial request
        payload = {'model': model, 'messages': messages, 'tools': tools, 'stream': False}

        try:
            response = requests.post(f"{self.ollama_url}/api/chat", json=payload, timeout=120)
            if response.status_code != 200:
                return f"Error: {response.status_code}"

            data = response.json()
            message_data = data.get('message', {})
            tool_calls = message_data.get('tool_calls', [])

            if not tool_calls:
                return message_data.get('content', 'No response')

            # Execute tools
            messages.append(message_data)
            for tool_call in tool_calls:
                function = tool_call.get('function', {})
                tool_name = function.get('name', '')
                # Handle both string and dict argument formats
                arguments_raw = function.get('arguments', '{}')
                if isinstance(arguments_raw, dict):
                    arguments = arguments_raw
                else:
                    arguments = json.loads(arguments_raw)

                result = self.execute_tool(tool_name, arguments)
                messages.append({'role': 'tool', 'content': json.dumps(result)})

            # Final response
            final_payload = {'model': model, 'messages': messages, 'stream': False}
            final_response = requests.post(f"{self.ollama_url}/api/chat", json=final_payload, timeout=120)

            if final_response.status_code == 200:
                return final_response.json().get('message', {}).get('content', 'No response')
            else:
                return f"Final response error: {final_response.status_code}"

        except Exception as e:
            return f"Exception: {str(e)}"


# === INTELLIGENT CACHING SYSTEM ===

@dataclass
class CacheConfig:
    """Configuration for the intelligent caching system"""
    memory_size: int = 100  # Number of items in memory cache
    memory_ttl: int = 1800  # 30 minutes
    disk_size: int = 1000   # Number of items in disk cache
    disk_ttl: int = 86400   # 24 hours
    disk_path: str = field(default_factory=lambda: os.path.expanduser("~/.openwebui_cache"))
    enable_compression: bool = True
    cache_hit_extend_ttl: bool = True  # Extend TTL on cache hits


@dataclass
class CacheStats:
    """Cache performance statistics"""
    memory_hits: int = 0
    memory_misses: int = 0
    disk_hits: int = 0
    disk_misses: int = 0
    total_requests: int = 0

    @property
    def hit_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return (self.memory_hits + self.disk_hits) / self.total_requests

    @property
    def memory_hit_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return self.memory_hits / self.total_requests


class CacheEntry:
    """Individual cache entry with metadata"""
    def __init__(self, data: Any, ttl: int, tags: Optional[List[str]] = None):
        self.data = data
        self.created_at = time.time()
        self.expires_at = self.created_at + ttl
        self.access_count = 0
        self.last_accessed = self.created_at
        self.tags = tags or []
        self.size = len(pickle.dumps(data)) if data else 0

    def is_expired(self) -> bool:
        return time.time() > self.expires_at

    def access(self, extend_ttl: bool = False, ttl_extension: int = 0):
        """Record access and optionally extend TTL"""
        self.access_count += 1
        self.last_accessed = time.time()
        if extend_ttl and ttl_extension > 0:
            self.expires_at = max(self.expires_at, time.time() + ttl_extension)


class MemoryCache:
    """High-speed in-memory cache with LRU eviction"""
    def __init__(self, max_size: int, default_ttl: int):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = threading.RLock()

    def _generate_key(self, query: str, codebase: str, filters: Optional[Dict] = None) -> str:
        """Generate consistent cache key"""
        key_data = {
            'query': query.strip().lower(),
            'codebase': codebase,
            'filters': filters or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:16]

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            if key not in self.cache:
                return None

            entry = self.cache[key]
            if entry.is_expired():
                del self.cache[key]
                return None

            # Move to end (most recently used)
            self.cache.move_to_end(key)
            entry.access()
            return entry.data

    def put(self, key: str, data: Any, ttl: Optional[int] = None, tags: Optional[List[str]] = None):
        """Store item in cache"""
        with self.lock:
            ttl = ttl or self.default_ttl
            entry = CacheEntry(data, ttl, tags)

            # Remove if already exists
            if key in self.cache:
                del self.cache[key]

            # Add new entry
            self.cache[key] = entry

            # Evict oldest if over capacity
            while len(self.cache) > self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]

    def clear_expired(self):
        """Remove expired entries"""
        with self.lock:
            expired_keys = [k for k, v in self.cache.items() if v.is_expired()]
            for key in expired_keys:
                del self.cache[key]

    def clear_by_tags(self, tags: List[str]):
        """Clear entries with specific tags"""
        with self.lock:
            keys_to_remove = []
            for key, entry in self.cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_remove.append(key)
            for key in keys_to_remove:
                del self.cache[key]

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_size = sum(entry.size for entry in self.cache.values())
            return {
                'entries': len(self.cache),
                'max_size': self.max_size,
                'total_size_bytes': total_size,
                'avg_size_bytes': total_size / len(self.cache) if self.cache else 0
            }


class DiskCache:
    """Persistent disk-based cache for larger storage"""
    def __init__(self, cache_dir: str, max_size: int, default_ttl: int):
        self.cache_dir = cache_dir
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.lock = threading.RLock()

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Index file for metadata
        self.index_file = os.path.join(cache_dir, "cache_index.json")
        self.index = self._load_index()

    def _load_index(self) -> Dict[str, Dict]:
        """Load cache index from disk"""
        try:
            if os.path.exists(self.index_file):
                with open(self.index_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def _save_index(self):
        """Save cache index to disk"""
        try:
            with open(self.index_file, 'w') as f:
                json.dump(self.index, f)
        except Exception:
            pass

    def _get_file_path(self, key: str) -> str:
        """Get file path for cache key"""
        return os.path.join(self.cache_dir, f"{key}.cache")

    def get(self, key: str) -> Optional[Any]:
        """Get item from disk cache"""
        with self.lock:
            if key not in self.index:
                return None

            entry_info = self.index[key]
            if time.time() > entry_info['expires_at']:
                self._remove_entry(key)
                return None

            try:
                file_path = self._get_file_path(key)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)

                # Update access info
                entry_info['access_count'] += 1
                entry_info['last_accessed'] = time.time()
                self._save_index()

                return data
            except Exception:
                self._remove_entry(key)
                return None

    def put(self, key: str, data: Any, ttl: Optional[int] = None, tags: Optional[List[str]] = None):
        """Store item in disk cache"""
        with self.lock:
            ttl = ttl or self.default_ttl

            try:
                # Save data to file
                file_path = self._get_file_path(key)
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)

                # Update index
                self.index[key] = {
                    'created_at': time.time(),
                    'expires_at': time.time() + ttl,
                    'access_count': 0,
                    'last_accessed': time.time(),
                    'tags': tags or [],
                    'size': os.path.getsize(file_path)
                }

                # Evict old entries if over capacity
                self._evict_if_needed()
                self._save_index()

            except Exception:
                pass

    def _remove_entry(self, key: str):
        """Remove entry from cache"""
        try:
            file_path = self._get_file_path(key)
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass

        if key in self.index:
            del self.index[key]

    def _evict_if_needed(self):
        """Evict oldest entries if over capacity"""
        while len(self.index) > self.max_size:
            # Find oldest entry
            oldest_key = min(self.index.keys(),
                           key=lambda k: self.index[k]['last_accessed'])
            self._remove_entry(oldest_key)

    def clear_expired(self):
        """Remove expired entries"""
        with self.lock:
            current_time = time.time()
            expired_keys = [k for k, v in self.index.items()
                          if current_time > v['expires_at']]
            for key in expired_keys:
                self._remove_entry(key)
            if expired_keys:
                self._save_index()

    def clear(self):
        """Clear all entries from disk cache"""
        with self.lock:
            # Remove all cache files
            for key in list(self.index.keys()):
                self._remove_entry(key)

            # Clear the index
            self.index.clear()
            self._save_index()

    def get_stats(self) -> Dict[str, Any]:
        """Get disk cache statistics"""
        with self.lock:
            total_size = sum(entry['size'] for entry in self.index.values())
            return {
                'entries': len(self.index),
                'max_size': self.max_size,
                'total_size_bytes': total_size,
                'avg_size_bytes': total_size / len(self.index) if self.index else 0
            }


class IntelligentCache:
    """Multi-layer intelligent caching system"""
    def __init__(self, config: Optional[CacheConfig] = None):
        self.config = config or CacheConfig()
        self.memory_cache = MemoryCache(self.config.memory_size, self.config.memory_ttl)
        self.disk_cache = DiskCache(self.config.disk_path, self.config.disk_size, self.config.disk_ttl)
        self.stats = CacheStats()

        # Start background cleanup thread
        self._start_cleanup_thread()

    def _start_cleanup_thread(self):
        """Start background thread for cache maintenance"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # Run every 5 minutes
                    self.memory_cache.clear_expired()
                    self.disk_cache.clear_expired()
                except Exception:
                    pass

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    def generate_cache_key(self, query: str, codebase: str, filters: Optional[Dict] = None,
                          context_type: str = "context") -> str:
        """Generate consistent cache key for queries"""
        return self.memory_cache._generate_key(query, codebase, filters) + f"_{context_type}"

    async def get_cached_context(self, query: str, codebase: str,
                                filters: Optional[Dict] = None) -> Optional[str]:
        """Get cached context if available"""
        cache_key = self.generate_cache_key(query, codebase, filters, "context")

        self.stats.total_requests += 1

        # Try memory cache first (L1)
        result = self.memory_cache.get(cache_key)
        if result is not None:
            self.stats.memory_hits += 1
            return result

        # Try disk cache (L2)
        result = self.disk_cache.get(cache_key)
        if result is not None:
            self.stats.disk_hits += 1
            # Promote to memory cache
            self.memory_cache.put(cache_key, result,
                                tags=[f"codebase:{codebase}", "context"])
            return result

        # Cache miss
        self.stats.memory_misses += 1
        self.stats.disk_misses += 1
        return None

    async def cache_context(self, query: str, codebase: str, context: str,
                           filters: Optional[Dict] = None):
        """Cache context result"""
        cache_key = self.generate_cache_key(query, codebase, filters, "context")
        tags = [f"codebase:{codebase}", "context"]

        # Store in both memory and disk cache
        self.memory_cache.put(cache_key, context, tags=tags)
        self.disk_cache.put(cache_key, context, tags=tags)

    def invalidate_codebase(self, codebase: str):
        """Invalidate all cache entries for a specific codebase"""
        tags_to_clear = [f"codebase:{codebase}"]
        self.memory_cache.clear_by_tags(tags_to_clear)
        # Note: Disk cache tag clearing would need additional implementation

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        memory_stats = self.memory_cache.get_stats()
        disk_stats = self.disk_cache.get_stats()

        return {
            'hit_rate': self.stats.hit_rate,
            'memory_hit_rate': self.stats.memory_hit_rate,
            'total_requests': self.stats.total_requests,
            'memory_cache': memory_stats,
            'disk_cache': disk_stats,
            'performance_improvement': f"{(self.stats.hit_rate * 100):.1f}% faster"
        }


class Tools:
    """
    Codebase Analyzer - Multi-Language Code Analysis with Vector Database Management

    Key Features:
    • 🔍 Intelligent code search and context retrieval
    • 🗄️ Complete vector database lifecycle management (CRUD operations)
    • ⚡ Optimized for 16K context with up to 20 code chunks
    • 🎯 Eliminates double LLM calls by providing raw context

    Vector Database Operations:
    • create_vector_database() - Create new database for codebase
    • rebuild_vector_database() - Delete and recreate database
    • reprocess_vector_database() - Update existing database
    • delete_vector_database() - Permanently remove database
    """
    
    class Valves(BaseModel):
        code_analyzer_server_url: str = Field(
            default="http://************:5002",
            description="URL of the Code Analyzer server (use home-ai-server IP for reliability)"
        )
        enable_remote_ollama: bool = Field(
            default=True,
            description="Enable direct Ollama tool calling for remote models"
        )
        lynn_pc_ollama_url: str = Field(
            default="http://************:11434",
            description="lynn-pc Ollama server URL"
        )
        t5810_ollama_url: str = Field(
            default="http://************:11434",
            description="T5810 Ollama server URL"
        )
        request_timeout: int = Field(
            default=90,
            description="Request timeout in seconds for general operations"
        )
        ai_timeout: int = Field(
            default=180,
            description="Request timeout in seconds for AI operations (3 minutes)"
        )
        max_results: int = Field(
            default=20,
            description="Maximum number of search results (optimized for 16k context)"
        )
        auto_context_injection: bool = Field(
            default=True,
            description="Automatically optimize context for OpenWebUI"
        )
        current_codebase: str = Field(
            default="",
            description="Currently selected codebase (cached)"
        )
        context_format: str = Field(
            default="clean",
            description="Context format: 'clean' (optimized) or 'detailed' (original)"
        )

        # === CACHING CONFIGURATION ===
        # NOTE: Caching disabled by default due to issues with stale failed results.
        # Consider removing caching entirely if no significant performance benefit.
        enable_caching: bool = Field(
            default=False,
            description="Enable caching system (DISABLED by default - cache can serve stale failed results)"
        )
        cache_memory_size: int = Field(
            default=100,
            description="Number of items to keep in memory cache"
        )
        cache_memory_ttl: int = Field(
            default=1800,
            description="Memory cache TTL in seconds (30 minutes)"
        )
        cache_disk_size: int = Field(
            default=1000,
            description="Number of items to keep in disk cache"
        )
        cache_disk_ttl: int = Field(
            default=86400,
            description="Disk cache TTL in seconds (24 hours)"
        )

        # === INTENT DETECTION KEYWORD CONFIGURATION ===
        # Configurable keyword categories for flexible intent detection

        # Core programming and software development keywords
        keywords_programming: str = Field(
            default="function,method,class,variable,algorithm,implementation,code,program,software,debug,error,bug,compile,memory,pointer,array,loop,condition,return,struct,namespace,template,inheritance,polymorphism,encapsulation,malloc,free,new,delete,import,include,header,socket,network,tcp,udp,protocol,buffer,thread,mutex,semaphore,async,sync,callback,api,library",
            description="Core programming keywords (comma-separated)"
        )

        # Technical configuration and system keywords
        keywords_technical: str = Field(
            default="timer,timers,timeout,configuration,config,settings,setting,parameter,parameters,option,options,handler,handlers,processor,processing,manager,management",
            description="Technical configuration keywords (comma-separated)"
        )

        # Code analysis and metrics keywords
        keywords_analysis: str = Field(
            default="complexity,analysis,analyze,metrics,performance,profiling,benchmark,optimization,refactor,review",
            description="Code analysis and metrics keywords (comma-separated)"
        )

        # Protocol and communication keywords
        keywords_protocol: str = Field(
            default="register,registers,modbus,holding,coil,discrete,read,write,request,response,frame,packet,serial,ethernet,communication,device,slave,master",
            description="Protocol and communication keywords (comma-separated)"
        )

        # Programming language specific keywords
        keywords_languages: str = Field(
            default="python,javascript,typescript,java,csharp,cpp,c++,rust,go,php,ruby,swift,kotlin,scala,perl,bash,powershell",
            description="Programming language keywords (comma-separated)"
        )

        # Code-related question patterns
        patterns_code_questions: str = Field(
            default="how does,how is,how are,show me,find,explain,what is,what are,where is,where are,tell me about,describe,functions used,methods used",
            description="Code-related question patterns (comma-separated)"
        )

        # Management operation phrases
        phrases_management: str = Field(
            default="list codebase,show codebase,available codebase,select codebase,choose codebase,switch codebase,process codebase,index codebase,delete codebase,get stats,show stats,codebase stats,get statistics,show statistics,codebase statistics",
            description="Management operation phrases (comma-separated)"
        )

        # Help-related phrases
        phrases_help: str = Field(
            default="codebase analyzer help,codebase_analyzer help,analyzer help,codebase help,help with codebase,help with analyzer,help with code analysis,codebase analyzer guide,analyzer guide,codebase analysis help,how to analyze code,codebase documentation,analyzer documentation,code search help,code context help",
            description="Help-related phrases (comma-separated)"
        )
    
    def __init__(self):
        self.valves = self.Valves()
        self.citation = False  # We'll handle citations manually

        # Debug: Log valve initialization for codebase selection persistence
        print(f"🔧 [TOOL INIT] Current codebase from valves: '{self.valves.current_codebase}'", flush=True)

        # Track when we last made a codebase selection (to prevent immediate override)
        self._last_selection_time = 0

        # Track codebase changes for debugging
        self._codebase_change_log = []

        # Load persistent codebase selection from server (fallback for valve persistence issues)
        self._load_persistent_codebase_selection()

        # Log initial codebase state
        self._log_codebase_change("INIT", "", self.valves.current_codebase)

        # Initialize remote Ollama analyzers
        self.remote_analyzers = {}
        if self.valves.enable_remote_ollama:
            self.remote_analyzers = {
                "lynn-pc": RemoteOllamaAnalyzer(self.valves.lynn_pc_ollama_url),
                "t5810": RemoteOllamaAnalyzer(self.valves.t5810_ollama_url)
            }

        # Initialize intelligent caching system (DISABLED by default)
        if self.valves.enable_caching:
            cache_config = CacheConfig(
                memory_size=self.valves.cache_memory_size,
                memory_ttl=self.valves.cache_memory_ttl,
                disk_size=self.valves.cache_disk_size,
                disk_ttl=self.valves.cache_disk_ttl
            )
            self.cache = IntelligentCache(cache_config)
            print("🔧 [CACHE] Caching enabled", flush=True)
        else:
            self.cache = None
            print("🔧 [CACHE] Caching disabled (default) - all queries will be fresh", flush=True)

        # List of private methods that should not be called directly by OpenWebUI
        self._private_methods = {
            '_format_context_for_openwebui',
            '_analyze_query_for_context',
            '_determine_optimal_context_format',
            '_detect_query_intent',
            '_detect_code_related_query',
            '_auto_inject_optimized_context',
            '_handle_management_query',
            '_suggest_codebase_selection',
            '_explain_format_choice',
            '_ensure_chunk_clarification',
            '_add_chunk_clarification',
            '_get_available_codebase_names'
        }

    def __getattr__(self, name):
        """
        Prevent OpenWebUI from calling private methods directly.
        This handles cases where OpenWebUI tries to call internal helper methods.
        """
        if name in self._private_methods:
            def private_method_error(*args, **kwargs):
                return "❌ This function is not available for direct use. Please use the main tool commands instead."
            return private_method_error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def __dir__(self):
        """
        Control which methods OpenWebUI can see.
        Only expose public methods, hide private ones.
        """
        # Get all attributes from the class
        all_attrs = set(dir(type(self)))

        # Remove private methods from the visible list
        public_attrs = all_attrs - self._private_methods

        # Also remove any method starting with underscore
        public_attrs = {attr for attr in public_attrs if not attr.startswith('_')}

        return list(public_attrs)

    def _detect_codebase_in_query(self, query: str) -> Optional[str]:
        """
        🎯 Auto-detect codebase name mentioned in the query.

        Examples:
        - "tell me about modbus holding register" -> "modbus"
        - "show utils memory functions" -> "utils"
        - "find z80emu opcodes" -> "z80emu"
        """
        try:
            # Get available codebases synchronously (for performance)
            # Use a simple HTTP request instead of the async method
            import requests
            response = requests.get(f"{self.valves.code_analyzer_server_url}/codebases", timeout=5)
            if response.status_code != 200:
                return None

            data = response.json()
            available_codebases = [cb.get('name', '') for cb in data.get('codebases', [])]
            if not available_codebases:
                return None

            query_lower = query.lower()

            # Sort by length (longest first) to match more specific names first
            # This prevents "test" from matching before "test_project"
            sorted_codebases = sorted(available_codebases, key=len, reverse=True)

            import re
            for codebase in sorted_codebases:
                if len(codebase) > 50:  # Skip extremely long names
                    continue

                codebase_lower = codebase.lower()

                # For hyphenated names, use special matching
                if '-' in codebase:
                    patterns = [
                        r'(?:^|\s)' + re.escape(codebase_lower) + r'(?:\s|$)',  # Space-bounded
                        r'\b' + re.escape(codebase_lower) + r'\b',  # Word boundary
                        r'"' + re.escape(codebase_lower) + r'"',  # Quoted
                        r"'" + re.escape(codebase_lower) + r"'",  # Single quoted
                    ]
                    if any(re.search(pattern, query_lower) for pattern in patterns):
                        print(f"🎯 [AUTO-DETECT] Found codebase '{codebase}' in query", flush=True)
                        return codebase
                else:
                    # Standard word boundary matching for simple names
                    pattern = r'\b' + re.escape(codebase_lower) + r'\b'
                    if re.search(pattern, query_lower):
                        print(f"🎯 [AUTO-DETECT] Found codebase '{codebase}' in query", flush=True)
                        return codebase

            return None

        except Exception as e:
            print(f"⚠️ [AUTO-DETECT] Error detecting codebase in query: {e}", flush=True)
            return None

    # --- Codebase Management Tools (Retained) ---
    
    async def list_codebases(
        self,
        __event_emitter__=None
    ) -> str:
        """
        📚 List all available codebases for analysis.
        
        Shows all codebases found in the source code directory, along with their
        indexing status, document counts, and last update times.
        """
        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Retrieving available codebases...", "done": False}
            })

        try:
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/list_codebases",
                json={},  # Send empty payload for consistency with test script
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to retrieve codebase list")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Codebases retrieved successfully", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to list codebases: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Error: {str(e)}", "done": True}
                })
            return error_msg
    
    async def select_codebase(
        self,
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🎯 Select a specific codebase for subsequent analysis operations.
        
        After selecting a codebase, all search and analysis tools will operate
        on that codebase until a different one is selected.
        
        :param codebase_name: Name of the codebase to select for analysis
                                 (supports names with hyphens like "TypeScript-Node-Starter-master")
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Selecting codebase: {codebase_name}", "done": False}
            })
        
        try:
            # Handle case where no parameter is provided
            if codebase_name is None:
                # Get available codebases to show in the error message
                available_codebases = self._get_available_codebases()
                if available_codebases:
                    example_codebase = available_codebases[0]
                    return f"❌ Please specify a codebase name. Example: 'select codebase {example_codebase}'\n\nAvailable codebases: {', '.join(available_codebases)}"
                else:
                    return "❌ Please specify a codebase name. No codebases are currently available."

            # Handle different parameter formats that OpenWebUI might send
            if isinstance(codebase_name, dict):
                # If OpenWebUI sends a dict, try to extract the codebase name
                if 'codebase_name' in codebase_name:
                    actual_name = codebase_name['codebase_name']
                elif 'name' in codebase_name:
                    actual_name = codebase_name['name']
                else:
                    # Try to find any string value in the dict
                    string_values = [v for v in codebase_name.values() if isinstance(v, str)]
                    if string_values:
                        actual_name = string_values[0]
                    else:
                        return "❌ Invalid codebase parameter format. Expected string, got dict without valid codebase name."
            elif isinstance(codebase_name, str):
                actual_name = codebase_name
            else:
                return f"❌ Invalid codebase parameter type: {type(codebase_name)}. Expected string."

            # Clean the name
            actual_name = actual_name.strip()
            if not actual_name:
                return "❌ Codebase name cannot be empty."

            # In stateless architecture, validate codebase exists by listing available codebases
            try:
                list_response = requests.post(
                    f"{self.valves.code_analyzer_server_url}/tools/list_codebases",
                    json={},  # Send empty payload for consistency
                    headers={"Content-Type": "application/json"},
                    timeout=self.valves.request_timeout
                )

                if list_response.status_code == 200:
                    list_data = list_response.json()
                    available_codebases = list_data.get("result", "").lower()

                    # Check if the requested codebase is available
                    if actual_name.lower() not in available_codebases:
                        return f"❌ Codebase '{actual_name}' not found. Available codebases:\n{list_data.get('result', 'Unable to list codebases')}"
                else:
                    return f"❌ Unable to verify codebase availability: {list_response.status_code}"

            except Exception as e:
                return f"❌ Error validating codebase: {str(e)}"

            # Store selection locally (client-side state)
            old_codebase = self.valves.current_codebase
            self.valves.current_codebase = actual_name

            # Log codebase selection change
            self._log_codebase_change("SELECT", old_codebase, actual_name)

            # Save to persistent storage (fallback for valve persistence issues)
            self._save_persistent_codebase_selection(actual_name)

            # Mark that we just made a selection (to prevent immediate override)
            self._last_selection_time = time.time()

            # Call the backend to get actual codebase information
            try:
                select_response = requests.post(
                    f"{self.valves.code_analyzer_server_url}/tools/select_codebase",
                    json={"codebase_name": actual_name},
                    headers={"Content-Type": "application/json"},
                    timeout=self.valves.request_timeout
                )

                if select_response.status_code == 200:
                    backend_data = select_response.json()
                    backend_result = backend_data.get("result", "")

                    # Return the backend's detailed information
                    if __event_emitter__:
                        await __event_emitter__({
                            "type": "status",
                            "data": {"description": f"Codebase {actual_name} selected with details", "done": True}
                        })

                    return backend_result
                else:
                    print(f"⚠️ [DEBUG] Backend select_codebase failed: {select_response.status_code}", flush=True)
                    # Fall back to basic response

            except Exception as e:
                print(f"⚠️ [DEBUG] Error calling backend select_codebase: {e}", flush=True)
                # Fall back to basic response

            # Fallback response if backend call fails
            result = f"""✅ **Codebase Selected: {actual_name}**

📊 **Client-Side Selection:**
- Previous: {old_codebase or 'None'}
- Current: {actual_name}
- Architecture: Stateless (client manages selection)

🎯 **Ready for Analysis!**
All subsequent queries will use the '{actual_name}' codebase.

⚠️ **Note**: Could not retrieve detailed codebase information from server.
"""

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Codebase {actual_name} selected", "done": True}
                })

            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to select codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Selection failed: {str(e)}", "done": True}
                })
            return error_msg
    
    async def process_codebase(
        self,
        codebase_name: str,
        exclude_dirs: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        ⚙️ Process a codebase: analyze source code and create searchable database.
        
        This tool performs the complete pipeline:
        1. Code Preprocessing: Parse multi-language files using tree-sitter
        2. Chunk Extraction: Extract functions, classes, methods, structs, etc.
        3. Vector Database Creation: Generate embeddings and index in ChromaDB
        
        :param codebase_name: Name of the codebase to process
        :param exclude_dirs: Comma-separated list of directories to exclude (e.g., "build,test,docs")
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Starting processing of {codebase_name}...", "done": False}
            })
        
        try:
            # Parse exclude_dirs string into list
            exclude_list = None
            if exclude_dirs:
                # Ensure exclude_dirs is a string
                if isinstance(exclude_dirs, str):
                    exclude_list = [d.strip() for d in exclude_dirs.split(",") if d.strip()]
                elif isinstance(exclude_dirs, (list, tuple)):
                    # If it's already a list/tuple, use it directly
                    exclude_list = [str(d).strip() for d in exclude_dirs if str(d).strip()]
                else:
                    # Convert other types to string and split
                    exclude_list = [d.strip() for d in str(exclude_dirs).split(",") if d.strip()]
            
            payload = {
                "codebase_name": codebase_name.strip(),
                "exclude_dirs": exclude_list
            }
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Preprocessing source code...", "done": False}
                })
            
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/process_codebase",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=600  # 10 minutes for processing
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Processing completed but no result returned")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Processing completed successfully", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to process codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Processing failed: {str(e)}", "done": True}
                })
            return error_msg

    async def delete_codebase(
        self,
        codebase_name: str,
        __event_emitter__=None
    ) -> str:
        """
        🗑️ Delete a specific codebase and its associated data.

        This will permanently remove the indexed data for the specified codebase
        from the vector database. The source code files remain unchanged.

        ⚠️ **Important**: After deletion, you must use process_codebase() before
        you can select_codebase() again, as the ChromaDB collection is removed.

        Args:
            codebase_name: Name of the codebase to delete
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Deleting codebase: {codebase_name}...", "done": False}
            })

        try:
            payload = {"codebase_name": codebase_name}

            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/delete_codebase",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )

            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to delete codebase")

            # Clear cache if deleting current codebase
            if self.valves.current_codebase == codebase_name:
                self.valves.current_codebase = ""

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Codebase {codebase_name} deleted successfully", "done": True}
                })

            return result

        except Exception as e:
            error_msg = f"❌ Failed to delete codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Deletion failed: {str(e)}", "done": True}
                })
            return error_msg

    async def _analyze_complexity(
        self,
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🔍 Analyze code complexity patterns in a codebase using real code analysis.

        This function performs actual complexity analysis by:
        • Examining real code patterns and structures
        • Searching for complexity indicators (nested loops, conditionals, etc.)
        • Providing concrete examples from the actual codebase
        • Using real metrics from codebase statistics

        :param codebase_name: Name of the codebase to analyze (uses current if not specified)
        """
        # Use current codebase if not specified
        target_codebase = codebase_name or self.valves.current_codebase

        if not target_codebase:
            return "❌ No codebase selected. Use select_codebase() first."

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Analyzing complexity patterns in {target_codebase}...", "done": False}
            })

        try:
            # Get basic codebase statistics first
            stats_response = await self.get_codebase_stats(target_codebase)

            # Search for complexity indicators in actual code
            complexity_indicators = []

            # Search for nested loops
            nested_loops = await self.search_code("for.*for|while.*while|for.*while", target_codebase, n_results=5)
            if "No relevant code context found" not in nested_loops:
                complexity_indicators.append(("Nested Loops", nested_loops))

            # Search for complex conditionals
            complex_conditionals = await self.search_code("if.*&&.*||", target_codebase, n_results=5)
            if "No relevant code context found" not in complex_conditionals:
                complexity_indicators.append(("Complex Conditionals", complex_conditionals))

            # Search for switch statements (complexity indicator)
            switch_statements = await self.search_code("switch.*case", target_codebase, n_results=5)
            if "No relevant code context found" not in switch_statements:
                complexity_indicators.append(("Switch Statements", switch_statements))

            # Search for recursive functions
            recursive_functions = await self.search_code("function.*\\(.*\\).*{.*function", target_codebase, n_results=3)
            if "No relevant code context found" not in recursive_functions:
                complexity_indicators.append(("Potential Recursion", recursive_functions))

            # Build the analysis report
            result_parts = [
                f"🔍 **Code Complexity Analysis: {target_codebase}**",
                "",
                "📊 **Codebase Overview:**"
            ]

            # Include basic statistics
            result_parts.append(stats_response)
            result_parts.append("")

            # Add complexity findings
            if complexity_indicators:
                result_parts.append("🎯 **Complexity Indicators Found:**")
                result_parts.append("")

                for indicator_type, examples in complexity_indicators:
                    result_parts.append(f"### {indicator_type}")
                    result_parts.append(examples)
                    result_parts.append("")
            else:
                result_parts.append("✅ **Low Complexity Detected:**")
                result_parts.append("No significant complexity patterns found in the codebase.")
                result_parts.append("This suggests relatively straightforward, maintainable code.")
                result_parts.append("")

            # Add recommendations
            result_parts.extend([
                "💡 **Analysis Notes:**",
                "• This analysis is based on actual code patterns found in your codebase",
                "• Complexity indicators help identify areas that may need refactoring",
                "• Use search_code() to examine specific patterns in more detail",
                "",
                "🔧 **Next Steps:**",
                "• Review highlighted complex sections for potential simplification",
                "• Consider breaking down large functions with multiple complexity indicators",
                "• Use search_code() to find specific patterns: search_code('pattern', codebase_name)"
            ])

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Complexity analysis complete", "done": True}
                })

            return "\n".join(result_parts)

        except Exception as e:
            error_msg = f"❌ Error analyzing complexity: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Analysis failed: {str(e)}", "done": True}
                })
            return error_msg

    async def get_codebase_stats(
        self,
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        📊 Get comprehensive statistics about a specific codebase.

        Returns detailed metrics about the indexed codebase including:
        • Scale metrics: Total indexed chunks (text segments), actual files, and last update time
        • Code structure breakdown: Distribution of functions, classes, methods, etc.
        • Language analysis: Multi-language code distribution

        Note: Chunks are text segments created by splitting source files for vector search.
        One file can contain multiple chunks.

        :param codebase_name: Name of the codebase to analyze
        """
        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Retrieving statistics for {codebase_name}", "done": False}
            })

        try:
            # Handle case where no parameter is provided
            if codebase_name is None:
                return "❌ Please specify a codebase name. Example: 'get stats for utils'"

            payload = {"codebase_name": codebase_name.strip()}
                
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/get_code_stats",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            raw_result = data.get("result", "Unable to retrieve codebase statistics")

            # Add clarifying context about chunks vs files
            clarified_result = self._add_chunk_clarification(raw_result)

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Statistics retrieved", "done": True}
                })

            return clarified_result
            
        except Exception as e:
            error_msg = f"❌ Failed to get codebase stats: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Stats retrieval failed: {str(e)}", "done": True}
                })
            return error_msg
    
    # --- HELPER METHODS ---

    # NOTE: _sync_with_server_state removed in stateless architecture
    # Server no longer maintains global codebase state - client is source of truth

    async def _ensure_codebase_selected(self, target_codebase: str) -> bool:
        """
        Ensure the target codebase is selected on the server.
        Returns True if successful, False otherwise.
        """
        try:
            health_response = requests.get(f"{self.valves.code_analyzer_server_url}/health", timeout=5)
            if health_response.status_code == 200:
                health_data = health_response.json()
                server_current = health_data.get("current_codebase", "")

                # If server doesn't have our target codebase selected, try to select it
                if server_current != target_codebase:
                    print(f"🔧 [DEBUG] Server codebase mismatch. Server: '{server_current}', Tool: '{target_codebase}'. Attempting to sync...", flush=True)

                    # Try to select the target codebase on the server
                    select_response = requests.post(
                        f"{self.valves.code_analyzer_server_url}/tools/select_codebase",
                        json={"codebase_name": target_codebase},
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )

                    if select_response.status_code == 200:
                        print(f"✅ [DEBUG] Successfully synced server to codebase '{target_codebase}'", flush=True)
                        return True
                    else:
                        print(f"❌ [DEBUG] Failed to sync server to codebase '{target_codebase}': {select_response.status_code}", flush=True)
                        return False
                else:
                    print(f"✅ [DEBUG] Server already has '{target_codebase}' selected", flush=True)
                    return True
        except requests.RequestException as e:
            # If health check fails, try to select anyway
            print(f"⚠️ [DEBUG] Health check failed ({e}), attempting direct codebase selection...", flush=True)
            try:
                select_response = requests.post(
                    f"{self.valves.code_analyzer_server_url}/tools/select_codebase",
                    json={"codebase_name": target_codebase},
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                if select_response.status_code == 200:
                    print(f"✅ [DEBUG] Direct selection successful for '{target_codebase}'", flush=True)
                    return True
                else:
                    print(f"❌ [DEBUG] Direct selection failed for '{target_codebase}': {select_response.status_code}", flush=True)
                    return False
            except requests.RequestException as e2:
                print(f"❌ [DEBUG] All codebase selection attempts failed: {e2}", flush=True)
                return False

        return True

    async def _get_dynamic_query_enhancement(self, query: str, codebase_name: str) -> List[str]:
        """Get dynamic query enhancement using the new server endpoint"""
        try:
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/api/v1/enhance_query",
                json={"query": query, "codebase_name": codebase_name},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                enhancements = data.get('enhancements', [])
                # Note: enhanced_query from server is available but not used in current implementation
                # The calling code builds its own enhanced query by appending enhancements to the original query

                if enhancements:
                    print(f"🧠 [DEBUG] Dynamic enhancement applied: {enhancements}", flush=True)
                    return enhancements
                else:
                    print("⚠️ [DEBUG] No dynamic enhancements available", flush=True)
                    return []
            else:
                print(f"⚠️ [DEBUG] Enhancement endpoint not available: {response.status_code}", flush=True)
                return []

        except Exception as e:
            print(f"❌ [DEBUG] Error getting dynamic enhancement: {e}", flush=True)
            return []



    # REMOVED: _get_semantic_enhancements() - unused function that added complexity
    # The server-side enhancement is sufficient for intelligent query enhancement



    # --- OPTIMIZED Code Analysis Tools (Primary Change) ---
    
    async def get_code_context(
        self,
        query: str = "",
        codebase_name: Optional[str] = None,
        n_results: int = 10,
        filter_type: Optional[str] = None,
        filter_language: Optional[str] = None,
        filter_file: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🔍 OPTIMIZED: Get relevant code context for OpenWebUI (NO LLM response generation).

        This is the PRIMARY optimization - uses the /tools/get_optimized_context endpoint
        to retrieve raw code context that OpenWebUI's main LLM can analyze directly,
        completely eliminating the double LLM call pattern.

        :param query: The user's query needing code context
        :param codebase_name: Codebase to search (uses current if not specified)
        :param n_results: Number of context chunks to retrieve
        :param filter_type: Filter by code type (function, class, method, etc.)
        :param filter_language: Filter by language (27 supported languages)
        :param filter_file: Filter by file pattern
        """
        # Log context retrieval for monitoring
        print(f"🔧 [CONTEXT] Query: '{query[:30]}...', Codebase: '{codebase_name or self.valves.current_codebase}'", flush=True)

        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        # Check if query is provided
        if not query or query.strip() == "":
            return "❌ Please provide a query to search for code context. Example: get_code_context('error handling patterns')"

        # Use current codebase if not specified, but first check if query mentions a specific codebase
        target_codebase = codebase_name or self._detect_codebase_in_query(query) or self.valves.current_codebase

        # In stateless architecture, we require explicit codebase selection
        if not target_codebase:
            return "❌ No codebase selected. Use select_codebase() first."

        # AGGRESSIVE: Always ensure server has the codebase selected before each query
        # This fixes persistence issues where server loses codebase selection
        if not await self._ensure_codebase_selected(target_codebase):
            print(f"❌ [DEBUG] Failed to ensure codebase '{target_codebase}' is selected on server", flush=True)
            return f"❌ Failed to select codebase '{target_codebase}' on server. Please use select_codebase('{target_codebase}') first."
        
        # === INTELLIGENT CACHING INTEGRATION ===
        filters_dict = {
            'filter_type': filter_type,
            'filter_language': filter_language,
            'filter_file': filter_file,
            'n_results': n_results
        }

        # Try cache first if enabled
        if self.cache:
            cached_result = await self.cache.get_cached_context(
                query, target_codebase, filters_dict
            )
            if cached_result:
                if __event_emitter__:
                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": "⚡ Retrieved from cache (instant response)", "done": True}
                    })
                print(f"🎯 [CACHE HIT] Returning cached context for: {query[:50]}...", flush=True)
                return cached_result

        # Event emission is handled by smart_code_context to avoid duplicate messages

        try:
            # Build context preferences for optimized endpoint
            context_preferences = {}
            if filter_type:
                context_preferences["filter_type"] = filter_type.strip().lower()
            if filter_language:
                context_preferences["filter_language"] = filter_language.strip().lower()
            if filter_file:
                context_preferences["filter_file"] = filter_file.strip()

            # Phase 2: Dynamic semantic enhancement using codebase analyzer
            enhanced_query = query.strip()
            query_lower = query.lower()
            enhancement_applied = False

            # Server-side enhancement only
            try:
                dynamic_enhancements = await self._get_dynamic_query_enhancement(query_lower, target_codebase)
                if dynamic_enhancements:
                    enhanced_query = f"{enhanced_query} {' '.join(dynamic_enhancements)}"
                    enhancement_applied = True
                    print(f"🧠 [DEBUG] Server-side dynamic enhancement applied: {dynamic_enhancements}", flush=True)
                elif len(query.split()) < 4:
                    # Keep short queries as-is for better exact matching
                    # enhanced_query = f"{enhanced_query} function implementation code"  # DISABLED - breaks exact function name searches
                    pass
                elif any(pattern in query_lower for pattern in ['how', 'show', 'find', 'explain', 'what']):
                    # Question-type queries - keep minimal enhancement to preserve intent
                    # enhanced_query = f"{enhanced_query} function code implementation"  # DISABLED - too aggressive
                    pass  # Let natural language queries work as-is
            except Exception as e:
                print(f"⚠️ [DEBUG] Server enhancement failed: {e}", flush=True)
                # Minimal fallback for very short queries - keep as-is for exact matching
                if len(query.split()) < 4:
                    # enhanced_query = f"{enhanced_query} function implementation code"  # DISABLED - breaks exact function name searches
                    pass

            if enhancement_applied:
                print(f"🔧 [DEBUG] Enhanced query: {enhanced_query}", flush=True)

            # REAL FIX: Use /search endpoint which returns actual vector database results
            # Both get_optimized_context and framework_query return sample data
            search_payload = {
                "query": query,
                "codebase_name": target_codebase,
                "n_results": n_results
            }

            print(f"🔧 [DEBUG] USING SEARCH ENDPOINT (REAL FIX): {self.valves.code_analyzer_server_url}/search", flush=True)
            print(f"🔧 [DEBUG] Payload: {search_payload}", flush=True)

            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/search",
                json=search_payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )

            # Debug logging for the response
            print(f"🔧 [DEBUG] Response status: {response.status_code}", flush=True)
            print(f"🔧 [DEBUG] Response headers: {dict(response.headers)}", flush=True)
            
            response.raise_for_status()
            data = response.json()

            # SEARCH ENDPOINT RESPONSE HANDLING
            # /search endpoint returns list of chunks directly
            chunk_count = 0
            raw_result = ""

            if isinstance(data, dict) and 'results' in data:
                # Search endpoint returns dict with 'results' key containing chunks
                chunks = data['results']
                chunk_count = len(chunks)
            elif isinstance(data, list):
                # Fallback: direct list format
                chunks = data
                chunk_count = len(chunks)
            else:
                chunks = []
                chunk_count = 0

            # Format the chunks into a readable context string
            if chunks:
                context_parts = []
                for i, chunk in enumerate(chunks, 1):
                    content = chunk.get("content", "")
                    metadata = chunk.get("metadata", {})
                    file_path = metadata.get("file_path", "unknown")

                    context_parts.append(f"=== Code Section {i} ({file_path}) ===\n{content}")
                raw_result = "\n\n".join(context_parts)
            else:
                raw_result = "No relevant code context found for this query."

            print(f"🔧 [DEBUG] Search endpoint: {chunk_count} chunks found", flush=True)

            # OPTIMIZATION: Format context for OpenWebUI consumption

            # Ensure raw_result is a string (handle cases where server returns dict)
            if not isinstance(raw_result, str):
                if isinstance(raw_result, dict):
                    # Convert dict to string representation
                    raw_result = str(raw_result)
                else:
                    # Convert other types to string
                    raw_result = str(raw_result) if raw_result is not None else ""

            # Use automatic format selection if available, otherwise fall back to user setting
            optimal_format = getattr(self, '_current_optimal_format', self.valves.context_format)

            if optimal_format == "clean":
                context_text = self._format_context_for_openwebui(raw_result)
            else:
                context_text = raw_result  # Return original detailed format
            
            # === CACHE STORAGE ===
            # Store successful result in cache for future use
            if self.cache and context_text:
                await self.cache.cache_context(query, target_codebase, context_text, filters_dict)
                chunk_info = f" ({chunk_count} chunks)" if chunk_count > 0 else ""
                print(f"🎯 [CACHE STORE] Cached context for: {query[:50]}...{chunk_info}", flush=True)

            # Accumulate chunk count for consolidated reporting
            if hasattr(self, '_total_chunks_found'):
                self._total_chunks_found += chunk_count
                self._context_calls_made = getattr(self, '_context_calls_made', 0) + 1
                print(f"🔧 [DEBUG] Accumulated chunks: {self._total_chunks_found} (call #{self._context_calls_made})", flush=True)
            else:
                # Fallback: emit individual status if not in accumulation mode
                if __event_emitter__:
                    if chunk_count > 0:
                        description = f"Code context retrieved successfully ({chunk_count} chunk{'s' if chunk_count != 1 else ''} found)"
                    else:
                        description = "No relevant code context found for this query"

                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": description, "done": True}
                    })

            return context_text
            
        except Exception as e:
            # Detailed error message for debugging
            error_details = str(e)
            print(f"🚨 [ERROR] Context retrieval failed: {error_details}", flush=True)

            # Check for common error patterns
            if "Connection" in error_details or "timeout" in error_details.lower():
                error_msg = f"❌ Server connection failed: {error_details}\nServer URL: {self.valves.code_analyzer_server_url}"
            elif "404" in error_details or "Not Found" in error_details:
                error_msg = f"❌ Server endpoint not found: {error_details}\nCheck if server is running the latest version."
            elif "500" in error_details or "Internal Server Error" in error_details:
                error_msg = f"❌ Server internal error: {error_details}\nCheck server logs for details."
            elif "JSON" in error_details or "json" in error_details:
                error_msg = f"❌ Response parsing error: {error_details}\nServer may have returned invalid JSON."
            else:
                error_msg = f"❌ Context retrieval failed: {error_details}\nServer: {self.valves.code_analyzer_server_url}"

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Context retrieval failed: {error_details[:100]}", "done": True}
                })
            return error_msg

    async def _try_framework_query(self, query: str, codebase_name: str, n_results: int = 10) -> Optional[str]:
        """Try using the new framework query endpoint for enhanced intelligence"""
        try:
            url = f"{self.valves.code_analyzer_server_url}/tools/framework_query"
            payload = {
                "query": query,
                "codebase_name": codebase_name,
                "n_results": n_results,
                "context_preferences": {
                    "prefer_documented": True,
                    "prefer_public_api": True
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get("success", False):
                            # Format framework results
                            query_classification = result.get("query_classification", {})
                            gpu_info = result.get("gpu_info", {})
                            results_data = result.get("results", {})
                            chunks = results_data.get("chunks", [])

                            formatted_result = f"""📊 **Enhanced Framework Analysis**
**Query:** {query}
**Codebase:** {codebase_name}
**Chunks Found:** {len(chunks)}
**Query Complexity:** {query_classification.get('complexity', 'unknown')}
**Processing Strategy:** {', '.join(query_classification.get('suggested_strategies', ['standard']))}

=== CODE CONTEXT (Framework-Enhanced) ===
"""

                            for i, chunk in enumerate(chunks, 1):
                                content = getattr(chunk, 'content', str(chunk))
                                metadata = getattr(chunk, 'metadata', {})

                                formatted_result += f"\n--- Chunk {i} ---\n"
                                formatted_result += f"File: {metadata.get('file_path', 'unknown')}\n"
                                formatted_result += f"Language: {metadata.get('language', 'unknown')}\n"
                                formatted_result += f"\n{content}\n"

                            # Add GPU info if available
                            if gpu_info.get("gpu_infrastructure_available"):
                                formatted_result += "\n=== Processing Info ===\n"
                                formatted_result += f"GPUs Available: {gpu_info.get('available_gpus', 0)}\n"

                            formatted_result += "\n=== END CONTEXT ===\n"
                            formatted_result += "*Analysis completed using enhanced framework with query intelligence*"

                            return formatted_result

                    return None  # Framework not available or failed

        except Exception as e:
            print(f"🔧 [FRAMEWORK] Framework query failed, falling back: {e}", flush=True)
            return None

    async def smart_code_context(
        self,
        query: str,
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🧠 OPTIMIZED: Intelligently retrieve code context with framework integration.

        Analyzes the query to optimize search parameters and returns clean context
        for OpenWebUI's LLM to process directly.
        """
        target_codebase = codebase_name or self._detect_codebase_in_query(query) or self.valves.current_codebase

        # In stateless architecture, we require explicit codebase selection

        if not target_codebase:
            return "❌ No codebase selected. Use select_codebase() first."

        # DISABLED: Framework query returns mock data, use working search endpoint instead
        # Try framework query first for enhanced intelligence
        # if __event_emitter__:
        #     await __event_emitter__({
        #         "type": "status",
        #         "data": {"description": "Trying enhanced framework analysis...", "done": False}
        #     })

        # framework_result = await self._try_framework_query(query, target_codebase, 10)
        # if framework_result:
        #     if __event_emitter__:
        #         await __event_emitter__({
        #             "type": "status",
        #             "data": {"description": "Framework analysis completed successfully", "done": True}
        #         })
        #     return framework_result

        # AGGRESSIVE: Always ensure server has the codebase selected before each query
        # This fixes persistence issues where server loses codebase selection
        if not await self._ensure_codebase_selected(target_codebase):
            print(f"❌ [DEBUG] Failed to ensure codebase '{target_codebase}' is selected on server", flush=True)
            return f"❌ Failed to select codebase '{target_codebase}' on server. Please use select_codebase('{target_codebase}') first."
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Retrieving code context from {target_codebase}...", "done": False}
            })

        # Analyze query to determine optimal search parameters (no LLM needed)
        search_params = self._analyze_query_for_context(query)

        # Set the optimal context format for this query
        optimal_format = search_params.get('optimal_context_format', self.valves.context_format)
        self._current_optimal_format = optimal_format

        try:
            context = await self.get_code_context(
                query=query,
                codebase_name=target_codebase,
                n_results=search_params['n_results'],
                filter_type=search_params.get('filter_type'),
                filter_language=search_params.get('filter_language'),
                filter_file=search_params.get('filter_file'),
                __event_emitter__=__event_emitter__
            )

            # Add chunk clarification if this is a statistics-related response
            if any(term in query.lower() for term in ['stats', 'statistics', 'details', 'metrics']) and 'chunks' in context.lower():
                context = self._add_chunk_clarification(context)

            # Clean up the temporary format setting
            if hasattr(self, '_current_optimal_format'):
                delattr(self, '_current_optimal_format')

            return context
            
        except Exception as _:
            return "❌ Unable to retrieve smart context. Please ensure a codebase is selected and try a different query."
    
    # --- LEGACY Code Analysis Tools (Retained but marked as less optimal) ---
    
    async def search_code(
        self,
        query: str,
        codebase_name: str,
        n_results: int = 10,
        filter_type: Optional[str] = None,
        filter_language: Optional[str] = None,
        filter_file: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🔍 LEGACY: Search through a specific codebase for relevant code snippets.
        
        ⚠️ NOTE: For optimal performance, consider using get_code_context() instead,
        which provides cleaner context for OpenWebUI without formatting overhead.
        
        This tool performs semantic search across the indexed codebase to find
        code snippets that match your query, even if they don't contain exact keywords.
        
        :param query: Search query for finding relevant code snippets
        :param codebase_name: Name of the codebase to search
        :param n_results: Number of results to return (1-10)
        :param filter_type: Filter by code type (function, class, method, etc.)
        :param filter_language: Filter by language (c, cpp, python, csharp)
        :param filter_file: Filter by file pattern (e.g., 'tcp', 'socket', 'net')
        """
        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Searching {codebase_name} for: {query}", "done": False}
            })

        try:
            payload = {
                "query": query.strip(),
                "codebase_name": codebase_name.strip(),
                "n_results": min(max(n_results, 1), self.valves.max_results)
            }
            
            # Add optional filters
            if filter_type:
                payload["filter_type"] = filter_type.strip().lower()
            if filter_language:
                payload["filter_language"] = filter_language.strip().lower()
            if filter_file:
                payload["filter_file"] = filter_file.strip()
                
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/search_code",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "No code search results returned")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Search completed", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Code search failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Search failed: {str(e)}", "done": True}
                })
            return error_msg
    
    async def ask_about_code(
        self,
        question: str,
        codebase_name: str,
        n_results: int = 10,
        filter_type: Optional[str] = None,
        filter_language: Optional[str] = None,
        filter_file: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🚫 DEPRECATED: This function causes double LLM calls and is no longer recommended.

        ⚠️ AUTOMATIC REDIRECT: This function now automatically uses the optimized approach
        to eliminate double LLM calls and improve performance.

        Instead of calling this function directly, the system will:
        1. Get optimized context using get_code_context()
        2. Let OpenWebUI's main LLM process the context directly
        3. Provide the same functionality with better performance
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "🔄 Redirecting to optimized approach (eliminating double LLM calls)...", "done": False}
            })

        # Automatically redirect to optimized approach
        try:
            # Use the optimized context retrieval instead
            context = await self.get_code_context(
                query=question,
                codebase_name=codebase_name,
                n_results=n_results,
                filter_type=filter_type,
                filter_language=filter_language,
                filter_file=filter_file,
                __event_emitter__=__event_emitter__
            )

            if context and "No relevant code context found" not in context:
                # Format for seamless integration with user query
                return f"""
{context}

Based on the above code context from codebase '{codebase_name}', please analyze and answer the following question:

{question}
"""
            else:
                return f"❌ No relevant code found in '{codebase_name}' for your question: {question}"

        except Exception as e:
            error_msg = f"❌ Code analysis failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Analysis failed: {str(e)}", "done": True}
                })
            return error_msg

    # --- CACHE MANAGEMENT TOOLS ---

    async def get_cache_stats(
        self,
        __event_emitter__=None
    ) -> str:
        """
        📊 Get intelligent cache performance statistics and insights.

        Shows cache hit rates, memory usage, and performance improvements
        from the intelligent caching system.
        """
        # Debug logging to detect inappropriate calls
        import traceback
        print("🔧 [DEBUG] get_cache_stats called! Stack trace:", flush=True)
        for line in traceback.format_stack()[-3:]:
            print(f"  {line.strip()}", flush=True)

        # Check if this is being called inappropriately for code analysis
        stack_str = ''.join(traceback.format_stack())
        if '__call__' not in stack_str and '_handle_management_query' not in stack_str:
            print("🔧 [DEBUG] get_cache_stats called directly, not through proper routing!", flush=True)

            # Check if this might be a memory management query that got misrouted
            # This is a fallback to help users who get misrouted by OpenWebUI
            if hasattr(self, '_last_user_query'):
                query = getattr(self, '_last_user_query', '')
                if any(term in query.lower() for term in ['memory management', 'memory allocation', 'memory handling', 'how is memory']):
                    print("🔧 [DEBUG] Detected memory management query misrouted to cache stats. Redirecting...", flush=True)
                    # Redirect to proper code analysis
                    try:
                        return await self.smart_code_context(query)
                    except Exception as e:
                        return f"❌ Query was misrouted. Please ask about memory management directly: {str(e)}"

            return "❌ This function is for cache statistics only. For code analysis questions, please ask your question normally and let the system route it appropriately."
        if not self.cache:
            return "❌ Caching is disabled. Enable it in settings for better performance."

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Retrieving cache statistics...", "done": False}
            })

        try:
            stats = self.cache.get_cache_stats()

            cache_report = f"""
🎯 **Intelligent Cache Performance Report**

## 📈 **Performance Metrics**
- **Overall Hit Rate**: {stats['hit_rate']:.1%} ({stats['performance_improvement']})
- **Memory Hit Rate**: {stats['memory_hit_rate']:.1%}
- **Total Requests**: {stats['total_requests']:,}

## 💾 **Memory Cache (L1 - Fastest)**
- **Entries**: {stats['memory_cache']['entries']:,} / {stats['memory_cache']['max_size']:,}
- **Total Size**: {stats['memory_cache']['total_size_bytes'] / 1024:.1f} KB
- **Avg Entry Size**: {stats['memory_cache']['avg_size_bytes']:.0f} bytes

## 💿 **Disk Cache (L2 - Persistent)**
- **Entries**: {stats['disk_cache']['entries']:,} / {stats['disk_cache']['max_size']:,}
- **Total Size**: {stats['disk_cache']['total_size_bytes'] / (1024*1024):.1f} MB
- **Avg Entry Size**: {stats['disk_cache']['avg_size_bytes'] / 1024:.1f} KB

## 🚀 **Performance Benefits**
- **Instant Responses**: {stats['memory_cache']['entries']} queries served instantly
- **Fast Responses**: {stats['disk_cache']['entries']} queries served from disk
- **Server Load Reduction**: {stats['hit_rate']:.1%} fewer API calls to server
- **Estimated Time Saved**: ~{stats['total_requests'] * stats['hit_rate'] * 2:.0f} seconds

## 💡 **Optimization Tips**
- Cache hit rate above 70% is excellent
- Memory cache provides instant responses
- Disk cache survives application restarts
- Clear cache if codebases are updated frequently
"""

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Cache statistics retrieved", "done": True}
                })

            return cache_report

        except Exception as e:
            return f"❌ Error retrieving cache statistics: {str(e)}"

    async def clear_cache(
        self,
        cache_type: str = "all",  # "memory", "disk", "all"
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🗑️ Clear cache entries to free up space or refresh data.

        :param cache_type: Type of cache to clear ("memory", "disk", "all")
        :param codebase_name: Clear cache for specific codebase only
        """
        if not self.cache:
            return "❌ Caching is disabled."

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Clearing {cache_type} cache...", "done": False}
            })

        try:
            if codebase_name:
                self.cache.invalidate_codebase(codebase_name)
                result = f"✅ Cleared cache for codebase: {codebase_name}"
            else:
                if cache_type in ["memory", "all"]:
                    self.cache.memory_cache.cache.clear()
                if cache_type in ["disk", "all"]:
                    self.cache.disk_cache.clear()
                result = f"✅ Cleared {cache_type} cache successfully"

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Cache cleared successfully", "done": True}
                })

            return result

        except Exception as e:
            return f"❌ Error clearing cache: {str(e)}"

    async def disable_cache(
        self,
        __event_emitter__=None
    ) -> str:
        """
        🚫 Disable caching entirely to ensure fresh results.
        """
        self.valves.enable_caching = False
        self.cache = None

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Caching disabled", "done": True}
            })

        return "✅ Caching disabled. All queries will now fetch fresh results from the server."

    async def enable_cache(
        self,
        __event_emitter__=None
    ) -> str:
        """
        ✅ Re-enable caching for better performance.
        """
        self.valves.enable_caching = True

        # Reinitialize cache
        cache_config = CacheConfig(
            memory_size=self.valves.cache_memory_size,
            memory_ttl=self.valves.cache_memory_ttl,
            disk_size=self.valves.cache_disk_size,
            disk_ttl=self.valves.cache_disk_ttl
        )
        self.cache = IntelligentCache(cache_config)

        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Caching enabled", "done": True}
            })

        return "✅ Caching enabled. Queries will now be cached for better performance."

    # --- Framework Integration Tools ---

    async def get_framework_status(
        self,
        __event_emitter__=None
    ) -> str:
        """
        🔧 Get comprehensive framework status including GPU infrastructure.

        Shows the status of the new framework integration, supported languages,
        GPU infrastructure, and processing capabilities.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Checking framework status...", "done": False}
            })

        try:
            response = requests.get(
                f"{self.valves.code_analyzer_server_url}/tools/framework_status",
                timeout=self.valves.request_timeout
            )

            if response.status_code == 200:
                status_data = response.json()

                # Format comprehensive status report
                report = f"""🚀 **Code Analysis Framework Status**

## 📊 **Core Framework**
- Framework Available: {'✅' if status_data.get('framework_available') else '❌'}
- Language Framework: {'✅' if status_data.get('language_framework_loaded') else '❌'}
- Supported Languages: {status_data.get('supported_languages', 0)}
- Supported Extensions: {status_data.get('supported_extensions', 0)}

## 🔧 **GPU Infrastructure**"""

                gpu_info = status_data.get('gpu_infrastructure', {})
                if gpu_info.get('gpu_infrastructure_available'):
                    report += f"""
- GPU Infrastructure: ✅ Available
- Available GPUs: {gpu_info.get('available_gpus', 0)}
- Supported GPU Types: {gpu_info.get('supported_gpu_types', 0)}

### 🎮 **Discovered GPUs:**"""

                    gpu_details = gpu_info.get('gpu_details', {})
                    if gpu_details:
                        for host, info in gpu_details.items():
                            report += f"""
- **{info['type']}** ({info['tier']}) at {host}"""
                    else:
                        report += "\n- No GPUs currently discovered"

                    # Processing recommendations
                    recommendations = status_data.get('processing_recommendations', {})
                    if recommendations.get('recommendations_available'):
                        rec_data = recommendations.get('recommendations', {})
                        if 'recommended_gpu' in rec_data:
                            gpu_rec = rec_data['recommended_gpu']
                            report += f"""

### ⚡ **Processing Recommendations (100 chunks):**
- Recommended GPU: **{gpu_rec['type']}** ({gpu_rec['tier']})
- Estimated Time: {rec_data.get('estimated_time_minutes', 0):.1f} minutes
- Cost Estimate: ${rec_data.get('cost_estimate', 0):.4f}"""
                else:
                    report += """
- GPU Infrastructure: ❌ Not Available
- Reason: """ + gpu_info.get('error', 'Unknown error')

                report += """

## 🎯 **Usage**
The framework provides enhanced query intelligence, GPU-accelerated processing,
and improved code analysis capabilities. Use normal queries - the system will
automatically use framework features when available.

*Framework integration active and ready for enhanced code analysis!*"""

                if __event_emitter__:
                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": "Framework status retrieved successfully", "done": True}
                    })

                return report
            else:
                error_msg = f"❌ **Framework Status Error**\nHTTP {response.status_code}: {response.text[:200]}"
                return error_msg

        except Exception as e:
            error_msg = f"❌ **Framework Status Error**\n{str(e)}\n\nPlease ensure the code analyzer server is running with framework support."
            return error_msg

    # --- Utility Tools (Enhanced) ---

    async def get_code_analyzer_help(
        self,
        __event_emitter__=None
    ) -> str:
        """
        ❓ Get comprehensive help and guidance for using the code analyzer system.
        
        Returns detailed information about available tools, workflows, and best practices
        for multi-codebase code analysis with optimization recommendations.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Loading help documentation...", "done": False}
            })
        
        help_text = """
🚀 **Codebase Analyzer - Complete Guide**

## 🤖 **AUTOMATIC OPTIMIZATION (How It Works)**

The system now **automatically optimizes** your queries! Here's how:

### **Seamless Usage:**
1. **select_codebase("my_project")** - Choose your codebase once
2. **Ask natural questions** - "How does the TCP socket work?"
3. **System automatically** injects optimized context
4. **OpenWebUI's LLM** analyzes context + your question in one pass

### **What Happens Behind the Scenes:**
```
User: "How does error handling work?"
↓
Tool: Detects code-related query
↓  
Tool: Auto-retrieves relevant context (NO LLM call)
↓
Tool: Injects context + reformats query  
↓
OpenWebUI LLM: Single analysis of context + question
↓
Result: Optimized response with 50% fewer LLM calls!
```

## 🎯 **Three Usage Modes**

### **1. AUTOMATIC (Recommended - Zero effort)**
```
select_codebase("networking")
"How does the TCP connection work?"  # ← Just ask naturally!
```
System automatically injects context and optimizes.

### **2. EXPLICIT (Manual control)**
```
inject_context_for_query("How does TCP work?", "networking")
```
Manually trigger context injection for any query.

### **3. DEPRECATED (Automatically redirected to optimized approach)**
```
ask_about_code("How does TCP work?", "networking")  # ← Now uses optimized single LLM approach
```

## 📋 **Setup Workflow**
1. **list_codebases()** - See what's available
2. **select_codebase("name")** - Choose your target  
3. **Ask questions naturally** - System handles optimization automatically

## 🛠️ **Management Tools**
• **list_codebases()** - View all projects
• **select_codebase(name)** - Choose active project
• **process_codebase(name)** - Index new source code
• **delete_codebase(name)** - Remove indexed data
• **get_codebase_stats(name)** - Project metrics
• **Complexity analysis** - Available through natural language queries

## 📊 **System Status Tools**
• **check_system_status()** - Basic health check (uses /health endpoint)
• **check_system_status(detailed=True)** - Comprehensive status (uses /status endpoint)
• **get_detailed_status()** - Convenience wrapper for detailed status
• **get_server_status()** - Direct access to /status endpoint

## ⚡ **Intelligent Cache Management**
• **get_cache_stats()** - View cache performance metrics and hit rates
• **clear_cache()** - Clear all cache entries
• **clear_cache("memory")** - Clear only memory cache
• **clear_cache("disk")** - Clear only disk cache
• **clear_cache(codebase_name="utils")** - Clear cache for specific codebase

**Cache Benefits:**
- **Instant responses** for repeated queries (memory cache)
- **Fast responses** for recent queries (disk cache)
- **Reduced server load** and improved performance
- **Automatic cleanup** of expired entries

## 🔧 **Manual Tools (If Needed)**
• **get_code_context(query, codebase)** - Raw context retrieval
• **inject_context_for_query(query, codebase)** - Manual context injection
• **search_code(query, codebase)** - Detailed search results

## ⚙️ **Configuration**
• **auto_context_injection**: Enable/disable automatic optimization
• **context_format**: "clean" (optimized) or "detailed" (verbose) - *Auto-selected per query*
• **current_codebase**: Cached selection for convenience

## 💡 **Smart Features**
• **Auto-detects** code vs non-code queries
• **Auto-optimizes** search parameters based on question type
• **Auto-selects** context format: detailed for analysis, clean for lookups
• **Auto-formats** context for optimal LLM consumption
• **Auto-routes** management queries to appropriate functions

## 🧠 **Automatic Context Format Selection**
The system now automatically chooses the best context format for your query:

**📋 Detailed Format** (for complex questions):
• "How does memory management work?"
• "Explain the error handling approach"
• "Compare these two algorithms"
• "Analyze the security implementation"

**🎯 Clean Format** (for simple lookups):
• "Find the malloc function"
• "Show me all classes"
• "List database functions"
• "What is the User class?"

## 🔍 **Search Optimization Tips**
• Use specific terms: "TCP socket initialization" 
• Mention languages: "Python error handling"
• Be specific: "memory allocation in C++" vs "memory"

## 📁 **Supported**
**Languages:** 27 languages including C, C++, Python, C#, JavaScript, TypeScript, Rust, Java, Go, SQL, TCL, Verilog, Bash, CommonLisp, EmacsLisp, Scheme, Lua, Make, JSON, YAML, XML, PHP, Perl, Markdown, HTML, Fortran, VHDL
**Types:** Functions, Classes, Methods, Structs, Enums, Templates, Namespaces

## ❓ **Getting Help**
**Specific Help Commands:**
• `"codebase analyzer help"` - This comprehensive guide
• `"analyzer help"` - Available tools and functions
• `"codebase help"` - How to work with codebases

**Generic Help:**
• `"help"` - General assistance (may conflict with other tools)
• `"what can you do"` - Capability overview

## 🚀 **Just Get Started!**
```
select_codebase("my_project")
"Show me how authentication works"  # ← That's it!
```

The system handles all optimization automatically while you focus on your analysis!
"""
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Help loaded", "done": True}
            })
        
        return help_text
    
    async def get_server_status(
        self,
        __event_emitter__=None
    ) -> str:
        """
        📊 Get detailed server status information.

        Uses the /status endpoint to get comprehensive server information including
        operational status, version, timestamps, and system metrics.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Retrieving server status...", "done": False}
            })

        try:
            response = requests.get(
                f"{self.valves.code_analyzer_server_url}/status",
                timeout=self.valves.request_timeout
            )
            response.raise_for_status()

            status_data = response.json()

            # Format detailed status report
            status_parts = ["📊 **Server Status Report**\n"]

            # Basic status info
            status_parts.append(f"🏷️ **Service**: {status_data.get('service', 'Unknown')}")
            status_parts.append(f"🔢 **Version**: {status_data.get('version', 'Unknown')}")
            status_parts.append(f"⏰ **Timestamp**: {status_data.get('timestamp', 'Unknown')}")
            status_parts.append(f"🟢 **Status**: {status_data.get('status', 'Unknown')}")
            status_parts.append(f"🔧 **Code Analyzer Service**: {status_data.get('code_analyzer_service', 'Unknown')}")

            # Codebase info
            available_codebases = status_data.get('available_codebases', 0)
            current_codebase = status_data.get('current_codebase', 'None')
            status_parts.append(f"📚 **Available Codebases**: {available_codebases}")
            status_parts.append(f"🎯 **Current Codebase**: {current_codebase}")

            # Ollama info
            ollama_status = status_data.get('ollama', 'Unknown')
            ollama_models = status_data.get('ollama_models', 0)
            status_parts.append(f"🤖 **Ollama**: {ollama_status}")
            status_parts.append(f"🧠 **Available Models**: {ollama_models}")

            # Overall health
            overall = status_data.get('overall', 'Unknown')
            status_parts.append(f"💚 **Overall Health**: {overall}")

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Status retrieved successfully", "done": True}
                })

            return "\n".join(status_parts)

        except Exception as e:
            error_msg = f"❌ Failed to get server status: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Status check failed: {str(e)}", "done": True}
                })
            return error_msg

    async def check_system_status(
        self,
        detailed: bool = False,
        __event_emitter__=None
    ) -> str:
        """
        🔧 Check the health and status of the Code Analyzer analysis system.

        Args:
            detailed: If True, uses /status endpoint for comprehensive info.
                     If False, uses /health endpoint for basic health check.

        Verifies connectivity and operational status of all system components.
        """
        # If detailed status requested, use the new get_server_status method
        if detailed:
            return await self.get_server_status(__event_emitter__)

        # Otherwise, use the original health check
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Checking system status...", "done": False}
            })

        try:
            response = requests.get(
                f"{self.valves.code_analyzer_server_url}/health",
                timeout=self.valves.request_timeout
            )
            response.raise_for_status()

            health_data = response.json()
            
            # Format status report
            status_parts = ["🔧 **Code Analyzer System Status**\n"]
            
            components = {
                "code_analyzer_server": "Code Analyzer Server",
                "code_analyzer_service": "Code Analyzer Service", 
                "ollama": "Ollama AI",
                "source_code_directory": "Source Directory",
                "chroma_db_directory": "Database Storage"
            }
            
            all_healthy = True
            for key, name in components.items():
                status = health_data.get(key, "unknown")
                if status in ["healthy", "available", "connected"]:
                    status_parts.append(f"✅ **{name}**: Online")
                else:
                    status_parts.append(f"❌ **{name}**: {status}")
                    all_healthy = False
            
            # Add summary info
            codebases = health_data.get("available_codebases", 0)
            current = health_data.get("current_codebase", "None")
            
            status_parts.extend([
                f"\n📚 **Available Codebases**: {codebases}",
                f"🎯 **Current Selection**: {current}",
                f"🎯 **Tool Cached Selection**: {self.valves.current_codebase or 'None'}",
                f"⚙️ **Context Format**: {self.valves.context_format}",
                f"🤖 **Auto Context Injection**: {'Enabled' if self.valves.auto_context_injection else 'Disabled'}",
                f"\n{'✅ System Ready' if all_healthy else '⚠️ Issues Detected'}"
            ])
            
            result = "\n".join(status_parts)
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status", 
                    "data": {"description": "Status check completed", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ System Status Check Failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Status check failed: {str(e)}", "done": True}
                })
            return error_msg

    async def get_detailed_status(
        self,
        __event_emitter__=None
    ) -> str:
        """
        📊 Get comprehensive server status (convenience wrapper).

        This is a convenience function that calls check_system_status(detailed=True)
        to get comprehensive server information using the /status endpoint.
        """
        return await self.check_system_status(detailed=True, __event_emitter__=__event_emitter__)

    # --- Helper Methods for Optimization ---
    
    def _analyze_query_for_context(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine optimal search parameters (no LLM needed)."""
        query_lower = query.lower()

        # Determine number of results based on query complexity (optimized for 16k context)
        if any(word in query_lower for word in ['memory management', 'memory allocation', 'memory handling', 'malloc', 'free', 'tmwmem']):
            n_results = 20  # Memory management queries need comprehensive context
        elif any(word in query_lower for word in ['compare', 'difference', 'vs', 'versus', 'between']):
            n_results = 15  # More context for comparisons (was 8)
        elif any(word in query_lower for word in ['how', 'why', 'explain', 'describe', 'understand']):
            n_results = 12  # Good context for explanations (was 6)
        elif any(word in query_lower for word in ['find', 'show', 'get', 'locate']):
            n_results = 6   # Fewer results for direct searches (was 3)
        elif any(word in query_lower for word in ['list', 'all', 'every', 'complete']):
            n_results = 20  # More comprehensive results for listing queries (was 10)
        elif any(word in query_lower for word in ['analyze', 'analysis', 'review', 'audit', 'examine']):
            n_results = 18  # Deep analysis queries need lots of context
        elif any(word in query_lower for word in ['architecture', 'design', 'structure', 'overview']):
            n_results = 16  # Architectural queries need broad context
        else:
            n_results = 10  # Default (was 5)

        # Determine filters based on query content
        params: Dict[str, Any] = {'n_results': n_results}

        # NEW: Determine optimal context format based on query complexity
        params['optimal_context_format'] = self._determine_optimal_context_format(query_lower)
        
        # CONSERVATIVE Language detection - only apply when very specific
        # Removed overly aggressive patterns that cause false positives
        if any(word in query_lower for word in ['.py', 'import python', 'def ', 'python code']):
            params['filter_language'] = 'python'
        elif any(word in query_lower for word in ['c++ code', '.cpp', 'template', 'namespace', '::']):
            params['filter_language'] = 'cpp'
        elif any(word in query_lower for word in ['c# code', 'csharp', '.cs', 'using system']):
            params['filter_language'] = 'csharp'
        elif any(word in query_lower for word in ['.js', 'javascript code', 'node.js', 'npm', 'react']):
            params['filter_language'] = 'javascript'
        elif any(word in query_lower for word in ['.ts', 'typescript code', 'interface']):
            params['filter_language'] = 'typescript'
        elif any(word in query_lower for word in ['.rs', 'rust code', 'cargo']):
            params['filter_language'] = 'rust'
        elif any(word in query_lower for word in ['.java', 'java code', 'public class', 'import java']):
            params['filter_language'] = 'java'
        elif any(word in query_lower for word in ['.go', 'go code', 'goroutine']):
            params['filter_language'] = 'go'
        elif any(word in query_lower for word in ['.sql', 'sql code', 'select', 'insert', 'update', 'delete']):
            params['filter_language'] = 'sql'
        # Removed other languages that were causing false positives

        # NOTE: Removed overly aggressive language detection for:
        # - 'struct' (common in C, not Rust-specific)
        # - 'module' (common word, not Verilog-specific)
        # - 'function' (common word, not language-specific)
        # - Generic words that appear in many contexts
        
        # CONSERVATIVE Type detection - only apply when very specific
        # DISABLED: Overly aggressive type filtering that blocks results
        #
        # The following patterns were causing too many false positives:
        # - 'function' in "show me functions" blocked function results
        # - 'struct' in general queries blocked struct-related code
        # - 'module' in architecture queries blocked results
        #
        # Type filtering should only be used for very specific requests
        # where the user explicitly wants to filter by type

        # Only apply type filters for very explicit requests
        if query_lower.startswith('find all functions') or 'only functions' in query_lower:
            params['filter_type'] = 'function'
        elif query_lower.startswith('find all classes') or 'only classes' in query_lower:
            params['filter_type'] = 'class'
        elif query_lower.startswith('find all structs') or 'only structs' in query_lower:
            params['filter_type'] = 'struct_specifier'

        # File pattern detection - also more conservative
        if any(word in query_lower for word in ['.h files', 'header files only', 'include files']):
            params['filter_type'] = 'header'

        # Special handling for memory management queries - expand search terms
        if any(term in query_lower for term in ['memory management', 'memory allocation', 'memory handling', 'how is memory managed']):
            # For memory management queries, we want to cast a wider net
            params['expanded_search'] = True
            params['n_results'] = max(params.get('n_results', 10), 20)  # Ensure we get enough results

        return params

    def _determine_optimal_context_format(self, query_lower: str) -> str:
        """
        🧠 NEW: Automatically determine optimal context format based on query complexity.

        Returns 'detailed' for complex analytical questions that need full context,
        'clean' for simple lookup questions that benefit from streamlined format.
        """

        # Complex analytical questions that need detailed context with full metadata
        complex_analytical_indicators = [
            # Deep analysis questions
            'how does', 'how is', 'how are', 'why does', 'why is', 'why are',
            'explain how', 'explain why', 'explain the', 'analyze', 'analysis',
            'compare', 'comparison', 'what is the difference', 'difference between',
            'vs', 'versus', 'contrast',

            # Architecture and design questions
            'architecture', 'design pattern', 'design', 'structure', 'organization',
            'implementation details', 'implementation', 'approach', 'strategy',

            # Technical deep-dive topics
            'algorithm', 'performance', 'optimization', 'efficiency',
            'memory management', 'memory', 'allocation', 'deallocation',
            'error handling', 'exception handling', 'error recovery',
            'security', 'authentication', 'authorization', 'encryption',
            'concurrency', 'threading', 'synchronization', 'locking',
            'database', 'transaction', 'persistence', 'storage',

            # Process and workflow questions
            'workflow', 'process', 'lifecycle', 'sequence', 'flow',
            'interaction', 'communication', 'protocol', 'interface'
        ]

        # Simple lookup questions that work well with clean format
        simple_lookup_indicators = [
            # Direct search/find operations
            'find', 'show me', 'get', 'locate', 'search for',
            'list', 'display', 'print', 'output',

            # Simple existence checks
            'what functions', 'what methods', 'what classes', 'what variables',
            'which functions', 'which methods', 'which classes',
            'does it have', 'is there', 'are there',

            # Simple property queries
            'name of', 'type of', 'value of', 'size of',

            # Listing operations (high priority - should override other indicators)
            'list all', 'list the', 'list functions', 'list methods', 'list classes',
            'list database', 'list network', 'list memory', 'list error'
        ]

        # Check for simple indicators FIRST (list commands should have high priority)
        if any(indicator in query_lower for indicator in simple_lookup_indicators):
            return 'clean'

        # Then check for complex indicators
        if any(indicator in query_lower for indicator in complex_analytical_indicators):
            return 'detailed'

        # Default logic based on question words and patterns
        question_words = ['how', 'why', 'what', 'when', 'where', 'which', 'who']
        has_question_word = any(word in query_lower for word in question_words)

        # Questions typically need more context than searches
        if has_question_word:
            # But distinguish between simple "what is X" vs complex "how does X work"
            if any(pattern in query_lower for pattern in ['what is', 'what are', 'who is', 'who are']):
                return 'clean'  # Simple definition questions
            else:
                return 'detailed'  # Complex analytical questions

        # Default to clean for non-question queries (likely searches)
        return 'clean'

    def _format_context_for_openwebui(self, search_result: str) -> str:
        """
        PRIVATE METHOD: Format search results for optimal OpenWebUI context injection.
        This method should NOT be called directly by OpenWebUI - it's an internal helper.
        """
        if not search_result:
            return "No relevant code context found for this query."

        # Ensure search_result is a string (additional safety check)
        if not isinstance(search_result, str):
            search_result = str(search_result) if search_result is not None else ""

        if "No relevant code found" in search_result:
            return "No relevant code context found for this query."

        # Extract clean code sections from the verbose search result
        try:
            lines = search_result.split('\n')
        except AttributeError as e:
            # Fallback if split still fails somehow
            return f"Error processing search result: {str(e)}"
        context_sections = []
        current_section: list[str] = []
        current_metadata = ""
        in_code_block = False
        
        for line in lines:
            # Detect result headers
            if line.startswith('**Result'):
                # Save previous section
                if current_section and in_code_block:
                    context_sections.append({
                        'metadata': current_metadata,
                        'code': '\n'.join(current_section)
                    })
                current_section = []
                in_code_block = False
                current_metadata = ""
            
            # Extract file metadata
            elif line.startswith('📁 **File**'):
                # Clean up the metadata line
                current_metadata = line.replace('📁 **File**: ', '').replace('**', '').strip()
            
            # Detect code block boundaries
            elif line.startswith('```'):
                if in_code_block:
                    # End of code block - save the section
                    if current_section:
                        context_sections.append({
                            'metadata': current_metadata,
                            'code': '\n'.join(current_section)
                        })
                    current_section = []
                    in_code_block = False
                else:
                    # Start of code block
                    in_code_block = True
            
            # Collect code lines
            elif in_code_block and line.strip():
                current_section.append(line)
        
        # Handle final section
        if current_section and in_code_block:
            context_sections.append({
                'metadata': current_metadata,
                'code': '\n'.join(current_section)
            })
        
        # Return original if parsing failed
        if not context_sections:
            return search_result
        
        # Format for OpenWebUI with clean structure
        if self.valves.context_format == "clean":
            formatted_parts = ["=== RELEVANT CODE CONTEXT ===\n"]
            
            for i, section in enumerate(context_sections, 1):
                # Add minimal metadata
                metadata = section['metadata']
                if metadata:
                    formatted_parts.append(f"Context {i} - {metadata}:")
                else:
                    formatted_parts.append(f"Context {i}:")
                
                # Add clean code
                formatted_parts.append(f"```\n{section['code']}\n```\n")
            
            formatted_parts.append("=== END CONTEXT ===")
            return '\n'.join(formatted_parts)
        
        else:
            # Return structured but detailed format
            formatted_parts = ["=== CODE ANALYSIS CONTEXT ===\n"]
            
            for i, section in enumerate(context_sections, 1):
                formatted_parts.append(f"**Source {i}:**")
                if section['metadata']:
                    formatted_parts.append(f"File: {section['metadata']}")
                formatted_parts.append(f"```\n{section['code']}\n```\n")
            
            formatted_parts.append("=== END CONTEXT ===")
            return '\n'.join(formatted_parts)
    
    def _detect_code_related_query(self, query: str) -> bool:
        """Detect if a query is code-related for auto-context injection using configurable keywords."""
        query_lower = query.lower()

        # Get keywords from valves configuration
        code_keywords = []

        # Combine all keyword categories
        keyword_categories = [
            self.valves.keywords_programming,
            self.valves.keywords_technical,
            self.valves.keywords_analysis,
            self.valves.keywords_protocol,
            self.valves.keywords_languages
        ]

        for category in keyword_categories:
            if category:  # Check if category is not empty
                code_keywords.extend([kw.strip() for kw in category.split(',') if kw.strip()])

        # Get code-related question patterns from valves
        code_patterns = []
        if self.valves.patterns_code_questions:
            code_patterns = [pattern.strip() for pattern in self.valves.patterns_code_questions.split(',') if pattern.strip()]

        # Check for direct keyword matches
        if any(keyword in query_lower for keyword in code_keywords):
            return True

        # Check for code-related question patterns combined with technical context
        if any(pattern in query_lower for pattern in code_patterns):
            # Additional context check for technical terms (use programming keywords as context)
            programming_keywords = []
            if self.valves.keywords_programming:
                programming_keywords = [kw.strip() for kw in self.valves.keywords_programming.split(',') if kw.strip()]

            # Add some core technical context terms
            technical_context = programming_keywords + ['system', 'data', 'structure', 'format']
            if any(tech_term in query_lower for tech_term in technical_context):
                return True

        # Check for function/identifier patterns
        import re

        # Pattern for C-style function names (prefix_functionname)
        if re.search(r'\b[a-z]+_[a-z_]+\b', query_lower):
            return True

        # Pattern for UPPERCASE constants/macros
        if re.search(r'\b[A-Z][A-Z_]+[A-Z]\b', query):
            return True

        # Pattern for camelCase identifiers
        if re.search(r'\b[a-z]+[A-Z][a-zA-Z]*\b', query):
            return True

        return False

    def _get_matched_keywords(self, query: str) -> list:
        """Get list of keywords that matched in the query for debugging purposes."""
        query_lower = query.lower()
        matched = []

        # Check all keyword categories
        keyword_categories = [
            ("programming", self.valves.keywords_programming),
            ("technical", self.valves.keywords_technical),
            ("analysis", self.valves.keywords_analysis),
            ("protocol", self.valves.keywords_protocol),
            ("languages", self.valves.keywords_languages)
        ]

        for category_name, category_keywords in keyword_categories:
            if category_keywords:
                keywords = [kw.strip() for kw in category_keywords.split(',') if kw.strip()]
                for keyword in keywords:
                    if keyword in query_lower:
                        matched.append(f"{keyword}({category_name})")

        # Check patterns
        if self.valves.patterns_code_questions:
            patterns = [pattern.strip() for pattern in self.valves.patterns_code_questions.split(',') if pattern.strip()]
            for pattern in patterns:
                if pattern in query_lower:
                    matched.append(f"{pattern}(pattern)")

        return matched

    async def _try_remote_ollama(self, user_query: str, __event_emitter__):
        """Try to handle query with remote Ollama if applicable"""
        try:
            # Check if we can detect remote model context
            # This is a simplified detection - in practice you might get model info from OpenWebUI context

            # For now, we'll use a simple heuristic or configuration
            # You could extend this to detect model names like "LYNN-PC.llama3.1:latest"

            # Example: Check if query mentions remote server or if configured to prefer remote
            if "lynn-pc" in user_query.lower() or "t5810" in user_query.lower():
                server = "lynn-pc" if "lynn-pc" in user_query.lower() else "t5810"

                if server in self.remote_analyzers:
                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": f"🌐 Using remote {server} for analysis..."}
                    })

                    # Preprocess query for remote Ollama too
                    processed_query = self._preprocess_query(user_query)
                    result = self.remote_analyzers[server].chat_with_tools(processed_query)

                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": f"✅ Remote {server} analysis complete", "done": True}
                    })

                    return result

        except Exception as e:
            print(f"❌ Remote Ollama error: {e}", flush=True)
            # Fall back to normal processing

        return None

    # --- AUTOMATIC OPTIMIZATION SYSTEM ---
    
    def _preprocess_query(self, query: str) -> str:
        """Preprocess queries to prevent hallucination on 'show' queries"""
        # Convert "show X in Y codebase" to "find X in Y codebase" to prevent hallucination
        if query.lower().startswith("show ") and " in " in query.lower() and "codebase" in query.lower():
            # Replace "show" with "find" and add explicit instruction
            modified_query = query.replace("show ", "find ", 1)
            modified_query += ". Use the query_codebase tool to search the codebase and show actual code results."
            print(f"🔧 [QUERY PREPROCESSING] Converted 'show' to 'find': '{query}' → '{modified_query[:80]}...'", flush=True)
            return modified_query
        return query

    async def __call__(self, user_query: str = "", __event_emitter__=None, **_kwargs):
        """
        🚀 AUTOMATIC OPTIMIZATION: Main entry point that intelligently routes queries.

        🔧 DEBUG MARKER: FIXED_VERSION_2025_07_19_v2 - This should appear in OpenWebUI logs

        This method is automatically called by OpenWebUI and determines whether to:
        1. Auto-inject context for code queries (OPTIMIZED)
        2. Route to specific tools based on query intent
        3. Return empty for non-code queries
        """
        # Preprocess query to prevent hallucination
        user_query = self._preprocess_query(user_query)

        # Log incoming queries for persistence tracking
        print(f"🔧 [QUERY] Processing: '{user_query[:50]}...'", flush=True)

        # Check if this is a remote model that should use direct Ollama
        if self.valves.enable_remote_ollama and __event_emitter__:
            # Try to detect remote model from context
            remote_result = await self._try_remote_ollama(user_query, __event_emitter__)
            if remote_result:
                return remote_result

        # Store the query for misrouting detection
        self._last_user_query = user_query

        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        if not user_query.strip():
            return ""

        # Check for management queries first (before checking selected codebase)
        intent = self._detect_query_intent(user_query)

        # DEBUG: Log the intent detection for troubleshooting
        print(f"🔧 [DEBUG MARKER] FIXED_VERSION_2025_07_19_v2: Query='{user_query[:50]}...', Intent='{intent}'", flush=True)

        if intent == "help":
            # Route to help documentation
            return await self.get_code_analyzer_help()

        if intent == "codebase_management":
            # Route to appropriate management function (handles selection, listing, etc.)
            result = await self._handle_management_query(user_query)

            # Apply comprehensive chunk clarification to management results
            result = self._ensure_chunk_clarification(result)

            return result

        # In stateless architecture, client maintains codebase selection
        # No server sync needed - client is the source of truth

        if not self.valves.current_codebase:
            # For non-management queries without a selected codebase, suggest selection
            if self._is_code_related_query(user_query):
                return await self._suggest_codebase_selection()
            # Return empty string to let OpenWebUI handle non-code queries
            return ""

        # Handle code analysis queries (intent already detected above)
        if intent == "code_analysis" and self.valves.auto_context_injection:
            # Check if this is a complexity analysis query
            query_lower = user_query.lower()
            if any(phrase in query_lower for phrase in ['analyze complexity', 'code complexity analysis', 'complexity analysis']):
                # Route to dedicated complexity analysis function
                print(f"🔧 [DEBUG] Processing complexity analysis query in code_analysis intent: {user_query}", flush=True)

                # Try to extract codebase name from query
                codebase_name = None
                try:
                    available_codebases = await self._get_available_codebase_names()
                    import re
                    for codebase in available_codebases:
                        if len(codebase) > 50:
                            continue
                        escaped_name = re.escape(codebase.lower())
                        if '-' in codebase:
                            patterns = [
                                r'(?:^|\s)' + escaped_name + r'(?:\s|$)',
                                r'"' + escaped_name + r'"',
                                r"'" + escaped_name + r"'",
                            ]
                            if any(re.search(pattern, query_lower) for pattern in patterns):
                                codebase_name = codebase
                                break
                        else:
                            pattern = r'\b' + escaped_name + r'\b'
                            if re.search(pattern, query_lower):
                                codebase_name = codebase
                                break
                except Exception:
                    pass

                result = await self._analyze_complexity(codebase_name, __event_emitter__=__event_emitter__)
            else:
                # AUTOMATICALLY inject optimized context for regular code analysis
                result = await self._auto_inject_optimized_context(user_query, __event_emitter__=__event_emitter__)

            # Apply comprehensive chunk clarification to analysis results
            result = self._ensure_chunk_clarification(result)

            return result

        # For non-code queries, return empty (let OpenWebUI handle normally)
        return ""
    
    async def _auto_inject_optimized_context(self, user_query: str, __event_emitter__=None) -> str:
        """
        🎯 CORE OPTIMIZATION: Automatically inject context using optimized pipeline.
        """
        try:
            # Initialize chunk tracking for this session
            self._total_chunks_found = 0
            self._context_calls_made = 0

            context = await self.smart_code_context(
                user_query,
                self.valves.current_codebase,
                __event_emitter__=__event_emitter__
            )

            # Emit final consolidated status with total chunks
            if __event_emitter__ and hasattr(self, '_total_chunks_found'):
                total_chunks = getattr(self, '_total_chunks_found', 0)
                if total_chunks > 0:
                    description = f"Code context retrieved successfully ({total_chunks} chunk{'s' if total_chunks != 1 else ''} found)"
                else:
                    description = "No relevant code context found for this query"

                await __event_emitter__({
                    "type": "status",
                    "data": {"description": description, "done": True}
                })

            if context and "No relevant code context found" not in context:
                # Format for seamless integration with user query
                return f"""
{context}

Based on the above code context from codebase '{self.valves.current_codebase}', please analyze and answer the following question:

{user_query}
"""

            return ""

        except Exception as e:
            return f"<!-- Context injection failed: {str(e)} -->"
    
    def _is_code_related_query(self, query: str) -> bool:
        """Check if a query is related to code analysis"""
        code_keywords = [
            'function', 'class', 'method', 'variable', 'code', 'implementation',
            'algorithm', 'memory', 'allocation', 'error', 'bug', 'debug',
            'find', 'search', 'show', 'explain', 'how does', 'what is'
        ]
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in code_keywords)

    def _detect_query_intent(self, query: str) -> str:
        """Detect the intent of a user query to enable automatic optimization using configurable phrases."""
        query_lower = query.lower()

        # Help intents - get phrases from valves configuration
        help_phrases = []
        if self.valves.phrases_help:
            help_phrases = [phrase.strip() for phrase in self.valves.phrases_help.split(',') if phrase.strip()]

        if any(phrase in query_lower for phrase in help_phrases) or (
            # Generic help only if it's a standalone query (to avoid conflicts)
            query_lower.strip() in ['help', 'get help', 'show help', 'guide', 'documentation', 'instructions', 'tutorial', 'usage'] or
            query_lower.startswith('what can') or query_lower.startswith('how do i')
        ):
            return "help"

        # Management intents - get phrases from valves configuration
        management_phrases = []
        if self.valves.phrases_management:
            management_phrases = [phrase.strip() for phrase in self.valves.phrases_management.split(',') if phrase.strip()]

        # Check for exact management phrases first
        if any(phrase in query_lower for phrase in management_phrases):
            return "codebase_management"

        # Check for standalone stats/statistics queries (without code context)
        if query_lower.strip() in ['stats', 'statistics', 'status'] or (
            ('stats' in query_lower or 'statistics' in query_lower or 'status' in query_lower) and
            not self._detect_code_related_query(query)
        ):
            return "codebase_management"

        # Code analysis intents (now includes complexity analysis)
        if self._detect_code_related_query(query):
            return "code_analysis"

        return "general"
    
    async def _handle_management_query(self, query: str) -> str:
        """Auto-route management queries to appropriate functions."""
        query_lower = query.lower()

        # For codebase selection queries, don't load persistent selection first
        # as it would override the new selection being made
        if not any(phrase in query_lower for phrase in ['select codebase', 'choose codebase', 'switch codebase']):
            # Only load persistent selection for non-selection queries
            self._ensure_codebase_selection_loaded()

        # Check for stats/statistics queries first (more specific)
        if 'stats' in query_lower or 'statistics' in query_lower:
            # Try to extract codebase name from query using actual available codebases
            codebase_name = None
            try:
                # Get list of available codebases
                available_codebases = await self._get_available_codebase_names()

                # Check if any codebase name appears in the query (as whole word)
                import re
                for codebase in available_codebases:
                    # Skip extremely long titles that sometimes get extracted (but allow reasonable project names)
                    if len(codebase) > 50:
                        continue
                    # Use word boundary to match whole words only
                    # For hyphenated names, we need special handling since \b doesn't work well with hyphens
                    escaped_name = re.escape(codebase.lower())
                    if '-' in codebase:
                        # For hyphenated names, look for exact match with various boundaries
                        patterns = [
                            r'(?:^|\s)' + escaped_name + r'(?:\s|$)',  # Space-bounded (most reliable)
                            r'"' + escaped_name + r'"',  # Double quoted
                            r"'" + escaped_name + r"'",  # Single quoted
                            r'(?:^|\s)' + escaped_name + r'(?=\s|$|[^\w-])',  # Followed by non-word/non-hyphen
                            escaped_name + r'(?=\s|$)',  # End of string or followed by space
                        ]
                        if any(re.search(pattern, query_lower) for pattern in patterns):
                            codebase_name = codebase
                            break
                    else:
                        # Standard word boundary for simple names
                        pattern = r'\b' + escaped_name + r'\b'
                        if re.search(pattern, query_lower):
                            codebase_name = codebase
                            break
            except Exception:
                # If we can't get the list, fall back to current codebase
                pass

            # Use extracted name or current codebase
            if codebase_name:
                return await self.get_codebase_stats(codebase_name)
            elif self.valves.current_codebase:
                return await self.get_codebase_stats(self.valves.current_codebase)
            else:
                return "❌ No codebase selected for statistics. Please select a codebase first using 'select codebase <name>' or specify the codebase in your query like 'get stats for utils'."
        
        elif 'status' in query_lower:
            # Check if detailed status is requested
            if any(phrase in query_lower for phrase in ['detailed', 'comprehensive', 'full', 'complete']):
                return await self.get_detailed_status()
            else:
                return await self.check_system_status()

        elif any(phrase in query_lower for phrase in ['select codebase', 'choose codebase', 'switch codebase']):
            # Extract codebase name from the query
            # Handle patterns like "select codebase utils", "choose codebase z80emu", etc.
            print(f"🔧 [DEBUG] Processing select codebase query: {query}", flush=True)
            words = query.split()
            codebase_name = None

            # Find the word(s) after "codebase" - handle hyphenated names
            for i, word in enumerate(words):
                if word.lower() == 'codebase' and i + 1 < len(words):
                    # For hyphenated names, we might need to take the rest of the query
                    # or look for quoted strings
                    remaining_words = words[i + 1:]
                    if len(remaining_words) == 1:
                        codebase_name = remaining_words[0]
                    else:
                        # Check if it's a quoted name
                        remaining_text = ' '.join(remaining_words)
                        if remaining_text.startswith('"') and remaining_text.endswith('"'):
                            codebase_name = remaining_text.strip('"')
                        elif remaining_text.startswith("'") and remaining_text.endswith("'"):
                            codebase_name = remaining_text.strip("'")
                        else:
                            # For hyphenated names, take the first word (most common case)
                            codebase_name = remaining_words[0]
                    break

            print(f"🔧 [DEBUG] Extracted codebase name: {codebase_name}", flush=True)
            if codebase_name:
                # Call the select_codebase function directly
                print(f"🔧 [DEBUG] Calling select_codebase with: {codebase_name}", flush=True)
                result = await self.select_codebase(codebase_name)
                print(f"🔧 [DEBUG] select_codebase returned: {result[:100]}...", flush=True)
                return result
            else:
                return "❌ Please specify a codebase name. Example: 'select codebase utils'"

        # Check for list/show/available queries (after more specific checks)
        elif any(phrase in query_lower for phrase in ['list codebase', 'show codebase', 'available codebase', 'list all', 'show all']):
            # Make sure it's not a stats query that happens to contain these words
            if not ('stats' in query_lower or 'statistics' in query_lower):
                return await self.list_codebases()

        return ""
    
    async def _suggest_codebase_selection(self) -> str:
        """Helper to suggest codebase selection when none is active."""
        available = await self.list_codebases()
        return f"""No codebase is currently selected for code analysis.

{available}

Use `select_codebase("name")` to choose a codebase, then ask your code questions normally."""

    # --- EXPLICIT CONTEXT INJECTION (Alternative Approach) ---
    
    async def inject_context_for_query(
        self,
        user_query: str,
        codebase_name: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🔄 Explicitly inject context for any query (can be called manually).
        
        This provides a middle-ground approach where users can explicitly request
        context injection without using the deprecated ask_about_code function.
        """
        target_codebase = codebase_name or self.valves.current_codebase
        
        if not target_codebase:
            return "❌ No codebase selected. Use select_codebase() first."
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Injecting context for query...", "done": False}
            })
        
        try:
            context = await self.smart_code_context(
                user_query, 
                target_codebase,
                __event_emitter__=__event_emitter__
            )
            
            if context and "No relevant code context found" not in context:
                formatted_query = f"""
Here is relevant code context for your analysis:

{context}

Now please answer this question based on the code context above:
{user_query}
"""
                if __event_emitter__:
                    await __event_emitter__({
                        "type": "status",
                        "data": {"description": "Context injected successfully", "done": True}
                    })
                
                return formatted_query
            else:
                return f"No relevant code context found for: {user_query}"
            
        except Exception as e:
            error_msg = f"❌ Context injection failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Context injection failed: {str(e)}", "done": True}
                })
            return error_msg
    
    # --- Raw/Direct Access Methods ---
    
    async def raw_search(
        self,
        query: str,
        codebase_name: Optional[str] = None,
        n_results: int = 3,
        __event_emitter__=None
    ) -> str:
        """
        🔎 Raw code search with minimal formatting.
        
        Returns search results in the cleanest possible format for context injection.
        Useful for integration with other tools or custom processing.
        """
        target_codebase = codebase_name or self.valves.current_codebase
        
        if not target_codebase:
            return "No codebase selected."
        
        try:
            # Use the backend search endpoint directly for minimal overhead
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/search",
                json={
                    "query": query,
                    "n_results": n_results
                },
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            
            # Format results with absolute minimal formatting
            results = data.get("results", [])
            if not results:
                return "No relevant code found."
            
            context_parts = []
            for result in results:
                metadata = result.get('metadata', {})
                content = result.get('content', '')
                
                file_path = metadata.get('relative_path', 'Unknown')
                chunk_type = metadata.get('type', 'code')
                
                # Minimal context format
                context_parts.append(f"// {file_path} ({chunk_type})")
                context_parts.append(content.strip())
                context_parts.append("")  # Empty line separator
            
            return '\n'.join(context_parts)
            
        except Exception as e:
            return f"Raw search failed: {str(e)}"

    def _add_chunk_clarification(self, stats_text: str) -> str:
        """Add clarification about what chunks mean in statistics"""
        if "chunks" in stats_text.lower() and "text segments" not in stats_text.lower():
            # Add a clarifying note about chunks vs files (only if not already present)
            clarification = "\n\n📋 **Important**: 'Chunks' are text segments created by splitting source files for vector search and embedding. Each source file is divided into multiple chunks for better search granularity. The chunk count (479) represents text segments, not the number of actual files (43)."
            return stats_text + clarification
        return stats_text

    def _ensure_chunk_clarification(self, response_text: str) -> str:
        """Ensure any response mentioning chunks includes clarification"""
        if not response_text:
            return response_text

        # Check if response mentions chunks but lacks proper clarification
        has_chunks = "chunks" in response_text.lower()
        has_clarification = any(term in response_text.lower() for term in [
            "text segments", "splitting source files", "vector search",
            "search granularity", "not the number of actual files"
        ])

        if has_chunks and not has_clarification:
            return self._add_chunk_clarification(response_text)

        return response_text

    async def _get_available_codebase_names(self) -> list:
        """Get list of available codebase names"""
        try:
            response = requests.post(f"{self.valves.code_analyzer_server_url}/tools/list_codebases", json={}, timeout=10)
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", "")

                # Extract codebase names from the result string
                # Look for patterns like "1. **utils**" or "• utils"
                import re
                patterns = [
                    r'\*\*([^*]+)\*\*',  # **codebase_name**
                    r'• ([^\n]+)',       # • codebase_name
                    r'\d+\.\s*([^\n]+)', # 1. codebase_name
                ]

                codebase_names = []
                for pattern in patterns:
                    matches = re.findall(pattern, result)
                    if matches:
                        codebase_names.extend([match.strip() for match in matches])
                        break  # Use first successful pattern

                # Clean up names (remove extra formatting)
                cleaned_names = []
                for name in codebase_names:
                    clean_name = re.sub(r'[^\w_-]', '', name)  # Keep only alphanumeric, underscore, dash
                    if clean_name and len(clean_name) > 1:
                        cleaned_names.append(clean_name)

                return cleaned_names if cleaned_names else []
            else:
                # Return empty list if API fails - no hardcoded fallbacks
                return []
        except Exception:
            # Return empty list if anything fails - no hardcoded fallbacks
            return []

    # --- Configuration Helpers ---

    async def test_format_selection(
        self,
        test_query: str,
        __event_emitter__=None
    ) -> str:
        """
        🧪 Test the automatic context format selection for a query.

        Shows what format would be automatically selected and why.
        """
        if not test_query.strip():
            return "❌ Please provide a test query."

        query_lower = test_query.lower()
        optimal_format = self._determine_optimal_context_format(query_lower)

        # Analyze why this format was chosen
        analysis = self._explain_format_choice(query_lower, optimal_format)

        return f"""🧪 **Automatic Format Selection Test**

**Query:** "{test_query}"

**Selected Format:** {optimal_format.upper()}

**Reasoning:** {analysis}

**What this means:**
{"• Full context with metadata and detailed explanations" if optimal_format == 'detailed' else "• Streamlined context optimized for quick answers"}
{"• Better for analytical and complex questions" if optimal_format == 'detailed' else "• Better for lookups and simple questions"}
{"• More comprehensive but potentially verbose" if optimal_format == 'detailed' else "• Cleaner but may miss some context details"}

💡 **Tip:** The system automatically applies this selection - no manual configuration needed!"""

    def _explain_format_choice(self, query_lower: str, chosen_format: str) -> str:
        """Explain why a particular format was chosen."""
        if chosen_format == 'detailed':
            # Check which indicators triggered detailed format
            complex_indicators = [
                ('analytical questions', ['how does', 'how is', 'why does', 'explain how', 'analyze']),
                ('comparison queries', ['compare', 'difference', 'vs', 'versus']),
                ('architecture questions', ['architecture', 'design pattern', 'structure']),
                ('technical deep-dive', ['algorithm', 'performance', 'memory management', 'error handling']),
                ('process questions', ['workflow', 'process', 'sequence', 'interaction'])
            ]

            for category, indicators in complex_indicators:
                if any(indicator in query_lower for indicator in indicators):
                    return f"Detected {category} - requires comprehensive context"

            return "Contains question words suggesting analytical intent"

        else:  # clean format
            simple_indicators = [
                ('direct search', ['find', 'show me', 'get', 'locate']),
                ('listing queries', ['list', 'display', 'what functions', 'what classes']),
                ('existence checks', ['is there', 'does it have']),
                ('simple definitions', ['what is', 'what are'])
            ]

            for category, indicators in simple_indicators:
                if any(indicator in query_lower for indicator in indicators):
                    return f"Detected {category} - benefits from streamlined format"

            return "Appears to be a lookup/search query - clean format preferred"

    async def set_context_format(
        self,
        format_type: str,
        __event_emitter__=None
    ) -> str:
        """
        ⚙️ Set the context formatting style (overrides automatic selection).

        :param format_type: Either "clean" (optimized for OpenWebUI) or "detailed" (original verbose)
        """
        if format_type.lower() not in ["clean", "detailed"]:
            return "❌ Invalid format type. Use 'clean' or 'detailed'."

        self.valves.context_format = format_type.lower()

        return f"✅ Context format set to: {format_type.lower()}\n\n" + \
               f"{'Clean format optimizes context for OpenWebUI consumption.' if format_type.lower() == 'clean' else 'Detailed format provides verbose search results.'}\n\n" + \
               "⚠️ **Note:** This overrides automatic format selection. The system will still auto-select per query unless you manually set this."
    
    async def toggle_auto_context(
        self,
        __event_emitter__=None
    ) -> str:
        """
        🔄 Toggle automatic context injection for code-related queries.
        """
        self.valves.auto_context_injection = not self.valves.auto_context_injection

        status = "enabled" if self.valves.auto_context_injection else "disabled"
        return f"✅ Automatic context injection {status}.\n\n" + \
               f"{'Code-related queries will automatically include relevant context.' if self.valves.auto_context_injection else 'Manual context retrieval only.'}"

    async def debug_tool_status(
        self,
        test_query: str = "How does memory management work in this codebase?",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Debug the tool status and query detection.
        """
        # Ensure codebase selection is loaded (persistence fix)
        self._ensure_codebase_selection_loaded()

        debug_info = []

        # Check basic configuration
        debug_info.append("🔧 **TOOL CONFIGURATION:**")
        debug_info.append(f"   Server URL: {self.valves.code_analyzer_server_url}")
        debug_info.append(f"   Auto Context Injection: {self.valves.auto_context_injection}")
        debug_info.append(f"   Current Codebase: '{self.valves.current_codebase}'")
        debug_info.append(f"   Max Results: {self.valves.max_results}")

        # Test query detection
        debug_info.append("\n🔧 **QUERY DETECTION TEST:**")
        debug_info.append(f"   Test Query: '{test_query}'")

        intent = self._detect_query_intent(test_query)
        debug_info.append(f"   Detected Intent: {intent}")

        is_code_related = self._is_code_related_query(test_query)
        debug_info.append(f"   Is Code Related: {is_code_related}")

        is_code_related_detailed = self._detect_code_related_query(test_query)
        debug_info.append(f"   Detailed Code Detection: {is_code_related_detailed}")

        # Check server connectivity
        debug_info.append("\n🔧 **SERVER CONNECTIVITY:**")
        try:
            import requests
            response = requests.get(f"{self.valves.code_analyzer_server_url}/health", timeout=5)
            debug_info.append(f"   Server Health: {response.status_code} - {response.text[:100]}")
        except Exception as e:
            debug_info.append(f"   Server Health: ❌ Error - {e}")

        # Check codebase status (simplified)
        if self.valves.current_codebase:
            debug_info.append(f"   Current Codebase: {self.valves.current_codebase}")
        else:
            debug_info.append("   Current Codebase: None selected")

        # Show codebase change log
        if hasattr(self, '_codebase_change_log') and self._codebase_change_log:
            debug_info.append("\n🔧 **CODEBASE CHANGE LOG** (last 5):")
            for entry in self._codebase_change_log[-5:]:
                debug_info.append(f"   • {entry['timestamp'][:19]} | {entry['source']}: '{entry['old']}' → '{entry['new']}'")
        else:
            debug_info.append("\n🔧 **CODEBASE CHANGE LOG**: No changes recorded")

        # Simulate what would happen with the test query
        debug_info.append("\n🔧 **SIMULATION:**")
        if not self.valves.current_codebase:
            debug_info.append("   Result: Would suggest codebase selection")
        elif intent == "code_analysis" and self.valves.auto_context_injection:
            debug_info.append("   Result: Would auto-inject context")
        elif intent == "code_analysis":
            debug_info.append("   Result: Code query detected but auto-injection disabled")
        else:
            debug_info.append(f"   Result: Would handle as {intent} intent")

        return "\n".join(debug_info)

    async def test_intent_detection(self, test_queries: Optional[list] = None) -> str:
        """
        🧪 Test the intent detection logic with various queries.

        This helps verify that the intent detection fixes are working correctly.
        """
        if test_queries is None:
            test_queries = [
                "tell me about a modbus holding register and the functions used to manage it",
                "analyze complexity of utils codebase",
                "list codebases",
                "select codebase utils",
                "what is tmwmem_alloc function",
                "show me memory management functions",
                "get stats",
                "help with codebase analyzer",
                "how does memory allocation work",
                "complexity analysis for modbus"
            ]

        results = []
        results.append("🧪 **Intent Detection Test Results**")
        results.append("=" * 50)

        for query in test_queries:
            intent = self._detect_query_intent(query)
            is_code_related = self._detect_code_related_query(query)

            results.append(f"\n**Query:** `{query}`")
            results.append(f"**Intent:** {intent}")
            results.append(f"**Code Related:** {is_code_related}")

            # Show which keywords/patterns matched
            matched_keywords = self._get_matched_keywords(query)
            if matched_keywords:
                results.append(f"**Matched Keywords:** {', '.join(matched_keywords[:5])}{'...' if len(matched_keywords) > 5 else ''}")

            # Show what would happen
            if intent == "help":
                results.append("**Action:** Route to help documentation")
            elif intent == "codebase_management":
                results.append("**Action:** Route to management functions")
            elif intent == "code_analysis":
                if any(phrase in query.lower() for phrase in ['analyze complexity', 'code complexity analysis', 'complexity analysis']):
                    results.append("**Action:** Route to complexity analysis")
                else:
                    results.append("**Action:** Route to code context retrieval")
            else:
                results.append("**Action:** Return empty (let OpenWebUI handle)")

        results.append("\n" + "=" * 50)
        results.append("✅ **Intent detection test completed**")

        return "\n".join(results)

    async def configure_intent_keywords(self, action: str = "show", category: str = "", keywords: str = "") -> str:
        """
        🔧 Configure intent detection keywords and phrases.

        Actions:
        - "show" - Display current keyword configuration
        - "update" - Update a specific category (requires category and keywords parameters)
        - "reset" - Reset a category to defaults (requires category parameter)
        - "test" - Test current configuration with sample queries

        Categories:
        - programming, technical, analysis, protocol, languages
        - code_questions, management, help (for patterns/phrases)

        Example usage:
        - configure_intent_keywords("show")
        - configure_intent_keywords("update", "protocol", "modbus,bacnet,opcua,mqtt")
        - configure_intent_keywords("test")
        """
        if action == "show":
            result = []
            result.append("🔧 **Current Intent Detection Configuration**")
            result.append("=" * 50)

            # Show keyword categories
            result.append("\n📝 **Keyword Categories:**")
            categories = [
                ("Programming", self.valves.keywords_programming),
                ("Technical", self.valves.keywords_technical),
                ("Analysis", self.valves.keywords_analysis),
                ("Protocol", self.valves.keywords_protocol),
                ("Languages", self.valves.keywords_languages)
            ]

            for name, keywords in categories:
                result.append(f"\n**{name}:**")
                if keywords:
                    keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                    result.append(f"  Count: {len(keyword_list)}")
                    result.append(f"  Keywords: {', '.join(keyword_list[:10])}{'...' if len(keyword_list) > 10 else ''}")
                else:
                    result.append("  (empty)")

            # Show pattern categories
            result.append("\n🔍 **Pattern Categories:**")
            patterns = [
                ("Code Questions", self.valves.patterns_code_questions),
                ("Management", self.valves.phrases_management),
                ("Help", self.valves.phrases_help)
            ]

            for name, phrases in patterns:
                result.append(f"\n**{name}:**")
                if phrases:
                    phrase_list = [p.strip() for p in phrases.split(',') if p.strip()]
                    result.append(f"  Count: {len(phrase_list)}")
                    result.append(f"  Phrases: {', '.join(phrase_list[:5])}{'...' if len(phrase_list) > 5 else ''}")
                else:
                    result.append("  (empty)")

            result.append("\n" + "=" * 50)
            result.append("💡 Use configure_intent_keywords('test') to test current configuration")

            return "\n".join(result)

        elif action == "test":
            # Test current configuration with sample queries
            return await self.test_intent_detection()

        elif action == "update":
            if not category or not keywords:
                return "❌ Update action requires both 'category' and 'keywords' parameters"

            # Map category names to valve attributes
            category_map = {
                "programming": "keywords_programming",
                "technical": "keywords_technical",
                "analysis": "keywords_analysis",
                "protocol": "keywords_protocol",
                "languages": "keywords_languages",
                "code_questions": "patterns_code_questions",
                "management": "phrases_management",
                "help": "phrases_help"
            }

            if category not in category_map:
                return f"❌ Unknown category '{category}'. Valid categories: {', '.join(category_map.keys())}"

            # Update the valve attribute
            valve_attr = category_map[category]
            setattr(self.valves, valve_attr, keywords)

            return f"✅ Updated {category} keywords/phrases successfully"

        elif action == "reset":
            if not category:
                return "❌ Reset action requires 'category' parameter"

            # Reset to default values (you would need to define these defaults)
            return f"⚠️ Reset functionality not yet implemented for category '{category}'"

        else:
            return f"❌ Unknown action '{action}'. Valid actions: show, update, reset, test"

    async def unselect_codebase(
        self,
        __event_emitter__=None
    ) -> str:
        """
        🚫 Unselect the current codebase (clear selection).

        This clears the current codebase selection, requiring explicit selection
        for subsequent operations.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Clearing codebase selection...", "done": False}
            })

        try:
            # In stateless architecture, just clear local state
            old_codebase = self.valves.current_codebase
            self.valves.current_codebase = None

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Codebase selection cleared", "done": True}
                })

            result = f"""✅ **Codebase Selection Cleared**

📊 **Client-Side State:**
- Previous: {old_codebase or 'None'}
- Current: None
- Architecture: Stateless (client manages selection)

🎯 **Next Steps:**
Use select_codebase() to choose a codebase for analysis.
"""

            return result

        except Exception as e:
            return f"❌ Error clearing codebase selection: {str(e)}"

    async def force_set_codebase(
        self,
        codebase_name: str,
        __event_emitter__=None
    ) -> str:
        """
        🔧 Force set the current codebase (for debugging sync issues).

        :param codebase_name: Name of the codebase to force set (required)
        """
        if not codebase_name:
            return "❌ Codebase name is required for force_set_codebase"

        old_codebase = self.valves.current_codebase
        self.valves.current_codebase = codebase_name

        # Log the forced change
        self._log_codebase_change("FORCE_SET", old_codebase, codebase_name)

        return f"✅ Codebase forced from '{old_codebase}' to '{codebase_name}'\n\n" + \
               "Now try your query again: 'How does memory management work in this codebase?'"

    async def test_context_endpoint(
        self,
        test_query: str = "memory management",
        codebase_name: str = "utils",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Test the specific context retrieval endpoint with detailed debugging.
        """
        debug_info = []
        debug_info.append("🔧 **TESTING CONTEXT ENDPOINT:**")
        debug_info.append(f"   Server URL: {self.valves.code_analyzer_server_url}")
        debug_info.append(f"   Test Query: '{test_query}'")
        debug_info.append(f"   Codebase: '{codebase_name}'")

        try:
            import requests

            # Test basic server health first
            debug_info.append("\n🔧 **HEALTH CHECK:**")
            try:
                health_response = requests.get(f"{self.valves.code_analyzer_server_url}/health", timeout=5)
                debug_info.append(f"   Health Status: {health_response.status_code}")
                debug_info.append(f"   Health Response: {health_response.text[:200]}")
            except Exception as e:
                debug_info.append(f"   Health Check Failed: {e}")
                return "\n".join(debug_info)

            # Test the specific endpoint
            debug_info.append("\n🔧 **CONTEXT ENDPOINT TEST:**")
            payload = {
                "query": test_query,
                "codebase_name": codebase_name,
                "n_results": 5,
                "context_preferences": None
            }
            debug_info.append(f"   Payload: {payload}")

            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/get_optimized_context",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            debug_info.append(f"   Response Status: {response.status_code}")
            debug_info.append(f"   Response Headers: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    debug_info.append(f"   Response Type: {type(data)}")
                    if isinstance(data, dict):
                        debug_info.append(f"   Response Keys: {list(data.keys())}")
                        result = data.get("result", "")
                        debug_info.append(f"   Result Length: {len(str(result))}")
                        debug_info.append(f"   Result Preview: {str(result)[:200]}...")
                    else:
                        debug_info.append(f"   Response Content: {str(data)[:200]}...")
                except Exception as e:
                    debug_info.append(f"   JSON Parse Error: {e}")
                    debug_info.append(f"   Raw Response: {response.text[:200]}...")
            else:
                debug_info.append(f"   Error Response: {response.text[:200]}...")

        except Exception as e:
            debug_info.append(f"   Request Failed: {e}")
            debug_info.append(f"   Error Type: {type(e).__name__}")

        return "\n".join(debug_info)

    async def test_multiple_queries(
        self,
        codebase_name: str = "utils",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Test multiple specific queries to debug search functionality.
        """
        test_queries = [
            "tmwmem_alloc",
            "tmwmem_free",
            "TMWMEM_HEADER",
            "memory allocation",
            "malloc",
            "free",
            "buffer",
            "pool"
        ]

        results = []
        results.append("🔧 **TESTING MULTIPLE QUERIES:**")
        results.append(f"   Codebase: {codebase_name}")
        results.append("")

        for query in test_queries:
            try:
                context = await self.get_code_context(
                    query=query,
                    codebase_name=codebase_name,
                    n_results=3,
                    __event_emitter__=None  # Suppress status messages for cleaner output
                )

                if context and "No relevant code context found" not in context:
                    results.append(f"✅ '{query}' -> Found context ({len(context)} chars)")
                else:
                    results.append(f"❌ '{query}' -> No context found")

            except Exception as e:
                results.append(f"🚨 '{query}' -> Error: {e}")

        return "\n".join(results)

    async def test_server_search_direct(
        self,
        test_query: str = "tmwmem_alloc",
        codebase_name: str = "utils",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Test the server's search functionality directly with detailed debugging.
        """
        debug_info = []
        debug_info.append("🔧 **TESTING SERVER SEARCH DIRECTLY:**")
        debug_info.append(f"   Query: '{test_query}'")
        debug_info.append(f"   Codebase: '{codebase_name}'")

        try:
            import requests

            # Test the enhanced search endpoint directly
            payload = {
                "query": test_query,
                "codebase_name": codebase_name,
                "n_results": 5
            }

            debug_info.append("\n🔧 **TESTING ENHANCED SEARCH ENDPOINT:**")
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/enhanced_search",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            debug_info.append(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", "")
                debug_info.append(f"   Enhanced Search Result Length: {len(str(result))}")
                debug_info.append(f"   Enhanced Search Preview: {str(result)[:300]}...")
            else:
                debug_info.append(f"   Enhanced Search Error: {response.text[:200]}")

            # Test the legacy search endpoint
            debug_info.append("\n🔧 **TESTING LEGACY SEARCH ENDPOINT:**")
            legacy_payload = {
                "query": test_query,
                "codebase_name": codebase_name,
                "n_results": 5
            }

            legacy_response = requests.post(
                f"{self.valves.code_analyzer_server_url}/search",
                json=legacy_payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            debug_info.append(f"   Legacy Status: {legacy_response.status_code}")
            if legacy_response.status_code == 200:
                legacy_data = legacy_response.json()
                legacy_result = legacy_data.get("result", "")
                debug_info.append(f"   Legacy Search Result Length: {len(str(legacy_result))}")
                debug_info.append(f"   Legacy Search Preview: {str(legacy_result)[:300]}...")
            else:
                debug_info.append(f"   Legacy Search Error: {legacy_response.text[:200]}")

        except Exception as e:
            debug_info.append(f"   Request Failed: {e}")

        return "\n".join(debug_info)

    async def test_collection_status(
        self,
        codebase_name: str = "utils",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Test the collection status and basic stats.
        """
        debug_info = []
        debug_info.append("🔧 **TESTING COLLECTION STATUS:**")
        debug_info.append(f"   Codebase: '{codebase_name}'")

        try:
            import requests

            # Get enhanced stats
            response = requests.get(
                f"{self.valves.code_analyzer_server_url}/tools/get_enhanced_stats/{codebase_name}",
                timeout=10
            )

            debug_info.append(f"   Stats Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", "")
                debug_info.append(f"   Stats Result: {str(result)[:500]}...")
            else:
                debug_info.append(f"   Stats Error: {response.text[:200]}")

        except Exception as e:
            debug_info.append(f"   Stats Request Failed: {e}")

        return "\n".join(debug_info)

    async def test_optimized_context_debug(
        self,
        test_query: str = "tmwmem_alloc",
        codebase_name: str = "utils",
        __event_emitter__=None
    ) -> str:
        """
        🔧 Test the optimized context endpoint with debugging to see what's failing.
        """
        debug_info = []
        debug_info.append("🔧 **TESTING OPTIMIZED CONTEXT WITH DEBUG:**")
        debug_info.append(f"   Query: '{test_query}'")
        debug_info.append(f"   Codebase: '{codebase_name}'")

        try:
            import requests

            # Test with minimal parameters first
            payload = {
                "query": test_query,
                "codebase_name": codebase_name,
                "n_results": 3,
                "context_preferences": None
            }

            debug_info.append("\n🔧 **TESTING WITH MINIMAL PARAMETERS:**")
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/get_optimized_context",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            debug_info.append(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", "")
                debug_info.append(f"   Result Type: {type(result)}")
                debug_info.append(f"   Result Length: {len(str(result))}")
                if "No relevant code context found" in str(result):
                    debug_info.append("   ❌ No context found - filtering issue likely")
                else:
                    debug_info.append("   ✅ Context found!")
                debug_info.append(f"   Result Preview: {str(result)[:400]}...")
            else:
                debug_info.append(f"   Error: {response.text[:300]}")

            # Test with different context preferences
            debug_info.append("\n🔧 **TESTING WITH RELAXED PREFERENCES:**")
            relaxed_payload = {
                "query": test_query,
                "codebase_name": codebase_name,
                "n_results": 5,
                "context_preferences": {
                    "prefer_documented": False,
                    "prefer_public_api": False,
                    "filter_language": None,
                    "filter_type": None
                }
            }

            relaxed_response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/get_optimized_context",
                json=relaxed_payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            debug_info.append(f"   Relaxed Status: {relaxed_response.status_code}")
            if relaxed_response.status_code == 200:
                relaxed_data = relaxed_response.json()
                relaxed_result = relaxed_data.get("result", "")
                if "No relevant code context found" in str(relaxed_result):
                    debug_info.append("   ❌ Still no context - deeper issue")
                else:
                    debug_info.append("   ✅ Context found with relaxed preferences!")
                debug_info.append(f"   Relaxed Preview: {str(relaxed_result)[:400]}...")
            else:
                debug_info.append(f"   Relaxed Error: {relaxed_response.text[:300]}")

        except Exception as e:
            debug_info.append(f"   Request Failed: {e}")

        return "\n".join(debug_info)

    def _load_persistent_codebase_selection(self):
        """Load persistent codebase selection from server (fallback for valve persistence issues)"""
        try:
            # Try to get the current selection from the server's session storage
            response = requests.get(
                f"{self.valves.code_analyzer_server_url}/tools/get_session_codebase",
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                server_codebase = data.get('current_codebase')
                print(f"🔧 [PERSISTENCE] Server session: '{server_codebase}'", flush=True)

                # Always update from server (don't check if valve is empty)
                if server_codebase:
                    old_valve = self.valves.current_codebase
                    self.valves.current_codebase = server_codebase
                    self._log_codebase_change("PERSISTENCE", old_valve, server_codebase)
                else:
                    print(f"🔧 [PERSISTENCE] No server codebase, keeping valve: '{self.valves.current_codebase}'", flush=True)
            else:
                print(f"🔧 [PERSISTENCE] Server session error: {response.status_code}", flush=True)

        except Exception as e:
            print(f"🔧 [PERSISTENCE] Exception loading session: {e}", flush=True)
            # Silent fallback - persistence is a best-effort feature
            pass

    def _ensure_codebase_selection_loaded(self):
        """Ensure codebase selection is loaded before processing queries"""
        # Check if we recently made a selection (within last 30 seconds)
        time_since_selection = time.time() - self._last_selection_time
        if time_since_selection < 30:
            print(f"🔧 [PERSISTENCE] Skipping load - recent selection ({time_since_selection:.1f}s ago): '{self.valves.current_codebase}'", flush=True)
            return

        # Always check server for latest selection (since OpenWebUI creates new instances)
        old_codebase = self.valves.current_codebase
        print(f"🔧 [PERSISTENCE] Before load: valve='{old_codebase}'", flush=True)

        self._load_persistent_codebase_selection()

        if old_codebase != self.valves.current_codebase:
            print(f"🔧 [PERSISTENCE] Updated: '{old_codebase}' → '{self.valves.current_codebase}'", flush=True)
        else:
            print(f"🔧 [PERSISTENCE] No change: '{self.valves.current_codebase}'", flush=True)

    def _log_codebase_change(self, source: str, old_codebase: str, new_codebase: str):
        """Log codebase changes for debugging"""
        import datetime
        change_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "source": source,
            "old": old_codebase,
            "new": new_codebase
        }
        self._codebase_change_log.append(change_entry)

        # Keep only last 10 entries
        if len(self._codebase_change_log) > 10:
            self._codebase_change_log = self._codebase_change_log[-10:]

        print(f"🔧 [CODEBASE LOG] {source}: '{old_codebase}' → '{new_codebase}'", flush=True)

    def _save_persistent_codebase_selection(self, codebase_name):
        """Save persistent codebase selection to server (fallback for valve persistence issues)"""
        try:
            # Save the selection to the server's session storage
            response = requests.post(
                f"{self.valves.code_analyzer_server_url}/tools/set_session_codebase",
                json={"codebase_name": codebase_name},
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code == 200:
                print(f"🔧 [PERSISTENCE] Saved: '{codebase_name}'", flush=True)

        except Exception:
            # Silent fallback - persistence is a best-effort feature
            pass

    async def create_vector_database(
        self,
        codebase_name: str,
        exclude_dirs: Optional[List[str]] = None
    ) -> str:
        """
        Create a new vector database for a codebase.

        Args:
            codebase_name: Name of the codebase to create vector database for
            exclude_dirs: List of directories to exclude from processing (optional)

        Returns:
            Formatted result with creation statistics
        """
        if exclude_dirs is None:
            exclude_dirs = ["build", "test", "bin", "obj", "__pycache__", ".git"]

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
                async with session.post(
                    f"{self.valves.code_analyzer_server_url}/tools/create_codebase",
                    json={
                        "codebase_name": codebase_name,
                        "exclude_dirs": exclude_dirs
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            result_text = data.get('result', 'Vector database created successfully')
                            processing_stats = data.get('processing_stats', {})

                            formatted_result = f"""✅ **Vector Database Created: {codebase_name}**

{result_text}

📊 **Creation Statistics:**
• Files processed: {processing_stats.get('files_processed', 0)}
• Chunks created: {processing_stats.get('chunks_created', 0)}
• Functions discovered: {processing_stats.get('functions_discovered', 0)}
• Processing time: {processing_stats.get('processing_time_seconds', 0):.2f}s

🎉 Vector database is ready for code analysis!"""
                            return formatted_result
                        else:
                            return f"❌ Failed to create vector database: {data.get('error', 'Unknown error')}"
                    else:
                        return f"❌ Server error: HTTP {response.status}"
        except Exception as e:
            return f"❌ Error creating vector database: {str(e)}"

    async def rebuild_vector_database(
        self,
        codebase_name: str,
        exclude_dirs: Optional[List[str]] = None
    ) -> str:
        """
        Rebuild (delete and recreate) vector database for a codebase.

        Args:
            codebase_name: Name of the codebase to rebuild vector database for
            exclude_dirs: List of directories to exclude from processing (optional)

        Returns:
            Formatted result with rebuild statistics
        """
        if exclude_dirs is None:
            exclude_dirs = ["build", "test", "bin", "obj", "__pycache__", ".git"]

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
                async with session.post(
                    f"{self.valves.code_analyzer_server_url}/tools/rebuild_codebase",
                    json={
                        "codebase_name": codebase_name,
                        "exclude_dirs": exclude_dirs
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            result_text = data.get('result', 'Vector database rebuilt successfully')
                            deletion_stats = data.get('deletion_stats', {})
                            processing_stats = data.get('processing_stats', {})

                            formatted_result = f"""🔄 **Vector Database Rebuilt: {codebase_name}**

{result_text}

📊 **Rebuild Statistics:**
**Deletion:**
• Chunks deleted: {deletion_stats.get('chunks_deleted', 0)}
• Collections removed: {deletion_stats.get('collections_deleted', 0)}

**Recreation:**
• Files processed: {processing_stats.get('files_processed', 0)}
• Chunks created: {processing_stats.get('chunks_created', 0)}
• Functions discovered: {processing_stats.get('functions_discovered', 0)}
• Processing time: {processing_stats.get('processing_time_seconds', 0):.2f}s

🎉 Vector database has been completely rebuilt!"""
                            return formatted_result
                        else:
                            return f"❌ Failed to rebuild vector database: {data.get('error', 'Unknown error')}"
                    else:
                        return f"❌ Server error: HTTP {response.status}"
        except Exception as e:
            return f"❌ Error rebuilding vector database: {str(e)}"

    async def reprocess_vector_database(
        self,
        codebase_name: str,
        exclude_dirs: Optional[List[str]] = None
    ) -> str:
        """
        Reprocess/update existing vector database for a codebase.

        Args:
            codebase_name: Name of the codebase to reprocess vector database for
            exclude_dirs: List of directories to exclude from processing (optional)

        Returns:
            Formatted result with reprocessing statistics
        """
        if exclude_dirs is None:
            exclude_dirs = ["build", "test", "bin", "obj", "__pycache__", ".git"]

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
                async with session.post(
                    f"{self.valves.code_analyzer_server_url}/tools/process_codebase",
                    json={
                        "codebase_name": codebase_name,
                        "exclude_dirs": exclude_dirs
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            result_text = data.get('result', 'Vector database reprocessed successfully')
                            processing_stats = data.get('processing_stats', {})

                            formatted_result = f"""⚙️ **Vector Database Reprocessed: {codebase_name}**

{result_text}

📊 **Reprocessing Statistics:**
• Files processed: {processing_stats.get('files_processed', 0)}
• Chunks updated: {processing_stats.get('chunks_created', 0)}
• Functions discovered: {processing_stats.get('functions_discovered', 0)}
• Processing time: {processing_stats.get('processing_time_seconds', 0):.2f}s

🎉 Vector database has been updated with latest changes!"""
                            return formatted_result
                        else:
                            return f"❌ Failed to reprocess vector database: {data.get('error', 'Unknown error')}"
                    else:
                        return f"❌ Server error: HTTP {response.status}"
        except Exception as e:
            return f"❌ Error reprocessing vector database: {str(e)}"

    async def delete_vector_database(
        self,
        codebase_name: str
    ) -> str:
        """
        Delete vector database for a codebase (permanent operation).

        Args:
            codebase_name: Name of the codebase to delete vector database for

        Returns:
            Formatted result with deletion statistics
        """
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                async with session.post(
                    f"{self.valves.code_analyzer_server_url}/tools/delete_codebase",
                    json={
                        "codebase_name": codebase_name
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            result_text = data.get('result', 'Vector database deleted successfully')

                            formatted_result = f"""🗑️ **Vector Database Deleted: {codebase_name}**

{result_text}

⚠️ **This operation is permanent and cannot be undone.**

To recreate the vector database, use the create_vector_database function."""
                            return formatted_result
                        else:
                            return f"❌ Failed to delete vector database: {data.get('error', 'Unknown error')}"
                    else:
                        return f"❌ Server error: HTTP {response.status}"
        except Exception as e:
            return f"❌ Error deleting vector database: {str(e)}"