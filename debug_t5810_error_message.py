#!/usr/bin/env python3
"""
Debug the error message that T58<PERSON> is seeing
"""

import requests
import json

def debug_t5810_error():
    """Debug what error T5810 is seeing"""
    print("🔍 Debugging T5810 Error Message")
    print("=" * 60)
    
    t5810_ollama = "http://************:11434"
    
    # Create the exact tool with the fixed definition
    tools = [{
        'type': 'function',
        'function': {
            'name': 'query_codebase',
            'description': 'Search for specific code patterns or functions in a codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {'type': 'string', 'description': 'Name of the codebase to search in'},
                    'query': {'type': 'string', 'description': 'What to search for'},
                    'max_results': {'type': 'integer', 'description': 'Maximum number of results (default: 10)'}
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'show timer management in utils codebase'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print("📤 Sending request to T5810...")
    
    try:
        # Step 1: Get T5810's tool call
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            
            if tool_calls:
                messages.append(message)
                
                for tool_call in tool_calls:
                    function = tool_call.get('function', {})
                    arguments_raw = function.get('arguments', '{}')
                    
                    # Parse arguments
                    if isinstance(arguments_raw, dict):
                        arguments = arguments_raw
                    else:
                        arguments = json.loads(arguments_raw)
                    
                    print(f"🔧 T5810 called tool with: {arguments}")
                    
                    # Execute tool manually to see what happens
                    try:
                        tool_response = requests.post(
                            "http://192.168.0.77:5002/search",
                            json={
                                "query": arguments.get('query', ''),
                                "codebase_name": arguments.get('codebase_name', ''),
                                "n_results": max(1, arguments.get('max_results', 10))
                            },
                            timeout=60
                        )
                        
                        print(f"🔧 Tool response status: {tool_response.status_code}")
                        
                        if tool_response.status_code == 200:
                            tool_result = tool_response.json()
                            
                            # Check if there are any error messages in the result
                            if 'error' in tool_result:
                                print(f"❌ Tool result contains error: {tool_result['error']}")
                            elif 'results' in tool_result:
                                results = tool_result['results']
                                print(f"✅ Tool returned {len(results)} results")
                                
                                if results:
                                    first_result = results[0]
                                    content = first_result.get('content', '')
                                    
                                    # Check if the content itself contains error messages
                                    if 'error' in content.lower() or 'problem' in content.lower():
                                        print(f"⚠️ First result content contains error-like text:")
                                        print(f"   {content[:200]}...")
                                    else:
                                        print(f"✅ First result looks clean:")
                                        print(f"   {content[:200]}...")
                            else:
                                print(f"⚠️ Unexpected tool result format: {list(tool_result.keys())}")
                                
                        else:
                            print(f"❌ Tool HTTP error: {tool_response.status_code}")
                            print(f"   Response: {tool_response.text}")
                            tool_result = {"error": f"HTTP {tool_response.status_code}: {tool_response.text}"}
                            
                    except Exception as e:
                        print(f"❌ Tool execution exception: {e}")
                        tool_result = {"error": str(e)}
                    
                    # Add tool result to messages
                    tool_result_json = json.dumps(tool_result)
                    messages.append({
                        'role': 'tool',
                        'content': tool_result_json
                    })
                
                # Get final response
                print(f"\n📤 Getting final response from T5810...")
                
                final_payload = {
                    'model': 'llama3.1:latest',
                    'messages': messages,
                    'stream': False
                }
                
                final_response = requests.post(f"{t5810_ollama}/api/chat", json=final_payload, timeout=120)
                
                if final_response.status_code == 200:
                    final_data = final_response.json()
                    final_content = final_data.get('message', {}).get('content', '')
                    
                    print(f"📝 Final response:")
                    print(final_content[:500] + "..." if len(final_content) > 500 else final_content)
                    
                    # Look for the specific error message
                    if "problem with comparing strings and integers" in final_content:
                        print(f"\n🔍 FOUND THE ERROR MESSAGE!")
                        print(f"T5810 is seeing some comparison error that's making it confused")
                    
                    if "error message indicates" in final_content:
                        print(f"\n🔍 T5810 is responding to an error message it received")
                        print(f"This suggests the tool result contains error information")
                else:
                    print(f"❌ Final response error: {final_response.status_code}")
            else:
                print(f"❌ T5810 didn't call any tools")
        else:
            print(f"❌ Initial response error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_search_endpoint_directly():
    """Test the search endpoint directly to see if it returns errors"""
    print(f"\n🔧 Testing Search Endpoint Directly")
    print("=" * 60)
    
    try:
        response = requests.post(
            "http://192.168.0.77:5002/search",
            json={
                "query": "timer management",
                "codebase_name": "utils",
                "n_results": 5
            },
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"Response keys: {list(data.keys())}")
            
            if 'results' in data:
                results = data['results']
                print(f"Found {len(results)} results")
                
                if results:
                    first_result = results[0]
                    content = first_result.get('content', '')
                    
                    print(f"First result content preview:")
                    print(content[:300] + "..." if len(content) > 300 else content)
                    
                    # Check for error-like content
                    error_indicators = ['error', 'problem', 'failed', 'exception']
                    found_errors = [ind for ind in error_indicators if ind.lower() in content.lower()]
                    
                    if found_errors:
                        print(f"⚠️ Content contains error-like terms: {found_errors}")
                    else:
                        print(f"✅ Content looks clean")
            else:
                print(f"⚠️ No results key in response")
        else:
            print(f"❌ HTTP error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    print("🔍 Debugging T5810 Error Message Issue")
    print("=" * 70)
    
    # Test search endpoint directly
    test_search_endpoint_directly()
    
    # Debug T5810's full interaction
    debug_t5810_error()

if __name__ == "__main__":
    main()
