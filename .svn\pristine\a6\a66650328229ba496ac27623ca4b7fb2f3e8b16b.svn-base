#!/usr/bin/env python3
"""
Hybrid Code Analyzer - Integrates direct Ollama with existing system
Routes queries to appropriate backend based on model location
"""

import requests
import json
from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

class HybridCodeAnalyzer:
    def __init__(self):
        self.openwebui_url = "http://home-ai-server.local:8080"
        self.openwebui_api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        
        # Remote Ollama instances with direct tool calling
        self.remote_analyzers = {
            "lynn-pc": DirectOllamaCodeAnalyzer("http://192.168.0.32:11434"),
            "t5810": DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
        }
        
        # Model routing configuration
        self.model_routing = {
            # Local models (use OpenWebUI)
            "llama3:latest": "openwebui",
            "deepseek-coder:6.7b": "openwebui",
            "qwen2.5-coder:7b": "openwebui",
            
            # Remote models (use direct Ollama)
            "LYNN-PC.llama3.1:latest": "lynn-pc",
            "T5810.llama3.1:latest": "t5810",
        }
    
    def analyze_code(self, query, model="auto", prefer_fast=False):
        """
        Analyze code using the best available model/method
        
        Args:
            query: The code analysis query
            model: Specific model to use, or "auto" for automatic selection
            prefer_fast: If True, prefer faster models (T5810 > lynn-pc > local)
        """
        
        # Auto-select model if not specified
        if model == "auto":
            if prefer_fast:
                model = "T5810.llama3.1:latest"  # Fastest option
            else:
                model = "llama3:latest"  # Default local model
        
        # Route to appropriate backend
        backend = self.model_routing.get(model, "openwebui")
        
        if backend == "openwebui":
            return self._query_openwebui(query, model)
        elif backend in self.remote_analyzers:
            return self._query_remote_ollama(query, backend)
        else:
            raise ValueError(f"Unknown model or backend: {model}")
    
    def _query_openwebui(self, query, model):
        """Query using OpenWebUI (for local models)"""
        headers = {
            "Authorization": f"Bearer {self.openwebui_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": query}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{self.openwebui_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {
                    "result": content,
                    "backend": "openwebui",
                    "model": model,
                    "success": True
                }
            else:
                return {
                    "error": f"OpenWebUI error: {response.status_code}",
                    "backend": "openwebui",
                    "model": model,
                    "success": False
                }
                
        except Exception as e:
            return {
                "error": f"OpenWebUI exception: {str(e)}",
                "backend": "openwebui", 
                "model": model,
                "success": False
            }
    
    def _query_remote_ollama(self, query, backend):
        """Query using direct Ollama (for remote models)"""
        analyzer = self.remote_analyzers[backend]
        
        try:
            result = analyzer.chat_with_tools(query)
            return {
                "result": result,
                "backend": f"direct_ollama_{backend}",
                "model": "llama3.1:latest",
                "success": True
            }
        except Exception as e:
            return {
                "error": f"Direct Ollama exception: {str(e)}",
                "backend": f"direct_ollama_{backend}",
                "model": "llama3.1:latest", 
                "success": False
            }
    
    def get_available_models(self):
        """Get list of available models and their backends"""
        return {
            "local_models": [
                {"name": "llama3:latest", "backend": "openwebui", "location": "home-ai-server"},
                {"name": "deepseek-coder:6.7b", "backend": "openwebui", "location": "home-ai-server"},
                {"name": "qwen2.5-coder:7b", "backend": "openwebui", "location": "home-ai-server"}
            ],
            "remote_models": [
                {"name": "LYNN-PC.llama3.1:latest", "backend": "direct_ollama", "location": "lynn-pc", "gpu": "RTX 3050"},
                {"name": "T5810.llama3.1:latest", "backend": "direct_ollama", "location": "t5810", "gpu": "RTX 3090"}
            ]
        }
    
    def benchmark_models(self, query="List all available codebases"):
        """Benchmark all available models"""
        results = {}
        
        for model in self.model_routing.keys():
            print(f"🧪 Testing {model}...")
            
            import time
            start_time = time.time()
            
            result = self.analyze_code(query, model=model)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            results[model] = {
                "response_time": response_time,
                "success": result["success"],
                "backend": result.get("backend", "unknown")
            }
            
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {response_time:.2f}s")
        
        return results

def demo_hybrid_analyzer():
    """Demonstrate the hybrid analyzer"""
    print("🚀 Hybrid Code Analyzer Demo")
    print("=" * 50)
    
    analyzer = HybridCodeAnalyzer()
    
    # Show available models
    models = analyzer.get_available_models()
    print("\n📋 Available Models:")
    
    print("\n🏠 Local Models (OpenWebUI):")
    for model in models["local_models"]:
        print(f"   • {model['name']} ({model['location']})")
    
    print("\n🌐 Remote Models (Direct Ollama):")
    for model in models["remote_models"]:
        print(f"   • {model['name']} ({model['location']}, {model['gpu']})")
    
    # Test queries
    test_queries = [
        ("List codebases", "auto", False),
        ("Find memory functions in utils", "T5810.llama3.1:latest", True),
        ("What languages are in z80emu?", "LYNN-PC.llama3.1:latest", True)
    ]
    
    print(f"\n🧪 Testing Queries:")
    
    for query, model, prefer_fast in test_queries:
        print(f"\n📤 Query: {query}")
        print(f"🎯 Model: {model} (prefer_fast={prefer_fast})")
        
        result = analyzer.analyze_code(query, model=model, prefer_fast=prefer_fast)
        
        if result["success"]:
            print(f"✅ Success via {result['backend']}")
            print(f"📝 Response: {result['result'][:100]}...")
        else:
            print(f"❌ Failed: {result['error']}")

if __name__ == "__main__":
    demo_hybrid_analyzer()
