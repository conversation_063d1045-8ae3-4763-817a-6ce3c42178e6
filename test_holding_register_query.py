#!/usr/bin/env python3
"""
Test the holding register query to see if it works with the fixed configuration
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def test_holding_register_query():
    """Test the exact holding register query that's failing"""
    print("🔍 Testing Holding Register Query")
    print("=" * 60)
    
    # Create the tool instance
    tool = Tools()
    
    # Set the modbus codebase
    tool.valves.current_codebase = "modbus"
    
    # Check the configured URL
    print(f"📊 Configuration:")
    print(f"   Code Analyzer URL: {tool.valves.code_analyzer_server_url}")
    print(f"   Current Codebase: {tool.valves.current_codebase}")
    print(f"   Request Timeout: {tool.valves.request_timeout}")
    
    # Test the exact query that's failing
    query = "holding register"
    
    print(f"\n📤 Testing query: '{query}'")
    print("-" * 50)
    
    try:
        # Test the get_code_context method directly
        context = await tool.get_code_context(
            query=query,
            codebase_name="modbus",
            n_results=5
        )
        
        print(f"📝 Context length: {len(context) if context else 0}")
        
        if context and "No relevant code context found" not in context:
            print(f"✅ SUCCESS: Context retrieved successfully!")
            print(f"📊 Context preview:")
            print(context[:600] + "..." if len(context) > 600 else context)
            
            # Check if it contains real holding register content
            holding_terms = ['holding', 'register', 'modbus', 'read', 'write', 'triangle microworks']
            found_terms = [term for term in holding_terms if term.lower() in context.lower()]
            print(f"🎯 Found holding register terms: {found_terms}")
            
            # Check for specific function names that should be in the real code
            real_functions = ['_processReadHoldingRegisters', '_processWriteHoldingRegisters', 'holdingRegister']
            found_functions = [func for func in real_functions if func in context]
            print(f"🔧 Found real functions: {found_functions}")
            
            return True
        else:
            print(f"❌ FAILURE: Still getting 'No relevant code context found'")
            print(f"📝 Context: {context}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        
        # Check if it's a 400 error
        if "400" in str(e) and "192.168.0.32" in str(e):
            print(f"🔍 STILL HITTING LYNN-PC: The configuration hasn't been updated yet")
        elif "400" in str(e):
            print(f"🔍 400 ERROR: Check the request payload format")
        
        return False

async def test_direct_search_request():
    """Test making a direct search request to verify the endpoint works"""
    print(f"\n🔧 Testing Direct Search Request")
    print("=" * 60)
    
    import requests
    
    # Use the updated URL
    url = "http://192.168.0.77:5002/search"
    payload = {
        "query": "holding register",
        "codebase_name": "modbus",
        "n_results": 5
    }
    
    print(f"📤 Direct request to: {url}")
    print(f"📦 Payload: {payload}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if isinstance(data, dict) and 'results' in data:
                results = data['results']
                print(f"✅ SUCCESS: Found {len(results)} results")
                
                if results:
                    first_result = results[0]
                    content = first_result.get('content', '')
                    metadata = first_result.get('metadata', {})
                    file_path = metadata.get('file_path', 'Unknown')
                    
                    print(f"📄 First result: {file_path}")
                    print(f"📝 Content preview: {content[:300]}...")
                    
                    # Check for real holding register functions
                    if any(term in content.lower() for term in ['holding', 'register', 'modbus']):
                        print(f"🎯 Contains real holding register content")
                        return True
                    else:
                        print(f"⚠️ May not contain holding register content")
                        return False
                else:
                    print(f"❌ Empty results list")
                    return False
            else:
                print(f"⚠️ Unexpected response format: {type(data)}")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def test_full_query_processing():
    """Test the full query processing that OpenWebUI would do"""
    print(f"\n🚀 Testing Full Query Processing")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    # Test the full query that the user asked
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"📤 Full query: {query}")
    
    try:
        # This should trigger the auto-context injection
        result = await tool.__call__(query)
        
        print(f"📝 Result length: {len(result) if result else 0}")
        
        if result and len(result) > 100:
            print(f"✅ SUCCESS: Got substantial result")
            print(f"📊 Result preview:")
            print(result[:500] + "..." if len(result) > 500 else result)
            
            # Check if it contains real code context vs hallucination
            hallucination_indicators = [
                "Read Holding Registers: This function allows",
                "Write Holding Registers: This function allows",
                "Get Holding Register Count:",
                "I didn't see any direct references"
            ]
            
            real_code_indicators = [
                "Triangle MicroWorks",
                "/app/source_code/modbus",
                "_processReadHoldingRegisters",
                "_processWriteHoldingRegisters"
            ]
            
            found_hallucination = [ind for ind in hallucination_indicators if ind in result]
            found_real_code = [ind for ind in real_code_indicators if ind in result]
            
            print(f"\n🔍 Analysis:")
            print(f"Hallucination indicators: {found_hallucination}")
            print(f"Real code indicators: {found_real_code}")
            
            if found_real_code and not found_hallucination:
                print(f"✅ SUCCESS: Shows real modbus code!")
                return True
            elif found_hallucination:
                print(f"❌ FAILURE: Still hallucinating")
                return False
            else:
                print(f"⚠️ UNCLEAR: Neither clear hallucination nor real code")
                return True
        else:
            print(f"❌ FAILURE: Empty or very short result")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def main():
    print("🔍 Testing Holding Register Query After Configuration Fix")
    print("=" * 80)
    
    # Test context retrieval
    context_works = await test_holding_register_query()
    
    # Test direct search
    direct_works = await test_direct_search_request()
    
    # Test full processing
    full_works = await test_full_query_processing()
    
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 80)
    print(f"Context retrieval:  {'✅ WORKING' if context_works else '❌ BROKEN'}")
    print(f"Direct search:      {'✅ WORKING' if direct_works else '❌ BROKEN'}")
    print(f"Full processing:    {'✅ WORKING' if full_works else '❌ BROKEN'}")
    
    if context_works and direct_works and full_works:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"✅ The configuration fix worked")
        print(f"✅ Holding register queries now return real modbus code")
        print(f"✅ No more hallucination or 400 errors")
    elif direct_works:
        print(f"\n⚠️ PARTIAL SUCCESS")
        print(f"✅ Direct search works (server is fine)")
        print(f"❌ But OpenWebUI tool integration still has issues")
        print(f"💡 May need to restart OpenWebUI or update tool configuration")
    else:
        print(f"\n❌ STILL ISSUES")
        print(f"The configuration fix didn't resolve the problem")
        print(f"💡 Check if the updated tool file has been deployed to the server")

if __name__ == "__main__":
    asyncio.run(main())
