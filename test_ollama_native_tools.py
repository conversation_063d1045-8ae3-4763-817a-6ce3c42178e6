#!/usr/bin/env python3
"""
Test native Ollama tool calling on remote instances
This bypasses OpenWebUI and calls Ollama directly
"""

import requests
import json

def test_ollama_native_tool_calling():
    """Test Ollama's native tool calling on remote instance"""
    print("🧪 Testing Ollama Native Tool Calling")
    print("=" * 50)
    
    # Test on LYNN-PC Ollama instance
    lynn_pc_ollama = "http://************:11434"
    
    # Define a simple tool (similar to our code analyzer)
    tools = [{
        'type': 'function',
        'function': {
            'name': 'list_codebases',
            'description': 'List available codebases for analysis',
            'parameters': {
                'type': 'object',
                'properties': {},
                'required': [],
            },
        },
    }]
    
    # Test message
    messages = [
        {'role': 'user', 'content': 'List the available codebases for analysis'}
    ]
    
    # Try tool-compatible models
    models_to_test = ['llama3.1:latest', 'mistral-nemo:12b', 'llama3:latest']

    for model in models_to_test:
        print(f"\n🔍 Testing model: {model}")

        payload = {
            'model': model,
            'messages': messages,
            'tools': tools,
            'stream': False
        }
    
        print(f"📤 Testing native Ollama tool calling on LYNN-PC")
        print(f"🔗 URL: {lynn_pc_ollama}/api/chat")

        try:
            response = requests.post(
                f"{lynn_pc_ollama}/api/chat",
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                data = response.json()
                message = data.get('message', {})
                tool_calls = message.get('tool_calls', [])
                content = message.get('content', '')

                print(f"✅ {model}: Response received!")
                print(f"🛠️  Tool calls: {len(tool_calls)}")
                print(f"📝 Content: {content[:100]}...")

                if tool_calls:
                    print(f"🎉 SUCCESS: {model} supports tool calling!")
                    for i, tc in enumerate(tool_calls):
                        func_name = tc.get('function', {}).get('name', 'unknown')
                        print(f"   Tool {i+1}: {func_name}")
                    return True
                else:
                    print(f"⚠️ {model}: No tool calls, but response received")

            else:
                print(f"❌ {model}: HTTP Error {response.status_code}")
                if "does not support tools" in response.text:
                    print(f"   {model} does not support tools")
                else:
                    print(f"   Response: {response.text[:100]}")

        except Exception as e:
            print(f"❌ {model}: Exception: {e}")

    return False

def test_openai_compatible_endpoint():
    """Test Ollama's OpenAI-compatible endpoint with tools"""
    print(f"\n🔄 Testing OpenAI-Compatible Endpoint")
    print("-" * 30)
    
    lynn_pc_openai = "http://************:11434/v1"
    
    headers = {
        "Authorization": "Bearer ollama",
        "Content-Type": "application/json"
    }
    
    tools = [{
        "type": "function",
        "function": {
            "name": "list_codebases",
            "description": "List available codebases for analysis",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }]
    
    payload = {
        "model": "llama3:latest",
        "messages": [
            {"role": "user", "content": "List the available codebases"}
        ],
        "tools": tools
    }
    
    try:
        response = requests.post(
            f"{lynn_pc_openai}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('choices', [{}])[0].get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ OpenAI endpoint response received!")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content: {content[:200]}...")
            
            if tool_calls:
                print(f"🎉 SUCCESS: OpenAI-compatible tool calling works!")
                return True
            else:
                print(f"⚠️ No tool calls via OpenAI endpoint")
                return False
                
        else:
            print(f"❌ OpenAI endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI endpoint exception: {e}")
        return False

def create_code_analyzer_tool():
    """Create a tool definition that mimics our code analyzer"""
    return {
        'type': 'function',
        'function': {
            'name': 'analyze_code',
            'description': 'Analyze code in a specific codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {
                        'type': 'string',
                        'description': 'Name of the codebase to analyze',
                    },
                    'query': {
                        'type': 'string', 
                        'description': 'What to search for in the code',
                    }
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }

def test_realistic_code_analysis():
    """Test with a realistic code analysis scenario"""
    print(f"\n🔍 Testing Realistic Code Analysis Scenario")
    print("-" * 30)
    
    lynn_pc_ollama = "http://************:11434"
    
    tools = [create_code_analyzer_tool()]
    
    messages = [
        {'role': 'user', 'content': 'Find memory management functions in the utils codebase'}
    ]
    
    payload = {
        'model': 'llama3:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    try:
        response = requests.post(
            f"{lynn_pc_ollama}/api/chat",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            
            print(f"✅ Realistic scenario response received!")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            
            if tool_calls:
                print(f"🎉 SUCCESS: Model wants to call analyze_code!")
                for tc in tool_calls:
                    func = tc.get('function', {})
                    name = func.get('name', 'unknown')
                    args = func.get('arguments', '{}')
                    print(f"   Function: {name}")
                    print(f"   Arguments: {args}")
                return True
            else:
                print(f"⚠️ Model didn't call the tool")
                return False
                
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Testing Ollama Native Tool Calling Solution")
    print("=" * 60)
    print("This tests if remote Ollama instances can use tools natively,")
    print("bypassing OpenWebUI entirely.")
    
    # Test basic tool calling
    basic_works = test_ollama_native_tool_calling()
    
    # Test OpenAI-compatible endpoint
    openai_works = test_openai_compatible_endpoint()
    
    # Test realistic scenario
    realistic_works = test_realistic_code_analysis()
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Native Ollama API:     {'✅ Working' if basic_works else '❌ Not working'}")
    print(f"OpenAI-compatible:     {'✅ Working' if openai_works else '❌ Not working'}")
    print(f"Realistic scenario:    {'✅ Working' if realistic_works else '❌ Not working'}")
    
    if any([basic_works, openai_works, realistic_works]):
        print(f"\n🎉 BREAKTHROUGH!")
        print("✅ Remote Ollama instances CAN use tools natively!")
        print("💡 This means we can bypass OpenWebUI for tool calling")
        print("\n📋 Next steps:")
        print("1. Create a direct Ollama tool interface")
        print("2. Bypass OpenWebUI for remote model tool calls")
        print("3. Use native Ollama tool calling instead of OpenWebUI tools")
    else:
        print(f"\n❌ Native tool calling not working")
        print("💡 The proxy solution remains the best approach")

if __name__ == "__main__":
    main()
