#!/usr/bin/env python3
"""
Benchmark different models for code analysis performance
"""

import requests
import time
import json

def test_model_performance(model_name, test_query="Find memory management functions in utils codebase"):
    """Test a model's performance on code analysis"""
    print(f"🧪 Testing {model_name}")
    print("-" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": test_query}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            # Analyze response quality
            real_codebases = ["utils", "z80emu", "go-example-master"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            memory_terms = ["malloc", "free", "alloc", "memory", "buffer"]
            found_memory = [term for term in memory_terms if term.lower() in content.lower()]
            
            # Quality score
            quality_score = 0
            if len(tool_calls) > 0:
                quality_score += 40  # Used tools
            if found_real:
                quality_score += 30  # Found real codebases
            if found_memory:
                quality_score += 20  # Found relevant terms
            if len(content) > 100:
                quality_score += 10  # Substantial response
            
            result = {
                "model": model_name,
                "response_time": round(response_time, 2),
                "tool_calls": len(tool_calls),
                "content_length": len(content),
                "quality_score": quality_score,
                "found_real_data": len(found_real) > 0,
                "status": "success"
            }
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content length: {len(content)} chars")
            print(f"⭐ Quality score: {quality_score}/100")
            print(f"✅ Real data found: {len(found_real) > 0}")
            
            return result
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return {
                "model": model_name,
                "status": "http_error",
                "error_code": response.status_code,
                "response_time": round(response_time, 2)
            }
            
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout after {time.time() - start_time:.2f}s")
        return {
            "model": model_name,
            "status": "timeout",
            "response_time": round(time.time() - start_time, 2)
        }
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {
            "model": model_name,
            "status": "error",
            "error": str(e),
            "response_time": round(time.time() - start_time, 2)
        }

def benchmark_models():
    """Benchmark recommended models"""
    print("🚀 Code Analysis Model Benchmark")
    print("=" * 60)
    
    # Models to test (in order of recommendation)
    test_models = [
        "llama3:latest",           # Current baseline
        "qwen2.5-coder:7b",        # Top recommendation
        "deepseek-coder:6.7b",     # Second choice
        "codellama:7b-instruct",   # Third choice
        "smollm2:1.7b",           # Ultra-fast option
    ]
    
    results = []
    
    for i, model in enumerate(test_models, 1):
        print(f"\n[{i}/{len(test_models)}] Testing {model}")
        print("=" * 60)
        
        result = test_model_performance(model)
        results.append(result)
        
        # Small delay between tests
        if i < len(test_models):
            print("\n⏳ Waiting 5 seconds before next test...")
            time.sleep(5)
    
    return results

def analyze_results(results):
    """Analyze benchmark results"""
    print(f"\n📊 BENCHMARK RESULTS ANALYSIS")
    print("=" * 60)
    
    # Filter successful results
    successful = [r for r in results if r.get("status") == "success"]
    
    if not successful:
        print("❌ No successful tests - check model availability")
        return
    
    # Sort by quality score
    by_quality = sorted(successful, key=lambda x: x.get("quality_score", 0), reverse=True)
    
    # Sort by speed
    by_speed = sorted(successful, key=lambda x: x.get("response_time", 999))
    
    print(f"🏆 BEST QUALITY:")
    for i, result in enumerate(by_quality[:3], 1):
        model = result["model"]
        quality = result["quality_score"]
        time_taken = result["response_time"]
        print(f"   {i}. {model}: {quality}/100 quality ({time_taken}s)")
    
    print(f"\n⚡ FASTEST MODELS:")
    for i, result in enumerate(by_speed[:3], 1):
        model = result["model"]
        time_taken = result["response_time"]
        quality = result["quality_score"]
        print(f"   {i}. {model}: {time_taken}s ({quality}/100 quality)")
    
    # Find best balance
    print(f"\n⚖️ BEST BALANCE (Quality × Speed):")
    balanced = []
    for result in successful:
        quality = result.get("quality_score", 0)
        speed_score = max(1, 100 - result.get("response_time", 10) * 5)  # Penalize slow responses
        balance_score = (quality + speed_score) / 2
        balanced.append((result["model"], balance_score, quality, result["response_time"]))
    
    balanced.sort(key=lambda x: x[1], reverse=True)
    
    for i, (model, balance, quality, time_taken) in enumerate(balanced[:3], 1):
        print(f"   {i}. {model}: {balance:.1f} balance ({quality}/100 quality, {time_taken}s)")
    
    # Recommendation
    if balanced:
        best_model = balanced[0][0]
        print(f"\n🎯 RECOMMENDATION:")
        print(f"Switch to: {best_model}")
        print(f"Expected improvement: Better balance of speed and quality")

def main():
    print("🎯 Code Analysis Model Performance Benchmark")
    print("=" * 60)
    print("Testing models for code analysis speed and quality...")
    
    # Run benchmark
    results = benchmark_models()
    
    # Analyze results
    analyze_results(results)
    
    print(f"\n💡 Next Steps:")
    print("1. Choose a model based on the benchmark results")
    print("2. Update your code analyzer configuration")
    print("3. Test with your typical queries")

if __name__ == "__main__":
    main()
