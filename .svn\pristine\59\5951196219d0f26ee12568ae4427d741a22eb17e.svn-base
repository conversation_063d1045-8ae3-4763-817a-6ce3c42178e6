# OpenWebUI Code Analysis Tool Test Prompts

## 🚀 **Quick Start Tests**

### **1. System Status & Health**
```
status
system status
detailed status
comprehensive status
full status
check system health
get server status
show me the system information
```

### **2. Codebase Management**
```
list codebases
show available codebases
what codebases are available?
select codebase utils
choose the z80emu codebase
switch to modbus
get stats for utils
show statistics for z80emu
what are the metrics for the current codebase?
```

## 🔍 **Code Analysis Tests**

### **3. Basic Code Search**
```
find memory allocation functions
search for error handling code
show me socket programming examples
look for encryption functions
find all struct definitions
search for threading code
show me file I/O operations
find network protocol implementations
```

### **4. AI-Powered Analysis**
```
How does memory management work in this codebase?
What encryption algorithms are implemented?
Explain the error handling strategy
How is threading implemented?
What are the main classes and their purposes?
Describe the network protocol handling
How does the Z80 emulator work?
What security measures are in place?
```

### **5. Language-Specific Queries**
```
show me all C++ classes
find Python import statements
search for C# namespaces
look for C function definitions
find template implementations
show me inheritance hierarchies
search for virtual functions
find operator overloads
```

## 🎯 **Advanced Analysis Tests**

### **6. Comparative Analysis**
```
compare memory allocation between utils and modbus
what's the difference between TCP and UDP handling?
compare error handling in C vs C++ files
how do the different codebases handle threading?
what are the similarities between networking implementations?
```

### **7. Architecture & Design**
```
explain the overall architecture
what design patterns are used?
how is the code organized?
what are the main modules?
describe the data flow
how are dependencies managed?
what's the testing strategy?
```

### **8. Security & Quality**
```
what security vulnerabilities might exist?
how is input validation handled?
are there any buffer overflow risks?
what error conditions are not handled?
how is memory safety ensured?
what coding standards are followed?
```

## 🔧 **Technical Deep Dives**

### **9. Performance Analysis**
```
what are potential performance bottlenecks?
how is memory usage optimized?
what caching strategies are used?
how efficient are the algorithms?
what could be optimized for speed?
```

### **10. Integration & APIs**
```
what external APIs are used?
how does the system integrate with other components?
what communication protocols are implemented?
how is data serialization handled?
what configuration options are available?
```

## 🧪 **Edge Cases & Error Testing**

### **11. Error Scenarios**
```
what happens when memory allocation fails?
how are network timeouts handled?
what if a file cannot be opened?
how are invalid inputs processed?
what error recovery mechanisms exist?
```

### **12. Boundary Conditions**
```
how does the code handle empty inputs?
what about maximum buffer sizes?
how are null pointers managed?
what happens with very large files?
how are concurrent access conflicts resolved?
```

## 📊 **Context Format Testing**

### **13. Simple Queries (Should get clean format)**
```
find malloc
show printf
get main function
find struct
search class
```

### **14. Complex Queries (Should get detailed format)**
```
Analyze the complete memory management strategy across all modules and explain how it prevents leaks while maintaining performance
Compare and contrast the error handling approaches in the networking layer versus the application layer, including recovery mechanisms
Explain how the Z80 emulator maintains cycle-accurate timing while handling interrupts and memory banking
```

## 🎨 **Natural Language Variations**

### **15. Casual Language**
```
hey, show me some networking code
can you find encryption stuff?
what's this codebase about?
how does this thing work?
find me some examples of...
```

### **16. Formal Language**
```
Please provide an analysis of the memory allocation strategies employed in this codebase.
I require documentation regarding the error handling mechanisms.
Could you elaborate on the architectural patterns utilized?
What methodologies are implemented for data validation?
```

## 🔄 **Workflow Testing**

### **17. Complete Analysis Workflow**
```
1. "list codebases"
2. "select codebase utils"
3. "what is this codebase about?"
4. "show me the main functions"
5. "how does memory allocation work?"
6. "find any security issues"
7. "get detailed status"
```

### **18. Multi-Codebase Comparison**
```
1. "select codebase utils"
2. "how is networking handled?"
3. "switch to networking_project"
4. "how is networking handled here?"
5. "compare networking approaches between utils and networking_project"
```

## 🚨 **Stress Testing**

### **19. Rapid-Fire Queries**
```
find main
show classes
get functions
list structs
find errors
show memory
get network
find security
```

### **20. Long Complex Queries**
```
I need a comprehensive analysis of how this codebase handles concurrent access to shared resources, including mutex usage, lock-free algorithms, thread safety guarantees, potential race conditions, deadlock prevention mechanisms, and performance implications of the chosen synchronization strategies, with specific examples from the code and recommendations for improvements.
```

## 📝 **Expected Behaviors**

### **Auto-Context Injection**
- Code-related queries should automatically get relevant context
- Non-code queries should return empty (let OpenWebUI handle)

### **Format Selection**
- Simple searches: Clean, focused results
- Complex analysis: Detailed, comprehensive context
- Management queries: Routed to appropriate functions

### **Error Handling**
- Invalid codebase names: Clear error messages
- Network issues: Graceful degradation
- Empty results: Helpful suggestions

## 🎯 **Success Criteria**

✅ **Status functions work correctly**
✅ **Codebase selection and listing functional**
✅ **Code search returns relevant results**
✅ **AI analysis provides meaningful insights**
✅ **Auto-routing works for management queries**
✅ **Context format adapts to query complexity**
✅ **Error handling is graceful and informative**
✅ **Performance is acceptable (< 10s for most queries)**

## 💡 **Pro Tips for Testing**

1. **Start with status checks** to ensure system is healthy
2. **Test codebase selection** before running analysis queries
3. **Try both simple and complex queries** to test format selection
4. **Use natural language** - the tool should understand intent
5. **Test error scenarios** like invalid codebase names
6. **Compare results** between different codebases
7. **Check auto-routing** with management queries
