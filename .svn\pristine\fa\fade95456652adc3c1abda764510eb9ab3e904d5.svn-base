# Docker Web Management Deployment Guide

## 🎯 **Overview**

This guide shows how to deploy both the Code Analyzer Server (port 5002) and Web Management Interface (port 5003) in a single Docker container using supervisor to manage both processes.

## 🏗️ **Architecture**

```
┌─────────────────────────────────────────────────────────┐
│                Docker Container                         │
│  ┌─────────────────────┐    ┌─────────────────────────┐ │
│  │   Code Analyzer     │    │  Web Management         │ │
│  │   Server            │◄──►│  Interface              │ │
│  │   (Port 5002)       │    │  (Port 5003)            │ │
│  └─────────────────────┘    └─────────────────────────┘ │
│              ▲                          ▲               │
│              │         Supervisor       │               │
│              └──────────────────────────┘               │
└─────────────────────────────────────────────────────────┘
                         │
                    Docker Host
              ┌─────────────────────┐
              │ Port 5002 → 5002    │
              │ Port 5003 → 5003    │
              └─────────────────────┘
```

## 📁 **Required Files**

### **1. Updated Dockerfile**
```dockerfile
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies (including web management dependencies)
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir fastapi jinja2 python-multipart

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p templates && \
    mkdir -p /var/log/supervisor && \
    mkdir -p /var/run && \
    chmod 755 /var/log/supervisor

# Create supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose both ports
EXPOSE 5002 5003

# Health check for both services
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5002/health && curl -f http://localhost:5003/api/health || exit 1

# Set Python to unbuffered mode for immediate output
ENV PYTHONUNBUFFERED=1

# Command to run both applications via supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### **2. Supervisor Configuration (`supervisord.conf`)**
```ini
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:code-analyzer-server]
command=uvicorn main:app --host 0.0.0.0 --port 5002
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/code-analyzer-server.err.log
stdout_logfile=/var/log/supervisor/code-analyzer-server.out.log
environment=PYTHONUNBUFFERED="1"

[program:web-management-server]
command=python3 web_management_server.py
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/web-management-server.err.log
stdout_logfile=/var/log/supervisor/web-management-server.out.log
environment=PYTHONUNBUFFERED="1",CODE_ANALYZER_BASE_URL="http://localhost:5002"

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
```

### **3. Updated docker-compose.yml**
```yaml
  code-analyzer-server:
    container_name: code-analyzer-server
    build:
      context: ./code_analyzer_server # Code analyzer source is in subdirectory
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5002:5002" # Enhanced Code Analyzer server
      - "5003:5003" # Web Management Interface
    environment:
      - LOG_LEVEL=info
      - OLLAMA_HOST=http://ollama:11434  # Connect to existing Ollama
      - CHROMA_DB_BASE_PATH=/app/chroma_db
      - SOURCE_CODE_BASE_PATH=/app/source_code
      - USE_OLLAMA_EMBEDDINGS=true
      - DEBUG=true
      - PYTHONUNBUFFERED=1  # Ensure immediate output to logs
      - PYTHONIOENCODING=utf-8
      # Enhanced Framework Environment Variables
      - FRAMEWORK_MODE=production
      - GPU_DISCOVERY_ENABLED=true
      - LANGUAGE_REGISTRY_CACHE=true
      - PROCESSING_PIPELINE_PARALLEL=true
      # Web Management Environment Variables
      - CODE_ANALYZER_BASE_URL=http://localhost:5002
      - WEB_MANAGEMENT_PORT=5003
    volumes:
      - chroma_db:/app/chroma_db # Persist ChromaDB data
      - ./source_code:/app/source_code:ro # Mount source code read-only (at same level as docker-compose.yml)
      - supervisor_logs:/var/log/supervisor # Persist supervisor logs
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:5002/health && curl -f http://localhost:5003/api/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s

volumes:
  chroma_db:
  supervisor_logs:
```

## 📁 **Directory Structure**

```
~/home-ai-system/
├── docker-compose.yml
├── source_code/                    # Source code to analyze (same level as docker-compose.yml)
│   ├── utils/
│   ├── modbus/
│   └── ...
└── code_analyzer_server/           # Code analyzer application (subdirectory)
    ├── main.py
    ├── web_management_server.py
    ├── templates/
    │   └── dashboard.html
    ├── Dockerfile
    ├── supervisord.conf
    ├── requirements.txt
    └── ...
```

## 🚀 **Deployment Steps**

### **Step 1: Prepare Files**
```bash
# On home-ai-server, navigate to the main project directory
cd ~/home-ai-system

# Ensure directory structure is correct:
# - docker-compose.yml (in current directory)
# - source_code/ (in current directory)
# - code_analyzer_server/ (subdirectory with application code)

# Copy the updated files to code_analyzer_server/:
# - Dockerfile (updated)
# - supervisord.conf (new)
# - web_management_server.py (new)
# - templates/dashboard.html (new)
# - requirements.txt (updated with jinja2)
```

### **Step 2: Build and Deploy**
```bash
# Stop existing container
docker-compose down

# Rebuild with new configuration
docker-compose build --no-cache

# Start with both services
docker-compose up -d

# Check both services are running
docker logs code-analyzer-server
```

### **Step 3: Verify Deployment**
```bash
# Test Code Analyzer Server
curl http://home-ai-server.local:5002/health

# Test Web Management Interface
curl http://home-ai-server.local:5003/api/health

# Access Web Dashboard
# Open browser: http://home-ai-server.local:5003
```

## 📊 **Service Management**

### **Check Service Status**
```bash
# View all logs
docker logs code-analyzer-server

# View specific service logs
docker exec code-analyzer-server supervisorctl status

# View individual service logs
docker exec code-analyzer-server tail -f /var/log/supervisor/code-analyzer-server.out.log
docker exec code-analyzer-server tail -f /var/log/supervisor/web-management-server.out.log
```

### **Control Services**
```bash
# Restart specific service
docker exec code-analyzer-server supervisorctl restart code-analyzer-server
docker exec code-analyzer-server supervisorctl restart web-management-server

# Stop/start services
docker exec code-analyzer-server supervisorctl stop web-management-server
docker exec code-analyzer-server supervisorctl start web-management-server
```

## 🔍 **Monitoring & Health Checks**

### **Health Check Endpoints**
- **Code Analyzer**: `http://home-ai-server.local:5002/health`
- **Web Management**: `http://home-ai-server.local:5003/api/health`
- **Combined Health**: Docker health check tests both

### **Log Locations**
- **Supervisor Main**: `/var/log/supervisor/supervisord.log`
- **Code Analyzer Output**: `/var/log/supervisor/code-analyzer-server.out.log`
- **Code Analyzer Errors**: `/var/log/supervisor/code-analyzer-server.err.log`
- **Web Management Output**: `/var/log/supervisor/web-management-server.out.log`
- **Web Management Errors**: `/var/log/supervisor/web-management-server.err.log`

### **Log Directory Structure**
```
/var/log/supervisor/
├── supervisord.log                    # Supervisor daemon logs
├── code-analyzer-server.out.log       # Code analyzer stdout
├── code-analyzer-server.err.log       # Code analyzer stderr
├── web-management-server.out.log      # Web management stdout
└── web-management-server.err.log      # Web management stderr
```

### **Log Management Commands**
```bash
# View all supervisor logs
docker exec code-analyzer-server ls -la /var/log/supervisor/

# View specific service logs
docker exec code-analyzer-server tail -f /var/log/supervisor/code-analyzer-server.out.log
docker exec code-analyzer-server tail -f /var/log/supervisor/web-management-server.out.log

# View error logs
docker exec code-analyzer-server tail -f /var/log/supervisor/code-analyzer-server.err.log
docker exec code-analyzer-server tail -f /var/log/supervisor/web-management-server.err.log

# View supervisor daemon logs
docker exec code-analyzer-server tail -f /var/log/supervisor/supervisord.log
```

## 🌐 **Access Points**

### **Code Analyzer Server (Port 5002)**
- **Health**: `http://home-ai-server.local:5002/health`
- **API**: `http://home-ai-server.local:5002/tools/list_codebases`
- **Enhanced**: `http://home-ai-server.local:5002/enhanced_ask`

### **Web Management Interface (Port 5003)**
- **Dashboard**: `http://home-ai-server.local:5003`
- **API Health**: `http://home-ai-server.local:5003/api/health`
- **Metrics**: `http://home-ai-server.local:5003/api/metrics`

## 🔧 **Troubleshooting**

### **Common Issues**

**1. Container Won't Start**
```bash
# Check build logs
docker-compose build

# Check startup logs
docker logs code-analyzer-server
```

**2. Web Interface Not Accessible**
```bash
# Check if web management service is running
docker exec code-analyzer-server supervisorctl status

# Check web management logs
docker exec code-analyzer-server tail -f /var/log/supervisor/web-management-server.err.log
```

**3. Services Not Communicating**
```bash
# Test internal communication
docker exec code-analyzer-server curl http://localhost:5002/health
docker exec code-analyzer-server curl http://localhost:5003/api/health
```

### **Debug Commands**
```bash
# Enter container for debugging
docker exec -it code-analyzer-server bash

# Check supervisor status
supervisorctl status

# Restart supervisor
supervisorctl reread
supervisorctl update
```

## 🎯 **Benefits of This Approach**

### **✅ Advantages**
1. **Single Container**: Easier deployment and management
2. **Process Management**: Supervisor handles both services reliably
3. **Shared Resources**: Both services share the same environment
4. **Simplified Networking**: Internal communication via localhost
5. **Unified Logging**: All logs in one container
6. **Health Monitoring**: Combined health checks

### **📊 Expected Results**
- **Code Analyzer Server**: Fully functional on port 5002
- **Web Management Interface**: Beautiful dashboard on port 5003
- **Real-time Monitoring**: Live status of both services
- **Unified Management**: Single container to manage both services

## 🎉 **Final Result**

After deployment, you'll have:
- **http://home-ai-server.local:5002** - Code Analyzer Server API
- **http://home-ai-server.local:5003** - Web Management Dashboard

Both services running in a single, well-managed Docker container with supervisor ensuring reliability and proper process management!
