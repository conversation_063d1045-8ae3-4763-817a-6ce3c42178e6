#!/usr/bin/env python3
"""
Test if lynn-pc can reach the code analyzer server
"""

import requests
import subprocess
import socket

def test_connectivity_from_lynn_pc():
    print("🔍 Testing Connectivity from lynn-pc to home-ai-server")
    print("=" * 60)
    
    # Test different addresses lynn-pc might use to reach home-ai-server
    test_addresses = [
        "************",      # Current IP
        "home-ai-server.local",  # Hostname
        "home-ai-server",    # Short hostname
    ]
    
    for addr in test_addresses:
        print(f"\n🧪 Testing {addr}")
        print("-" * 30)
        
        # Test ping
        try:
            result = subprocess.run(
                ["ping", "-c", "1", addr], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            if result.returncode == 0:
                print(f"   ✅ Ping successful")
            else:
                print(f"   ❌ Ping failed")
                continue
        except:
            print(f"   ❌ Ping failed (exception)")
            continue
        
        # Test port 5002
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((addr, 5002))
            sock.close()
            
            if result == 0:
                print(f"   ✅ Port 5002 accessible")
            else:
                print(f"   ❌ Port 5002 not accessible")
                continue
        except:
            print(f"   ❌ Port test failed")
            continue
        
        # Test HTTP
        try:
            response = requests.get(f"http://{addr}:5002/health", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ HTTP accessible")
                data = response.json()
                print(f"   📊 Collections: {len(data.get('collections', []))}")
                print(f"   🎯 WORKING ADDRESS: {addr}")
                return addr
            else:
                print(f"   ❌ HTTP error: {response.status_code}")
        except Exception as e:
            print(f"   ❌ HTTP failed: {e}")
    
    print(f"\n❌ No working address found from lynn-pc perspective")
    return None

def main():
    print("🚀 lynn-pc Connectivity Test")
    print("This simulates what the tool would experience from lynn-pc")
    print("=" * 60)
    
    working_addr = test_connectivity_from_lynn_pc()
    
    if working_addr:
        print(f"\n🎉 SOLUTION FOUND!")
        print(f"✅ lynn-pc can reach home-ai-server at: {working_addr}")
        print(f"🔧 Update tool configuration:")
        print(f"   Change: http://************:5002")
        print(f"   To:     http://{working_addr}:5002")
        
        print(f"\n📋 Steps to fix:")
        print(f"1. Go to OpenWebUI → Workspace → Tools → code_analyzer_tool")
        print(f"2. Edit the tool")
        print(f"3. Change code_analyzer_server_url to: http://{working_addr}:5002")
        print(f"4. Save and test with LYNN-PC.llama3:latest")
        
    else:
        print(f"\n❌ NETWORK ISSUE")
        print(f"lynn-pc cannot reach home-ai-server on any tested address")
        print(f"🔧 Possible solutions:")
        print(f"1. Check network configuration")
        print(f"2. Ensure both machines are on same network")
        print(f"3. Check firewall settings")
        print(f"4. Use different network addressing")

if __name__ == "__main__":
    main()
