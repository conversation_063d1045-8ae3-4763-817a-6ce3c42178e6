#!/usr/bin/env python3
"""
Test and fix the LYNN-PC tool configuration
"""

import requests
import json

def test_tool_with_debug():
    """Test the tool with detailed debugging"""
    print("🔧 Testing LYNN-PC Tool with Debug Info")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test with explicit valve configuration
    payload = {
        "model": "LYNN-PC.llama3:latest",
        "messages": [
            {
                "role": "user", 
                "content": "Use the code analyzer tool to list codebases. Make sure to connect to http://192.168.0.77:5002"
            }
        ],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print("📤 Sending request with explicit server instruction...")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120  # 2 minutes
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"✅ Response received")
            print(f"   Tool calls: {len(tool_calls)}")
            print(f"   Content length: {len(content)} chars")
            
            # Show tool calls if any
            if tool_calls:
                print(f"\n🛠️ Tool Calls:")
                for i, tc in enumerate(tool_calls):
                    func_name = tc.get("function", {}).get("name", "unknown")
                    args = tc.get("function", {}).get("arguments", "{}")
                    print(f"   {i+1}. Function: {func_name}")
                    print(f"      Arguments: {args}")
            
            # Show content preview
            print(f"\n📝 Response Content:")
            print("-" * 30)
            print(content[:500] + ("..." if len(content) > 500 else ""))
            print("-" * 30)
            
            # Analyze response
            real_codebases = ["utils", "z80emu", "library-management-system", "test_project", "bookstore"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            if tool_calls or found_real:
                print(f"\n🎉 SUCCESS: Tool is working!")
                if found_real:
                    print(f"   Found real codebases: {found_real}")
                return True
            else:
                print(f"\n❌ Tool not working - no real codebases found")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out after 120 seconds")
        print(f"💡 This suggests the tool is trying to execute but hanging")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_direct_tool_call():
    """Test calling the code analyzer server directly"""
    print(f"\n🧪 Testing Direct Code Analyzer Call")
    print("-" * 40)
    
    try:
        # Test list_codebases endpoint directly
        response = requests.post("http://192.168.0.77:5002/tools/list_codebases",
                                json={}, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Direct call successful")
            print(f"   Codebases: {data.get('codebases', [])}")
            return True
        else:
            print(f"❌ Direct call failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Direct call exception: {e}")
        return False

def main():
    print("🚀 LYNN-PC Tool Fix Attempt")
    print("=" * 50)
    
    # Test direct server call first
    direct_works = test_direct_tool_call()
    
    if not direct_works:
        print(f"\n❌ Direct server call failed - server issue")
        return
    
    # Test tool with debug
    tool_works = test_tool_with_debug()
    
    print(f"\n📊 RESULTS")
    print("=" * 50)
    print(f"Direct server call: {'✅ Working' if direct_works else '❌ Failed'}")
    print(f"Tool via LYNN-PC:   {'✅ Working' if tool_works else '❌ Failed'}")
    
    if direct_works and not tool_works:
        print(f"\n🎯 DIAGNOSIS: Tool configuration or execution issue")
        print(f"💡 The server works but the tool doesn't use it properly")
        print(f"🔧 Possible solutions:")
        print(f"1. Check tool valve configuration in OpenWebUI")
        print(f"2. Verify tool has correct server URL")
        print(f"3. Check if tool execution context differs for remote models")
        print(f"4. Try updating tool code to handle remote model context")
        
        print(f"\n📋 Manual Steps:")
        print(f"1. Go to OpenWebUI → Workspace → Tools → code_analyzer_tool")
        print(f"2. Check the Valves section for code_analyzer_server_url")
        print(f"3. Ensure it's set to: http://192.168.0.77:5002")
        print(f"4. Save and test again")

if __name__ == "__main__":
    main()
