{".class": "MypyFile", "_fullname": "hybrid_code_analyzer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DirectOllamaCodeAnalyzer": {".class": "SymbolTableNode", "cross_ref": "direct_ollama_code_analyzer.DirectOllamaCodeAnalyzer", "kind": "Gdef"}, "HybridCodeAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer", "name": "HybridCodeAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "hybrid_code_analyzer", "mro": ["hybrid_code_analyzer.HybridCodeAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.__init__", "name": "__init__", "type": null}}, "_query_openwebui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer._query_openwebui", "name": "_query_openwebui", "type": null}}, "_query_remote_ollama": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer._query_remote_ollama", "name": "_query_remote_ollama", "type": null}}, "analyze_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "model", "prefer_fast"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.analyze_code", "name": "analyze_code", "type": null}}, "benchmark_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.benchmark_models", "name": "benchmark_models", "type": null}}, "get_available_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.get_available_models", "name": "get_available_models", "type": null}}, "model_routing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.model_routing", "name": "model_routing", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "openwebui_api_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.openwebui_api_key", "name": "openwebui_api_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "openwebui_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.openwebui_url", "name": "openwebui_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "remote_analyzers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.remote_analyzers", "name": "remote_analyzers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "hybrid_code_analyzer.HybridCodeAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "hybrid_code_analyzer.HybridCodeAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hybrid_code_analyzer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "demo_hybrid_analyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hybrid_code_analyzer.demo_hybrid_analyzer", "name": "demo_hybrid_analyzer", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\hybrid_code_analyzer.py"}