#!/usr/bin/env python3
"""
Compare LYNN-PC model vs local llama3:latest model
"""

import requests
import json

def test_model(model_name, description):
    print(f"\n🧪 Testing {description}")
    print(f"   Model: {model_name}")
    print("-" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"✅ Response received")
            print(f"   Tool calls: {len(tool_calls)}")
            print(f"   Content length: {len(content)} chars")
            
            # Show first 150 chars
            preview = content[:150] + "..." if len(content) > 150 else content
            print(f"   Preview: {preview}")
            
            # Analyze response type
            real_indicators = ["utils", "z80emu", "library-management-system", "test_project"]
            generic_indicators = ["wordpress", "react", "laravel", "popular", "open-source"]
            
            found_real = [ind for ind in real_indicators if ind.lower() in content.lower()]
            found_generic = [ind for ind in generic_indicators if ind.lower() in content.lower()]
            
            if tool_calls > 0:
                print(f"   🎉 TOOL CALLED - {len(tool_calls)} tool calls")
                return "tool_called"
            elif found_real:
                print(f"   ✅ TOOL WORKING - Found real codebases: {found_real}")
                return "tool_working"
            elif found_generic:
                print(f"   ❌ GENERIC RESPONSE - Found: {found_generic}")
                return "generic"
            else:
                print(f"   ❓ UNCLEAR RESPONSE")
                return "unclear"
                
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return "error"
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return "exception"

def main():
    print("🚀 Model Comparison Test")
    print("=" * 50)
    
    # Test local model (should work)
    local_result = test_model("llama3:latest", "Local llama3:latest (should work)")
    
    # Test LYNN-PC model (currently not working)
    lynn_pc_result = test_model("LYNN-PC.llama3:latest", "LYNN-PC llama3:latest (debugging)")
    
    # Summary
    print(f"\n📊 COMPARISON RESULTS")
    print("=" * 50)
    print(f"Local llama3:latest:     {local_result}")
    print(f"LYNN-PC.llama3:latest:   {lynn_pc_result}")
    
    if local_result in ["tool_called", "tool_working"] and lynn_pc_result == "generic":
        print(f"\n🎯 DIAGNOSIS: Tool configuration issue with LYNN-PC model")
        print(f"💡 The tool works locally but not with remote LYNN-PC model")
        print(f"🔧 Possible causes:")
        print(f"   1. Tool not properly enabled for LYNN-PC model")
        print(f"   2. Network connectivity issue during tool execution")
        print(f"   3. Tool server URL not accessible from lynn-pc")
        print(f"   4. Different tool configuration for remote models")
        
    elif local_result == lynn_pc_result:
        print(f"\n🤔 Both models behave the same - might be a broader issue")
        
    else:
        print(f"\n🔍 Mixed results - need further investigation")

if __name__ == "__main__":
    main()
