#!/usr/bin/env python3
"""
Test proxy with explicit URL override in the prompt
"""

import requests

def test_proxy_with_url_override():
    """Test if we can override the tool URL in the prompt"""
    print("🧪 Testing Proxy with URL Override")
    print("=" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Try to override the tool URL in the prompt
    override_prompt = """Use the code_analyzer_tool to list codebases.

IMPORTANT: Configure the tool to use server URL: http://192.168.0.32:5002 (lynn-pc proxy)

This proxy forwards requests to the actual code analyzer server. Please use this URL and list the available codebases."""
    
    payload = {
        "model": "LYNN-PC.llama3:latest",
        "messages": [{"role": "user", "content": override_prompt}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print("📤 Testing with URL override in prompt...")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"✅ Response received")
            print(f"Tool calls: {len(tool_calls)}")
            print(f"Content length: {len(content)} chars")
            
            # Check for real data
            real_codebases = ["utils", "z80emu", "library-management-system", "TypeScript-Node-Starter-master"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            print(f"Real codebases found: {found_real}")
            print(f"Content preview: {content[:200]}...")
            
            if len(tool_calls) > 0 or found_real:
                print("🎉 SUCCESS: Proxy working with URL override!")
                return True
            else:
                print("❌ Still not working - need to update tool configuration")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Timeout - tool configuration issue")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_direct_proxy_call():
    """Test calling the proxy directly to verify it works"""
    print(f"\n🔍 Testing Direct Proxy Call")
    print("-" * 30)
    
    try:
        # Test the proxy's list_codebases endpoint directly
        response = requests.post(
            "http://192.168.0.32:5002/tools/list_codebases",
            json={},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Direct proxy call successful")
            print(f"Response: {data}")
            return True
        else:
            print(f"❌ Direct proxy call failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Direct proxy call error: {e}")
        return False

def main():
    print("🚀 Testing Proxy with Configuration")
    print("=" * 50)
    
    # Test direct proxy call first
    proxy_direct = test_direct_proxy_call()
    
    if not proxy_direct:
        print("❌ Proxy not working - check lynn-pc proxy setup")
        return
    
    # Test with URL override
    tool_working = test_proxy_with_url_override()
    
    print(f"\n🎯 RESULTS")
    print("=" * 50)
    if tool_working:
        print("🎉 PROXY SOLUTION SUCCESSFUL!")
        print("✅ Remote model can use tools via proxy")
    else:
        print("❌ Need to update tool configuration")
        print("💡 Next steps:")
        print("1. Update code_analyzer_tool configuration")
        print("2. Change server URL to: http://192.168.0.32:5002")
        print("3. Or add model-specific system prompt")

if __name__ == "__main__":
    main()
