#!/usr/bin/env python3
"""
Debug the specific "show memory management in utils codebase" query
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
import requests
import json

def test_memory_management_query_step_by_step():
    """Test the exact memory management query step by step"""
    print("🔍 Testing Memory Management Query Step by Step")
    print("=" * 70)
    
    # First, test the tool execution directly
    print("1️⃣ Testing tool execution directly:")
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test the query_codebase tool directly
    tool_result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'memory management malloc free alloc',
        'max_results': 10
    })
    
    print(f"Direct tool result: {json.dumps(tool_result, indent=2)[:500]}...")
    
    if 'results' in tool_result and tool_result['results']:
        print(f"✅ Tool returns {len(tool_result['results'])} results")
        
        # Show first result
        first_result = tool_result['results'][0]
        content = first_result.get('content', 'No content')
        metadata = first_result.get('metadata', {})
        
        print(f"📝 First result content: {content[:200]}...")
        print(f"📊 Metadata: {metadata}")
        
        # Check if it's real C/C++ code
        if any(term in content.lower() for term in ['malloc', 'free', 'void', 'int', 'char']):
            print(f"✅ Contains real C/C++ memory management code")
        else:
            print(f"❌ Doesn't contain expected C/C++ code")
    else:
        print(f"❌ Tool returns no results")
        return False
    
    # Now test the full T5810 integration
    print(f"\n2️⃣ Testing full T5810 integration:")
    
    query = "Find and show memory management functions in the utils codebase. Use the query_codebase tool with codebase_name='utils' and search for malloc, free, alloc functions. Show me the actual code you find."
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📝 T5810 result length: {len(result)}")
    print(f"📊 T5810 result preview:")
    print(result[:600] + "..." if len(result) > 600 else result)
    
    # Check if T5810 result contains real code
    if any(term in result.lower() for term in ['malloc', 'free', 'void *', 'size_t']):
        print(f"✅ T5810 result contains real C/C++ code")
        return True
    else:
        print(f"❌ T5810 result is still hallucinating")
        return False

def test_different_memory_queries():
    """Test different variations of memory management queries"""
    print(f"\n🧪 Testing Different Memory Query Variations")
    print("=" * 70)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    queries = [
        "Search the utils codebase for malloc and free functions",
        "Show me memory allocation functions from utils codebase",
        "Find C memory management code in utils using query_codebase tool",
        "Use query_codebase tool: codebase_name='utils', query='malloc free memory allocation'"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n[{i}] Query: {query}")
        print("-" * 50)
        
        try:
            result = analyzer.chat_with_tools(query)
            
            # Check for real code indicators
            real_code_indicators = ['malloc', 'free', 'void *', 'size_t', '#include', 'struct']
            found_indicators = [ind for ind in real_code_indicators if ind in result.lower()]
            
            print(f"🔍 Real code indicators found: {found_indicators}")
            print(f"📝 Result preview: {result[:200]}...")
            
            if found_indicators:
                print(f"✅ Contains real code")
            else:
                print(f"❌ Still hallucinating")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def debug_t5810_tool_calling():
    """Debug T5810's tool calling for memory management query"""
    print(f"\n🔧 Debugging T5810 Tool Calling")
    print("=" * 70)
    
    t5810_ollama = "http://************:11434"
    
    # Create the exact tool
    tools = [{
        'type': 'function',
        'function': {
            'name': 'query_codebase',
            'description': 'Search for specific code patterns or functions in a codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {
                        'type': 'string',
                        'description': 'Name of the codebase to search in',
                    },
                    'query': {
                        'type': 'string',
                        'description': 'What to search for (functions, patterns, etc.)',
                    },
                    'max_results': {
                        'type': 'integer',
                        'description': 'Maximum number of results to return (default: 10)',
                        'default': 10
                    }
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Find memory management functions in the utils codebase. Use the query_codebase tool with codebase_name="utils" and query="malloc free alloc memory". Show me the actual code results.'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    try:
        # Step 1: Initial request
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            
            print(f"🛠️  Tool calls made: {len(tool_calls)}")
            
            if tool_calls:
                for tc in tool_calls:
                    func = tc.get('function', {})
                    name = func.get('name', '')
                    args = func.get('arguments', '{}')
                    print(f"   Tool: {name}")
                    print(f"   Args: {args}")
                    
                    # Parse and check arguments
                    try:
                        parsed_args = json.loads(args)
                        print(f"   Parsed: {parsed_args}")
                        
                        if parsed_args.get('codebase_name') == 'utils':
                            print(f"   ✅ Correct codebase_name")
                        else:
                            print(f"   ❌ Wrong codebase_name: {parsed_args.get('codebase_name')}")
                            
                    except Exception as e:
                        print(f"   ❌ Failed to parse args: {e}")
            else:
                print(f"❌ No tool calls made")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    print("🔍 Debugging Memory Management Query Issue")
    print("=" * 70)
    
    # Test step by step
    step_by_step_works = test_memory_management_query_step_by_step()
    
    # Test different query variations
    test_different_memory_queries()
    
    # Debug tool calling
    debug_t5810_tool_calling()
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    
    if step_by_step_works:
        print(f"✅ Tool execution works and returns real code")
        print(f"✅ T5810 integration works correctly")
        print(f"💡 The issue may be with specific query phrasing")
    else:
        print(f"❌ Tool execution or T5810 integration has issues")
        print(f"💡 Need to fix the underlying tool or integration")

if __name__ == "__main__":
    main()
