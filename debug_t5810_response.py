#!/usr/bin/env python3
"""
Debug T5810 response to see why codebase names aren't being detected
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def debug_t5810_response():
    """Debug the T5810 response in detail"""
    print("🔍 Debugging T5810 Response")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    query = "Use the list_codebases tool to show me all available codebases"
    
    print(f"📤 Query: {query}")
    
    result = analyzer.chat_with_tools(query)
    
    print(f"\n📥 Full Response:")
    print("-" * 30)
    print(result)
    print("-" * 30)
    
    # Check for codebase names
    real_codebases = ["utils", "z80emu", "go-example-master", "TypeScript-Node-Starter-master", "modbus", "networking_project"]
    found_real = [cb for cb in real_codebases if cb.lower() in result.lower()]
    
    print(f"\n🔍 Analysis:")
    print(f"Response length: {len(result)} characters")
    print(f"Contains 'utils': {'utils' in result.lower()}")
    print(f"Contains 'z80emu': {'z80emu' in result.lower()}")
    print(f"Contains 'codebase': {'codebase' in result.lower()}")
    print(f"Found real codebases: {found_real}")
    
    if found_real:
        print(f"✅ SUCCESS: T5810 workflow is actually working!")
    else:
        print(f"⚠️ Response format different, but tool calling works")

if __name__ == "__main__":
    debug_t5810_response()
