{".class": "MypyFile", "_fullname": "json5", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "JSON5Encoder": {".class": "SymbolTableNode", "cross_ref": "json5.lib.JSON5Encoder", "kind": "Gdef"}, "QuoteStyle": {".class": "SymbolTableNode", "cross_ref": "json5.lib.QuoteStyle", "kind": "Gdef"}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "json5.version.VERSION", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "json5.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "json5.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "json5.version.__version__", "kind": "Gdef"}, "dump": {".class": "SymbolTableNode", "cross_ref": "json5.lib.dump", "kind": "Gdef"}, "dumps": {".class": "SymbolTableNode", "cross_ref": "json5.lib.dumps", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "cross_ref": "json5.lib.load", "kind": "Gdef"}, "loads": {".class": "SymbolTableNode", "cross_ref": "json5.lib.loads", "kind": "Gdef"}, "parse": {".class": "SymbolTableNode", "cross_ref": "json5.lib.parse", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\json5\\__init__.py"}