#!/usr/bin/env python3
"""
Test what methods OpenWebUI can detect as tool functions
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

def test_public_methods():
    """Test what public methods OpenWebUI would detect"""
    print("🔍 Testing Public Method Detection")
    print("=" * 60)
    
    tool = Tools()
    
    # Get all public methods (what OpenWebUI would see)
    public_methods = []
    for attr_name in dir(tool):
        if not attr_name.startswith('_'):  # Public methods
            attr = getattr(tool, attr_name)
            if callable(attr) and hasattr(attr, '__call__'):
                public_methods.append(attr_name)
    
    print(f"📊 Public methods OpenWebUI can detect:")
    for method in sorted(public_methods):
        print(f"   • {method}")
    
    # Check specifically for complexity-related methods
    complexity_methods = [m for m in public_methods if 'complexity' in m.lower()]
    print(f"\n🔍 Complexity-related methods:")
    if complexity_methods:
        for method in complexity_methods:
            print(f"   ❌ FOUND: {method} (this would be auto-detected)")
    else:
        print(f"   ✅ NONE FOUND (complexity method is now private)")
    
    return complexity_methods

async def test_query_routing():
    """Test that queries now route correctly"""
    print(f"\n🚀 Testing Query Routing After Fix")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    # Test the problematic query
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"📤 Query: {query}")
    
    # Check intent detection
    intent = tool._detect_query_intent(query)
    print(f"🎯 Detected intent: {intent}")
    
    if intent == "code_analysis":
        print(f"✅ CORRECT: Should get context injection")
    elif intent == "codebase_management":
        print(f"❌ WRONG: Still detecting as management")
        
        # Check what's triggering management detection
        query_lower = query.lower()
        management_phrases = [
            'list codebase', 'show codebase', 'available codebase',
            'select codebase', 'choose codebase', 'switch codebase',
            'process codebase', 'index codebase', 'delete codebase',
            'stats', 'statistics', 'status',
            'get stats', 'show stats', 'codebase stats',
            'get statistics', 'show statistics', 'codebase statistics',
            'analyze complexity', 'code complexity analysis', 'complexity analysis'
        ]
        
        found_phrases = [phrase for phrase in management_phrases if phrase in query_lower]
        print(f"   Triggering phrases: {found_phrases}")
    else:
        print(f"⚠️ UNEXPECTED: Intent = {intent}")

async def test_full_processing():
    """Test full processing to see if it works now"""
    print(f"\n🔧 Testing Full Processing")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"📤 Query: {query}")
    
    try:
        # This should now go through __call__ method and show debug marker
        result = await tool.__call__(query)
        
        print(f"📝 Result length: {len(result) if result else 0}")
        
        if result:
            # Check if it contains complexity analysis (bad)
            if "complexity indicators" in result.lower() or "nested loops" in result.lower():
                print(f"❌ FAILURE: Still getting complexity analysis")
                return False
            
            # Check if it got real code context (good)
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code/modbus", "holding register"]):
                print(f"✅ SUCCESS: Got real code context!")
                return True
            
            else:
                print(f"⚠️ UNCLEAR: Got some result but unclear what type")
                print(f"📊 Result preview: {result[:200]}...")
                return None
        else:
            print(f"❌ FAILURE: Empty result")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def main():
    print("🔍 Testing Tool Method Detection Fix")
    print("=" * 70)
    
    # Test what methods OpenWebUI can detect
    complexity_methods = test_public_methods()
    
    # Test query routing
    await test_query_routing()
    
    # Test full processing
    full_works = await test_full_processing()
    
    print(f"\n🎯 RESULTS")
    print("=" * 70)
    
    if not complexity_methods:
        print(f"✅ SUCCESS: analyze_complexity method is now private")
        print(f"✅ OpenWebUI can no longer auto-detect it as a tool function")
    else:
        print(f"❌ ISSUE: Still has public complexity methods: {complexity_methods}")
    
    if full_works is True:
        print(f"✅ SUCCESS: Query routing now works correctly")
        print(f"✅ Should get real modbus code instead of complexity analysis")
    elif full_works is False:
        print(f"❌ FAILURE: Still has routing issues")
    else:
        print(f"⚠️ UNCLEAR: Need more investigation")

if __name__ == "__main__":
    asyncio.run(main())
