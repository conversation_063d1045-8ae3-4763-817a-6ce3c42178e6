#!/usr/bin/env python3
"""
Debug T5810 connectivity to home-ai-server
This script helps identify network connectivity issues
"""

import requests
import socket
import subprocess
import platform
import time

def test_dns_resolution():
    """Test if T5810 can resolve home-ai-server.local"""
    print("🔍 Testing DNS Resolution")
    print("-" * 30)
    
    try:
        # Try to resolve the hostname
        ip = socket.gethostbyname('home-ai-server.local')
        print(f"✅ home-ai-server.local resolves to: {ip}")
        return ip
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        print("💡 T5810 cannot resolve 'home-ai-server.local'")
        return None

def test_ping_connectivity(target):
    """Test ping connectivity"""
    print(f"\n🏓 Testing Ping to {target}")
    print("-" * 30)
    
    try:
        # Determine ping command based on OS
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "3", target]
        else:
            cmd = ["ping", "-c", "3", target]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Ping successful to {target}")
            return True
        else:
            print(f"❌ Ping failed to {target}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Ping timeout to {target}")
        return False
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return False

def test_port_connectivity(host, port):
    """Test if specific port is reachable"""
    print(f"\n🔌 Testing Port {port} on {host}")
    print("-" * 30)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open on {host}")
            return True
        else:
            print(f"❌ Port {port} is closed/filtered on {host}")
            return False
            
    except Exception as e:
        print(f"❌ Port test error: {e}")
        return False

def test_http_connectivity(url):
    """Test HTTP connectivity to the code analyzer server"""
    print(f"\n🌐 Testing HTTP connectivity to {url}")
    print("-" * 30)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ HTTP request successful")
        print(f"   Status: {response.status_code}")
        print(f"   Response length: {len(response.text)} chars")
        
        # Try to parse as JSON if it looks like our health endpoint
        if url.endswith('/health'):
            try:
                data = response.json()
                print(f"   Server info: {data.get('server', 'Unknown')}")
                print(f"   Collections: {len(data.get('collections', []))}")
            except:
                print("   Response is not JSON (might be HTML)")
        
        return True
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Connection timeout to {url}")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ HTTP error: {e}")
        return False

def test_alternative_addresses():
    """Test alternative ways to reach home-ai-server"""
    print(f"\n🔄 Testing Alternative Addresses")
    print("-" * 30)
    
    # Common alternative addresses to try
    alternatives = [
        "localhost",
        "127.0.0.1",
        "*************",  # Common home network IP
        "*************",  # Another common range
        "**********",     # Another common range
    ]
    
    working_addresses = []
    
    for addr in alternatives:
        print(f"   Testing {addr}:5002...")
        if test_port_connectivity(addr, 5002):
            working_addresses.append(addr)
    
    return working_addresses

def main():
    print("🚀 T5810 Connectivity Debugging")
    print("=" * 50)
    print("This script should be run ON the T5810 machine")
    print("=" * 50)
    
    # Step 1: DNS Resolution
    resolved_ip = test_dns_resolution()
    
    # Step 2: Ping test
    if resolved_ip:
        ping_success = test_ping_connectivity("home-ai-server.local")
        ping_ip_success = test_ping_connectivity(resolved_ip)
    else:
        print("\n⚠️ Skipping ping tests due to DNS failure")
        ping_success = False
        ping_ip_success = False
    
    # Step 3: Port connectivity
    if resolved_ip:
        port_success = test_port_connectivity(resolved_ip, 5002)
    else:
        port_success = False
    
    # Step 4: HTTP connectivity
    if port_success:
        http_success = test_http_connectivity(f"http://{resolved_ip}:5002/health")
    else:
        http_success = False
    
    # Step 5: Try alternative addresses
    working_addresses = test_alternative_addresses()
    
    # Summary and recommendations
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    
    if http_success:
        print("🎉 SUCCESS: T5810 can reach the code analyzer server!")
        print(f"✅ Use this URL in the tool: http://{resolved_ip}:5002")
    else:
        print("❌ PROBLEM: T5810 cannot reach the code analyzer server")
        
        print(f"\n🔧 TROUBLESHOOTING STEPS:")
        
        if not resolved_ip:
            print("1. DNS Issue:")
            print("   - Add entry to T5810's hosts file:")
            print("   - Find home-ai-server IP: hostname -I")
            print("   - Add: [IP] home-ai-server.local")
        
        if resolved_ip and not ping_success:
            print("2. Network Issue:")
            print("   - Check if machines are on same network")
            print("   - Check firewall settings")
            print("   - Verify network routing")
        
        if ping_success and not port_success:
            print("3. Port/Firewall Issue:")
            print("   - Check if port 5002 is open on home-ai-server")
            print("   - Check firewall rules")
            print("   - Verify code analyzer server is running")
        
        if working_addresses:
            print(f"4. Alternative addresses that work:")
            for addr in working_addresses:
                print(f"   - Try: http://{addr}:5002")
    
    print(f"\n💡 NEXT STEPS:")
    print("1. Run this script ON the T5810 machine")
    print("2. Fix any connectivity issues found")
    print("3. Update the tool configuration with working URL")
    print("4. Test the tool again with T5810 - llama3:latest")

if __name__ == "__main__":
    main()
