#!/usr/bin/env python3
"""
Debug the 400 BAD REQUEST error from the search endpoint
"""

import requests
import json

def test_search_endpoints():
    """Test different search endpoints to see which one causes 400 error"""
    print("🔍 Testing Search Endpoints for 400 Error")
    print("=" * 60)
    
    # Test different server URLs that might be causing the issue
    servers_to_test = [
        "http://************:5002",  # lynn-pc (from error message)
        "http://************:5002",  # home-ai-server IP
        "http://home-ai-server.local:5002"  # home-ai-server hostname
    ]
    
    query_payload = {
        "query": "holding register",
        "codebase_name": "modbus",
        "n_results": 5
    }
    
    for server in servers_to_test:
        print(f"\n📤 Testing: {server}")
        
        try:
            response = requests.post(
                f"{server}/search",
                json=query_payload,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    results = data['results']
                    print(f"   ✅ SUCCESS: Found {len(results)} results")
                    
                    if results:
                        first_result = results[0]
                        content = first_result.get('content', '')
                        if 'holding' in content.lower() or 'register' in content.lower():
                            print(f"   🎯 Contains holding register content")
                        else:
                            print(f"   ⚠️ May not contain holding register content")
                else:
                    print(f"   ⚠️ Unexpected response format")
                    
            elif response.status_code == 400:
                print(f"   ❌ 400 BAD REQUEST")
                print(f"   Error details: {response.text}")
                
                # Try to parse error details
                try:
                    error_data = response.json()
                    print(f"   Error JSON: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"   Raw error: {response.text}")
                    
            else:
                print(f"   ❌ HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_different_payloads():
    """Test different payload formats to see what causes 400 error"""
    print(f"\n🧪 Testing Different Payload Formats")
    print("=" * 60)
    
    # Use the server that worked in our previous tests
    server = "http://************:5002"
    
    payloads_to_test = [
        # Standard payload
        {"query": "holding register", "codebase_name": "modbus", "n_results": 5},
        
        # Minimal payload
        {"query": "holding register", "codebase_name": "modbus"},
        
        # Different parameter names (maybe the API changed)
        {"q": "holding register", "codebase": "modbus", "limit": 5},
        
        # String n_results (in case of type issues)
        {"query": "holding register", "codebase_name": "modbus", "n_results": "5"},
        
        # Empty query (edge case)
        {"query": "", "codebase_name": "modbus", "n_results": 5},
        
        # Missing codebase_name
        {"query": "holding register", "n_results": 5}
    ]
    
    for i, payload in enumerate(payloads_to_test, 1):
        print(f"\n{i}️⃣ Testing payload: {payload}")
        
        try:
            response = requests.post(
                f"{server}/search",
                json=payload,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                print(f"   ✅ SUCCESS: {len(results)} results")
            elif response.status_code == 400:
                print(f"   ❌ 400 BAD REQUEST")
                print(f"   Error: {response.text}")
            else:
                print(f"   ⚠️ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_lynn_pc_server():
    """Test lynn-pc server specifically since that's where the 400 error came from"""
    print(f"\n🖥️ Testing lynn-pc Server Specifically")
    print("=" * 60)
    
    lynn_pc_url = "http://************:5002"
    
    # First, test if the server is even running
    try:
        response = requests.get(f"{lynn_pc_url}/health", timeout=10)
        print(f"Health check: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ lynn-pc server is running")
            
            # Test if it has the modbus codebase
            try:
                cb_response = requests.get(f"{lynn_pc_url}/codebases", timeout=10)
                if cb_response.status_code == 200:
                    data = cb_response.json()
                    codebases = [cb.get('name', '') for cb in data.get('codebases', [])]
                    print(f"📊 Available codebases: {codebases}")
                    
                    if 'modbus' in codebases:
                        print(f"✅ modbus codebase is available on lynn-pc")
                    else:
                        print(f"❌ modbus codebase NOT available on lynn-pc")
                        print(f"💡 This could be why the search fails with 400")
                else:
                    print(f"❌ Cannot list codebases: {cb_response.status_code}")
            except Exception as e:
                print(f"❌ Codebase listing failed: {e}")
                
        else:
            print(f"❌ lynn-pc server health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Cannot reach lynn-pc server: {e}")
        print(f"💡 lynn-pc may be down or not running the code analyzer")

def main():
    print("🔍 Debugging 400 BAD REQUEST Error")
    print("=" * 70)
    print("Goal: Understand why lynn-pc returns 400 for holding register query")
    
    # Test different servers
    test_search_endpoints()
    
    # Test different payload formats
    test_different_payloads()
    
    # Test lynn-pc specifically
    test_lynn_pc_server()
    
    print(f"\n💡 LIKELY CAUSES:")
    print("1. lynn-pc doesn't have the modbus codebase indexed")
    print("2. lynn-pc code analyzer server is using different API format")
    print("3. lynn-pc server is down or misconfigured")
    print("4. OpenWebUI tool is routing to wrong server")

if __name__ == "__main__":
    main()
