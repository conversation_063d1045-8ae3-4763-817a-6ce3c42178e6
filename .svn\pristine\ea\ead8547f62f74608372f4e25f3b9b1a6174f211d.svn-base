"""
Embedding Model Configuration System
Provides configurable embedding model support with token limits and specifications
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class EmbeddingModelConfig:
    """Configuration for an embedding model"""
    name: str
    max_tokens: int
    dimensions: int
    description: str
    context_window: int
    recommended_chunk_size: int
    
    @property
    def safe_chunk_size(self) -> int:
        """Safe chunk size (80% of max tokens)"""
        return int(self.max_tokens * 0.8)

class EmbeddingConfigManager:
    """Manages embedding model configurations"""
    
    def __init__(self):
        self.configs = {
            "nomic-embed-text": EmbeddingModelConfig(
                name="nomic-embed-text",
                max_tokens=8192,
                dimensions=384,
                description="Nomic's open-source embedding model optimized for code",
                context_window=8192,
                recommended_chunk_size=6554  # 80% of 8192
            ),
            "mxbai-embed-large": EmbeddingModelConfig(
                name="mxbai-embed-large",
                max_tokens=512,
                dimensions=1024,
                description="High-performance embedding model",
                context_window=512,
                recommended_chunk_size=410  # 80% of 512
            ),
            "snowflake-arctic-embed2": EmbeddingModelConfig(
                name="snowflake-arctic-embed2",
                max_tokens=8192,
                dimensions=1024,
                description="Multilingual, latest generation embedding model",
                context_window=8192,
                recommended_chunk_size=6554  # 80% of 8192
            ),
            "bge-m3": EmbeddingModelConfig(
                name="bge-m3",
                max_tokens=8192,
                dimensions=1024,
                description="Multi-functional, versatile embedding model",
                context_window=8192,
                recommended_chunk_size=6554  # 80% of 8192
            ),
            "all-minilm": EmbeddingModelConfig(
                name="all-minilm",
                max_tokens=512,
                dimensions=384,
                description="Lightweight, fast embedding model",
                context_window=512,
                recommended_chunk_size=410  # 80% of 512
            ),
            "text-embedding-ada-002": EmbeddingModelConfig(
                name="text-embedding-ada-002",
                max_tokens=8191,
                dimensions=1536,
                description="OpenAI's embedding model",
                context_window=8191,
                recommended_chunk_size=6553  # 80% of 8191
            )
        }
    
    def get_config(self, model_name: str) -> EmbeddingModelConfig:
        """Get configuration for embedding model"""
        return self.configs.get(model_name, self.configs["nomic-embed-text"])
    
    def get_current_model(self) -> str:
        """Get current embedding model from environment or default"""
        return os.getenv("EMBEDDING_MODEL", "nomic-embed-text")
    
    def get_current_config(self) -> EmbeddingModelConfig:
        """Get current embedding model configuration"""
        return self.get_config(self.get_current_model())
    
    def list_available_models(self) -> Dict[str, EmbeddingModelConfig]:
        """List all available embedding models"""
        return self.configs.copy()
    
    def set_model(self, model_name: str) -> bool:
        """Set embedding model (for runtime configuration)"""
        if model_name in self.configs:
            os.environ["EMBEDDING_MODEL"] = model_name
            return True
        return False
    
    def estimate_tokens(self, text: str) -> int:
        """Rough token estimation (1 token ≈ 4 characters for most models)"""
        return len(text) // 4
    
    def is_chunk_size_valid(self, text: str, model_name: Optional[str] = None) -> bool:
        """Check if chunk size is valid for embedding model"""
        config = self.get_config(model_name or self.get_current_model())
        token_count = self.estimate_tokens(text)
        return token_count <= config.safe_chunk_size
    
    def get_model_summary(self) -> str:
        """Get summary of current model configuration"""
        config = self.get_current_config()
        return f"""
🔧 **Current Embedding Model: {config.name}**
   • Max Tokens: {config.max_tokens:,}
   • Safe Chunk Size: {config.safe_chunk_size:,} tokens
   • Dimensions: {config.dimensions}
   • Context Window: {config.context_window:,}
   • Description: {config.description}
"""

# Global instance
embedding_config_manager = EmbeddingConfigManager()

def get_embedding_config(model_name: Optional[str] = None) -> EmbeddingModelConfig:
    """Convenience function to get embedding configuration"""
    return embedding_config_manager.get_config(model_name or embedding_config_manager.get_current_model())

def estimate_tokens(text: str) -> int:
    """Convenience function to estimate tokens"""
    return embedding_config_manager.estimate_tokens(text)

def is_chunk_valid(text: str, model_name: Optional[str] = None) -> bool:
    """Convenience function to check chunk validity"""
    return embedding_config_manager.is_chunk_size_valid(text, model_name)
