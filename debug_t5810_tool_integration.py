#!/usr/bin/env python3
"""
Debug T5810 tool result integration issue
"""

import requests
import json

def test_t5810_tool_integration_step_by_step():
    """Test T5810 tool integration step by step to see where it fails"""
    print("🔍 Testing T5810 Tool Integration Step by Step")
    print("=" * 70)
    
    t5810_ollama = "http://************:11434"
    
    # Create a simple tool
    tools = [{
        'type': 'function',
        'function': {
            'name': 'list_codebases',
            'description': 'List all available codebases for analysis',
            'parameters': {
                'type': 'object',
                'properties': {},
                'required': [],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Use the list_codebases tool to show me all available codebases. Make sure to actually call the tool and use its results in your response.'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print(f"📤 Step 1: Sending initial request to T5810...")
    
    try:
        # Step 1: Initial request
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ T5810 responded")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Initial content: {content[:200]}...")
            
            if not tool_calls:
                print(f"❌ T5810 didn't call any tools - this is the problem!")
                return False
            
            # Step 2: Execute tools and add results
            messages.append(message)  # Add assistant's response with tool calls
            
            for tool_call in tool_calls:
                function = tool_call.get('function', {})
                tool_name = function.get('name', '')
                arguments_str = function.get('arguments', '{}')
                
                print(f"\n📤 Step 2: Executing tool '{tool_name}'...")
                
                # Parse arguments
                try:
                    arguments = json.loads(arguments_str)
                except:
                    arguments = {}
                
                # Execute the tool manually (simulate what should happen)
                if tool_name == 'list_codebases':
                    # Get real data from code analyzer
                    try:
                        tool_response = requests.get("http://192.168.0.77:5002/codebases", timeout=30)
                        if tool_response.status_code == 200:
                            tool_data = tool_response.json()
                            codebases = [cb['name'] for cb in tool_data.get('codebases', [])]
                            tool_result = {"codebases": codebases}
                        else:
                            tool_result = {"error": f"Failed to get codebases: {tool_response.status_code}"}
                    except Exception as e:
                        tool_result = {"error": str(e)}
                else:
                    tool_result = {"error": f"Unknown tool: {tool_name}"}
                
                print(f"🔧 Tool result: {tool_result}")
                
                # Add tool result to messages
                messages.append({
                    'role': 'tool',
                    'content': json.dumps(tool_result)
                })
            
            # Step 3: Get final response from model
            print(f"\n📤 Step 3: Getting final response with tool results...")
            
            final_payload = {
                'model': 'llama3.1:latest',
                'messages': messages,
                'stream': False
            }
            
            final_response = requests.post(f"{t5810_ollama}/api/chat", json=final_payload, timeout=120)
            
            if final_response.status_code == 200:
                final_data = final_response.json()
                final_content = final_data.get('message', {}).get('content', '')
                
                print(f"✅ Final response received")
                print(f"📝 Final content length: {len(final_content)}")
                print(f"📊 Final content preview:")
                print(final_content[:500] + "..." if len(final_content) > 500 else final_content)
                
                # Check if the final response uses the tool results
                if any(cb in final_content.lower() for cb in ['utils', 'z80emu', 'modbus']):
                    print(f"✅ SUCCESS: Final response includes real codebase names!")
                    return True
                else:
                    print(f"❌ FAILURE: Final response doesn't use tool results")
                    print(f"🔍 Tool result was: {tool_result}")
                    return False
            else:
                print(f"❌ Final response error: {final_response.status_code}")
                return False
                
        else:
            print(f"❌ Initial response error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_simple_tool_call():
    """Test a very simple tool call to see if T5810 can handle it"""
    print("\n🧪 Testing Simple Tool Call")
    print("=" * 50)
    
    t5810_ollama = "http://************:11434"
    
    # Very simple tool
    tools = [{
        'type': 'function',
        'function': {
            'name': 'get_current_time',
            'description': 'Get the current time',
            'parameters': {
                'type': 'object',
                'properties': {},
                'required': [],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'What time is it? Use the get_current_time tool.'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    try:
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"Tool calls: {len(tool_calls)}")
            print(f"Content: {content}")
            
            if tool_calls:
                print(f"✅ T5810 can call tools")
                return True
            else:
                print(f"❌ T5810 is not calling tools at all")
                return False
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🔍 Debugging T5810 Tool Integration Issue")
    print("=" * 70)
    
    # Test 1: Simple tool call
    simple_works = test_simple_tool_call()
    
    # Test 2: Full integration
    if simple_works:
        integration_works = test_t5810_tool_integration_step_by_step()
    else:
        print(f"❌ Skipping integration test - simple tool calls don't work")
        integration_works = False
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    
    if not simple_works:
        print(f"❌ ROOT CAUSE: T5810 is not calling tools at all")
        print(f"💡 SOLUTION: Check T5810 model configuration or tool format")
    elif not integration_works:
        print(f"❌ ROOT CAUSE: T5810 calls tools but doesn't use results")
        print(f"💡 SOLUTION: Fix tool result integration in DirectOllamaCodeAnalyzer")
    else:
        print(f"✅ T5810 tool integration is working correctly")

if __name__ == "__main__":
    main()
