#!/usr/bin/env python3
"""
Check what's needed to get v3.2_enhanced status
"""

import requests
import json

def check_metadata_fields(codebase_name):
    """Check what metadata fields a codebase actually has"""
    print(f"🔍 Checking metadata fields for: {codebase_name}")
    
    try:
        # Get codebase stats to see metadata structure
        response = requests.post(
            "http://************:5002/tools/get_codebase_stats",
            json={"codebase_name": codebase_name},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json().get('result', '')
            
            # Check if we can see metadata info in the stats
            if 'Enhanced Metadata' in result:
                print(f"✅ Stats available for {codebase_name}")
                return True
            else:
                print(f"⚠️ Limited stats for {codebase_name}")
                return False
        else:
            print(f"❌ Error getting stats: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def analyze_enhancement_requirements():
    """Analyze what's needed for v3.2_enhanced"""
    print("🎯 v3.2_enhanced Requirements Analysis")
    print("=" * 50)
    
    print("📋 Current Requirements:")
    print("Option 1: enhanced = True flag in metadata")
    print("Option 2: 3+ legacy enhanced fields:")
    print("   • complexity_metrics")
    print("   • quality_indicators") 
    print("   • code_patterns")
    print("   • api_surface")
    print("   • dependencies")
    
    # Test a few codebases
    test_codebases = ["utils", "z80emu", "go-example-master"]
    
    print(f"\n🔍 Testing sample codebases:")
    for codebase in test_codebases:
        check_metadata_fields(codebase)

def check_current_processing():
    """Check if current processing adds the enhanced flag"""
    print(f"\n🔧 Checking Current Processing Pipeline")
    print("-" * 30)
    
    # Check if the system is using the new framework
    try:
        response = requests.get("http://************:5002/health", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Look for framework indicators
            framework_version = data.get('framework_version', 'unknown')
            processing_pipeline = data.get('processing_pipeline', 'unknown')
            
            print(f"Framework version: {framework_version}")
            print(f"Processing pipeline: {processing_pipeline}")
            
            # Check if enhanced processing is default
            enhanced_by_default = data.get('enhanced_processing_default', False)
            print(f"Enhanced processing default: {enhanced_by_default}")
            
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check exception: {e}")
        return False

def suggest_upgrade_path():
    """Suggest how to get to v3.2_enhanced"""
    print(f"\n🚀 Upgrade Path to v3.2_enhanced")
    print("=" * 50)
    
    print("💡 Easiest approach:")
    print("1. Ensure new framework processing adds 'enhanced: True' flag")
    print("2. Re-process one codebase as a test")
    print("3. Check if it gets v3.2_enhanced status")
    
    print(f"\n🔧 Alternative approach:")
    print("1. Add legacy enhanced fields during processing")
    print("2. Include complexity_metrics, quality_indicators, code_patterns")
    print("3. This requires updating the processing pipeline")
    
    print(f"\n⚡ Quick fix approach:")
    print("1. Update detection logic to be more lenient")
    print("2. Consider v3.1_enhanced as 'good enough'")
    print("3. Focus on functionality over version numbers")

def main():
    print("🎯 v3.2_enhanced Status Analysis")
    print("=" * 50)
    
    # Analyze requirements
    analyze_enhancement_requirements()
    
    # Check current processing
    check_current_processing()
    
    # Suggest upgrade path
    suggest_upgrade_path()
    
    print(f"\n🤔 Key Question:")
    print("Do you need v3.2_enhanced specifically, or is v3.1_enhanced sufficient?")
    print("Both indicate full enhanced functionality - the version is just a label.")

if __name__ == "__main__":
    main()
