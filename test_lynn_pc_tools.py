#!/usr/bin/env python3
"""
Test if LYNN-PC model can see and use tools at all
"""

import requests
import json

def test_tool_awareness():
    """Test if LYNN-PC model knows about available tools"""
    print("🔍 Testing LYNN-PC Tool Awareness")
    print("=" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test queries to check tool awareness
    test_queries = [
        {
            "query": "What tools do you have available?",
            "description": "General tool awareness"
        },
        {
            "query": "Can you use the code_analyzer_tool?",
            "description": "Specific tool awareness"
        },
        {
            "query": "Please call the list_codebases function",
            "description": "Direct function call request"
        }
    ]
    
    for test in test_queries:
        print(f"\n🧪 Test: {test['description']}")
        print(f"   Query: '{test['query']}'")
        
        payload = {
            "model": "LYNN-PC.llama3:latest",
            "messages": [{"role": "user", "content": test["query"]}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                message = data.get("choices", [{}])[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                print(f"   ✅ Response received")
                print(f"   Tool calls: {len(tool_calls)}")
                
                # Check for tool-related keywords
                tool_keywords = ["tool", "function", "code_analyzer", "available", "call"]
                found_keywords = [kw for kw in tool_keywords if kw.lower() in content.lower()]
                
                print(f"   Tool keywords found: {found_keywords}")
                print(f"   Preview: {content[:100]}...")
                
                if tool_calls > 0:
                    print(f"   🎉 TOOL CALLED!")
                    for i, tc in enumerate(tool_calls):
                        func_name = tc.get("function", {}).get("name", "unknown")
                        print(f"      Tool {i+1}: {func_name}")
                elif "code_analyzer" in content.lower():
                    print(f"   ✅ Model knows about code_analyzer_tool")
                elif found_keywords:
                    print(f"   ⚠️ Model mentions tools but doesn't call them")
                else:
                    print(f"   ❌ No tool awareness")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def compare_with_local_model():
    """Compare LYNN-PC with local model tool behavior"""
    print(f"\n🔄 Comparing with Local Model")
    print("=" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    models = [
        ("llama3:latest", "Local model (should work)"),
        ("LYNN-PC.llama3:latest", "LYNN-PC model (debugging)")
    ]
    
    query = "What tools do you have available?"
    
    for model, description in models:
        print(f"\n🧪 {description}")
        print(f"   Model: {model}")
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": query}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                message = data.get("choices", [{}])[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                print(f"   Tool calls: {len(tool_calls)}")
                
                # Check for specific responses
                if "code_analyzer" in content.lower():
                    print(f"   ✅ Mentions code_analyzer_tool")
                if "list" in content.lower() and "codebase" in content.lower():
                    print(f"   ✅ Mentions listing codebases")
                if tool_calls > 0:
                    print(f"   🎉 Actually calls tools")
                
                print(f"   Preview: {content[:150]}...")
                
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def main():
    print("🚀 LYNN-PC Tool Debugging")
    print("=" * 50)
    
    # Test tool awareness
    test_tool_awareness()
    
    # Compare with local model
    compare_with_local_model()
    
    print(f"\n💡 ANALYSIS")
    print("=" * 50)
    print("If LYNN-PC model:")
    print("✅ Mentions tools but doesn't call them → Tool awareness but execution issue")
    print("❌ Doesn't mention tools at all → Tool not properly enabled")
    print("🎉 Actually calls tools → Tool working (unexpected!)")
    
    print(f"\nIf local model works but LYNN-PC doesn't:")
    print("🎯 Issue is specific to remote Ollama model tool execution")

if __name__ == "__main__":
    main()
