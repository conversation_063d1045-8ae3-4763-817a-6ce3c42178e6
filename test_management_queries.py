#!/usr/bin/env python3
"""
Quick test for management operation queries
"""
import asyncio
import aiohttp
import json

async def test_management_queries():
    queries = [
        'delete codebase test_project',
        'switch to bookstore codebase', 
        'remove codebase utils',
        'create new codebase',
        'update codebase index',
        'list all codebases',
        'show available codebases',
        'get codebase statistics',
        'process codebase modbus',
        'reindex codebase utils'
    ]
    
    print("🔍 Testing Management Operation Queries")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        for query in queries:
            try:
                async with session.post(
                    'http://home-ai-server.local:5002/detect_intent',
                    json={'query': query}
                ) as response:
                    result = await response.json()
                    intent = result.get('intent', 'unknown')
                    confidence = result.get('confidence', 0)
                    action = result.get('suggested_action', 'unknown')
                    
                    # Highlight potential issues
                    status = "✅" if intent == "management" else "❌" if intent == "code_analysis" else "⚠️"
                    
                    print(f'{status} Query: "{query}"')
                    print(f'   Intent: {intent} (confidence: {confidence:.2f})')
                    print(f'   Action: {action}')
                    print()
            except Exception as e:
                print(f'❌ Error testing "{query}": {e}')
                print()

if __name__ == "__main__":
    asyncio.run(test_management_queries())
