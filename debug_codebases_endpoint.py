#!/usr/bin/env python3
"""
Debug the codebases endpoint to see why it's not working in tool execution
"""

import requests
import json

def test_codebases_endpoint():
    """Test the codebases endpoint directly"""
    print("🔍 Testing Codebases Endpoint")
    print("=" * 50)
    
    base_url = "http://192.168.0.77:5002"
    
    try:
        response = requests.get(f"{base_url}/codebases", timeout=30)
        
        print(f"Status code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            print(f"Response length: {len(data) if isinstance(data, list) else 'Not a list'}")
            
            if isinstance(data, list):
                print(f"First few items:")
                for i, item in enumerate(data[:3]):
                    print(f"  Item {i}: {item}")
                    
                # Extract codebase names
                codebases = []
                for item in data:
                    if isinstance(item, dict) and 'name' in item:
                        codebases.append(item['name'])
                    elif isinstance(item, str):
                        codebases.append(item)
                
                print(f"Extracted codebases: {codebases}")
                return codebases
            else:
                print(f"Unexpected response format: {data}")
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")
    
    return []

def test_fixed_tool_execution():
    """Test the fixed tool execution"""
    print("\n🔧 Testing Fixed Tool Execution")
    print("=" * 50)
    
    from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Test the execute_tool method directly
    print("Testing execute_tool('list_codebases', {}):")
    result = analyzer.execute_tool('list_codebases', {})
    print(f"Result: {result}")
    
    return result

def main():
    print("🔍 Debugging Codebases Endpoint Issue")
    print("=" * 60)
    
    # Test direct endpoint
    direct_codebases = test_codebases_endpoint()
    
    # Test tool execution
    tool_result = test_fixed_tool_execution()
    
    print(f"\n🎯 COMPARISON")
    print("=" * 60)
    print(f"Direct endpoint: {len(direct_codebases)} codebases")
    print(f"Tool execution: {tool_result}")
    
    if direct_codebases and not tool_result.get('codebases'):
        print(f"❌ Tool execution is not extracting codebases correctly")
        print(f"💡 Need to fix the data extraction logic")
    elif direct_codebases and tool_result.get('codebases'):
        print(f"✅ Tool execution is working correctly")
    else:
        print(f"⚠️ Both methods have issues")

if __name__ == "__main__":
    main()
