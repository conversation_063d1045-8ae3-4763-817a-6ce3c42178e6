#!/usr/bin/env python3
"""
Debug the exact tool flow that T5810 is experiencing
"""

import requests
import json

def debug_t5810_tool_flow():
    """Debug the exact tool flow step by step"""
    print("🔍 Debugging T5810 Tool Flow")
    print("=" * 60)
    
    t5810_ollama = "http://************:11434"
    
    # Create the exact tool
    tools = [{
        'type': 'function',
        'function': {
            'name': 'query_codebase',
            'description': 'Search for specific code patterns or functions in a codebase',
            'parameters': {
                'type': 'object',
                'properties': {
                    'codebase_name': {
                        'type': 'string',
                        'description': 'Name of the codebase to search in',
                    },
                    'query': {
                        'type': 'string',
                        'description': 'What to search for (functions, patterns, etc.)',
                    },
                    'max_results': {
                        'type': 'integer',
                        'description': 'Maximum number of results to return (default: 10)',
                        'default': 10
                    }
                },
                'required': ['codebase_name', 'query'],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Find memory management functions in the utils codebase using the query_codebase tool.'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print("📤 Step 1: Sending initial request...")
    
    try:
        # Step 1: Initial request
        response = requests.post(f"{t5810_ollama}/api/chat", json=payload, timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ Initial response received")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Initial content: {content}")
            
            if not tool_calls:
                print(f"❌ No tool calls - T5810 didn't call the tool")
                return
            
            # Step 2: Execute the tool manually (simulate what DirectOllamaCodeAnalyzer does)
            messages.append(message)  # Add assistant's response
            
            for tool_call in tool_calls:
                function = tool_call.get('function', {})
                tool_name = function.get('name', '')
                arguments_str = function.get('arguments', '{}')
                
                print(f"\n📤 Step 2: Tool call details:")
                print(f"   Tool name: {tool_name}")
                print(f"   Arguments string: {arguments_str}")
                
                # Parse arguments
                try:
                    arguments = json.loads(arguments_str)
                    print(f"   Parsed arguments: {arguments}")
                except Exception as e:
                    print(f"   ❌ Failed to parse arguments: {e}")
                    arguments = {}
                
                # Execute the tool (simulate DirectOllamaCodeAnalyzer.execute_tool)
                print(f"\n🔧 Executing tool manually...")
                
                if tool_name == 'query_codebase':
                    try:
                        tool_response = requests.post(
                            "http://192.168.0.77:5002/search",
                            json={
                                "query": arguments.get('query', ''),
                                "codebase_name": arguments.get('codebase_name', ''),
                                "n_results": arguments.get('max_results', 10)
                            },
                            timeout=60
                        )
                        
                        if tool_response.status_code == 200:
                            tool_result = tool_response.json()
                            print(f"   ✅ Tool executed successfully")
                            print(f"   📊 Tool result keys: {tool_result.keys()}")
                            
                            if 'results' in tool_result:
                                results = tool_result['results']
                                print(f"   📝 Found {len(results)} results")
                                
                                if results:
                                    first_content = results[0].get('content', '')[:200]
                                    print(f"   📄 First result preview: {first_content}...")
                        else:
                            tool_result = {"error": f"HTTP {tool_response.status_code}"}
                            print(f"   ❌ Tool execution failed: {tool_response.status_code}")
                            
                    except Exception as e:
                        tool_result = {"error": str(e)}
                        print(f"   ❌ Tool execution exception: {e}")
                else:
                    tool_result = {"error": f"Unknown tool: {tool_name}"}
                
                # Add tool result to messages (this is what T5810 will see)
                tool_result_json = json.dumps(tool_result)
                print(f"\n📥 Tool result JSON length: {len(tool_result_json)}")
                print(f"📥 Tool result preview: {tool_result_json[:300]}...")
                
                messages.append({
                    'role': 'tool',
                    'content': tool_result_json
                })
            
            # Step 3: Get final response from T5810
            print(f"\n📤 Step 3: Getting final response from T5810...")
            
            final_payload = {
                'model': 'llama3.1:latest',
                'messages': messages,
                'stream': False
            }
            
            final_response = requests.post(f"{t5810_ollama}/api/chat", json=final_payload, timeout=120)
            
            if final_response.status_code == 200:
                final_data = final_response.json()
                final_content = final_data.get('message', {}).get('content', '')
                
                print(f"✅ Final response received")
                print(f"📝 Final content length: {len(final_content)}")
                print(f"📊 Final content:")
                print(final_content)
                
                # Check if final response uses the tool results
                if any(term in final_content.lower() for term in ['tmwmem', 'triangle microworks', 'copyright']):
                    print(f"\n✅ SUCCESS: T5810 used the real tool results!")
                else:
                    print(f"\n❌ FAILURE: T5810 ignored the tool results")
                    
                    # Check if it mentions the error about missing codebase_name
                    if 'codebase_name' in final_content and 'missing' in final_content:
                        print(f"🔍 T5810 is complaining about missing codebase_name")
                        print(f"   But we know the tool was called correctly!")
                        print(f"   This suggests T5810 is not processing tool results properly")
            else:
                print(f"❌ Final response error: {final_response.status_code}")
                
        else:
            print(f"❌ Initial response error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    print("🔍 Debugging T5810 Tool Flow Issue")
    print("=" * 70)
    print("Goal: Understand why T5810 says 'codebase_name missing' when tool works")
    
    debug_t5810_tool_flow()

if __name__ == "__main__":
    main()
