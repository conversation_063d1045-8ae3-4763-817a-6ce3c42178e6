#!/usr/bin/env python3
"""
Simple test of your theory: explicit remote context instructions
"""

import requests

def test_context_instruction():
    """Test if explicit context helps LYNN-PC model"""
    print("🧪 Testing Context Instruction Theory")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Your theory: explicit remote context instruction (without revealing answers)
    context_prompt = """SYSTEM CONTEXT:
You are running on lynn-pc (192.168.0.32) but you have access to a code_analyzer_tool that connects to home-ai-server (192.168.0.77:5002).

When you use the code_analyzer_tool, it will automatically handle the network connection to the remote server. You don't need to worry about the network details - just call the tool function.

The code analyzer server is confirmed working and contains real codebases from the development environment.

Please use the code_analyzer_tool now to list the available codebases."""
    
    payload = {
        "model": "LYNN-PC.llama3:latest",
        "messages": [{"role": "user", "content": context_prompt}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print("📤 Sending request with explicit context...")
    print(f"Model: LYNN-PC.llama3:latest")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        
        print(f"📥 Response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"Tool calls: {len(tool_calls)}")
            print(f"Content length: {len(content)} chars")
            
            # Show first 300 chars
            print(f"\nResponse preview:")
            print("-" * 30)
            print(content[:300] + ("..." if len(content) > 300 else ""))
            print("-" * 30)
            
            # Check for success indicators
            real_codebases = ["utils", "z80emu", "library-management-system", "test_project"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            if len(tool_calls) > 0:
                print(f"\n🎉 SUCCESS: Tool was called! ({len(tool_calls)} calls)")
                for i, tc in enumerate(tool_calls):
                    func_name = tc.get("function", {}).get("name", "unknown")
                    print(f"   Tool {i+1}: {func_name}")
                return True
            elif found_real:
                print(f"\n✅ SUCCESS: Found real codebases: {found_real}")
                print("Tool appears to be working even without explicit tool_calls")
                return True
            else:
                print(f"\n❌ FAILED: No tool calls and no real codebase data")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        print("💡 This might indicate the model is trying to process but hanging")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Testing Your Context Instruction Theory")
    print("=" * 50)
    print("Theory: Explicit remote context instructions might help LYNN-PC model use tools")
    
    success = test_context_instruction()
    
    print(f"\n🎯 RESULT")
    print("=" * 50)
    if success:
        print("🎉 YOUR THEORY WAS CORRECT!")
        print("✅ Explicit context instructions helped the remote model use tools")
        print("💡 The issue was context awareness, not architecture")
        print("\n📋 Next steps:")
        print("1. Update tool prompts to include remote context")
        print("2. Test with other remote models")
        print("3. Document this solution")
    else:
        print("❌ Context instructions didn't solve the issue")
        print("💡 The problem appears to be architectural")
        print("\n🔍 This suggests:")
        print("1. Tool execution context is the real issue")
        print("2. Remote Ollama models may have execution limitations")
        print("3. OpenWebUI tools may not support remote model contexts")

if __name__ == "__main__":
    main()
