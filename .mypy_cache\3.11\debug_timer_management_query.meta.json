{"data_mtime": 1752886914, "dep_lines": [6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30], "dependencies": ["direct_ollama_code_analyzer", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "e7fe9841edfa9c7d35bfd8b19b738d67669c66e6", "id": "debug_timer_management_query", "ignore_all": false, "interface_hash": "7076453d2706a03fedbaf15e80b63f7867e853a6", "mtime": 1752886912, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\debug_timer_management_query.py", "plugin_data": null, "size": 6032, "suppressed": [], "version_id": "1.15.0"}