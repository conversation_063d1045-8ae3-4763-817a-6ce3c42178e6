#!/usr/bin/env python3
"""
Fix T5810 prompt to force it to use tool results
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_fixed_prompt():
    """Test with a prompt that forces T5810 to use tool results"""
    print("🔧 Testing Fixed Prompt")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Extremely explicit prompt
    query = """TASK: Find memory management functions in the utils codebase.

STEP 1: Call the query_codebase tool with:
- codebase_name: "utils"
- query: "malloc free alloc"
- max_results: 3

STEP 2: After you receive the tool results, extract and show me:
- The file path from the first result
- The first 10 lines of actual code from the first result
- Any function names you see that contain "malloc", "free", or "alloc"

STEP 3: Do NOT generate any example code. Only show what the tool actually returned.

Execute this now and show me the real results."""
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result length: {len(result)}")
    print(f"Result:")
    print(result)
    
    # Check for real indicators
    real_indicators = [
        'tmwmem',
        'Triangle MicroWorks', 
        '/app/source_code/utils',
        'tmwmem.h',
        'TMWMEM_ALLOC',
        'TMWMEM_FREE'
    ]
    
    found_indicators = [ind for ind in real_indicators if ind in result]
    
    print(f"\nReal code indicators found: {found_indicators}")
    
    if len(found_indicators) >= 2:
        print(f"✅ SUCCESS: T5810 is now using real tool results!")
        return True
    else:
        print(f"❌ Still not using tool results properly")
        return False

def test_system_prompt_approach():
    """Test with a system-like prompt approach"""
    print(f"\n🎯 Testing System Prompt Approach")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # System-style prompt
    query = """You are a code analysis assistant. When you use tools, you MUST show the actual results from the tools, not make up examples.

User request: Show me memory management functions from the utils codebase.

Your response should:
1. Use the query_codebase tool to search utils codebase for "malloc free alloc"
2. Show the actual file path and code content from the tool results
3. Never generate fictional code examples

Begin now."""
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result length: {len(result)}")
    print(f"Result preview: {result[:500]}...")
    
    # Check for real content
    if any(term in result for term in ['tmwmem', '/app/source_code', 'Triangle MicroWorks']):
        print(f"✅ SUCCESS: Shows real code content")
        return True
    else:
        print(f"❌ Still generating examples")
        return False

def test_direct_instruction():
    """Test with very direct instruction"""
    print(f"\n⚡ Testing Direct Instruction")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Very direct
    query = """Search utils codebase for malloc functions. After you get the tool results, copy and paste the first 5 lines of code from the first result. Do not write any example code."""
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result: {result}")
    
    # Check for copyright header (which should be in the real code)
    if 'Triangle MicroWorks' in result or 'Copyright' in result:
        print(f"✅ SUCCESS: Shows real code header")
        return True
    else:
        print(f"❌ Not showing real code")
        return False

def main():
    print("🔧 Fixing T5810 Prompt to Use Tool Results")
    print("=" * 60)
    
    # Test different prompt approaches
    fixed_works = test_fixed_prompt()
    system_works = test_system_prompt_approach()
    direct_works = test_direct_instruction()
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Fixed prompt:      {'✅ Working' if fixed_works else '❌ Not working'}")
    print(f"System prompt:     {'✅ Working' if system_works else '❌ Not working'}")
    print(f"Direct instruction: {'✅ Working' if direct_works else '❌ Not working'}")
    
    if any([fixed_works, system_works, direct_works]):
        print(f"\n🎉 SUCCESS: Found a prompt that works!")
        print(f"T5810 can be made to use tool results with proper prompting")
    else:
        print(f"\n❌ ISSUE: T5810 is consistently ignoring tool results")
        print(f"This may be a model behavior issue with llama3.1:latest")

if __name__ == "__main__":
    main()
