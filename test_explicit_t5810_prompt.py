#!/usr/bin/env python3
"""
Test T5810 with explicit prompts to ensure it uses tool results
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
import time

def test_explicit_prompts():
    """Test T5810 with very explicit prompts"""
    print("🧪 Testing T5810 with Explicit Prompts")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    test_cases = [
        {
            "query": "Use the list_codebases tool to get all available codebases, then list them in your response. Make sure to actually call the tool and show the real results.",
            "expected": ["utils", "z80emu", "modbus"]
        },
        {
            "query": "I need you to search for memory management functions in the utils codebase. Use the query_codebase tool with codebase_name='utils' and query='malloc free alloc memory'. Then show me the actual code you found.",
            "expected": ["malloc", "free", "alloc", "void", "int"]
        },
        {
            "query": "Call the list_codebases tool first to see what codebases are available, then tell me which ones you found. Do not make up any codebase names - only use the real results from the tool.",
            "expected": ["utils", "z80emu", "modbus"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{len(test_cases)}] Testing explicit prompt:")
        print(f"Query: {test_case['query'][:100]}...")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = analyzer.chat_with_tools(test_case['query'])
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"📝 Result length: {len(result)} characters")
            
            # Check if expected terms are in the result
            found_terms = []
            for term in test_case['expected']:
                if term.lower() in result.lower():
                    found_terms.append(term)
            
            print(f"🔍 Expected terms found: {found_terms}")
            print(f"📊 Result preview:")
            print(result[:400] + "..." if len(result) > 400 else result)
            
            if len(found_terms) >= 2:  # At least 2 expected terms found
                print(f"✅ SUCCESS: Found expected content!")
            else:
                print(f"❌ FAILURE: Expected content not found")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_direct_comparison():
    """Compare T5810 vs lynn-pc on the same query"""
    print("\n🔄 Comparing T5810 vs lynn-pc")
    print("=" * 60)
    
    query = "Use the list_codebases tool to show me all available codebases. List each codebase name you find."
    
    servers = [
        ("T5810 (RTX 3090)", "http://192.168.0.11:11434"),
        ("lynn-pc (RTX 3050)", "http://192.168.0.32:11434")
    ]
    
    for server_name, server_url in servers:
        print(f"\n🖥️  Testing {server_name}:")
        print("-" * 30)
        
        analyzer = DirectOllamaCodeAnalyzer(server_url)
        
        try:
            start_time = time.time()
            result = analyzer.chat_with_tools(query)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            
            # Check for real codebase names
            real_codebases = ["utils", "z80emu", "modbus", "go-example-master"]
            found_codebases = [cb for cb in real_codebases if cb.lower() in result.lower()]
            
            print(f"🎯 Real codebases found: {found_codebases}")
            print(f"📝 Response preview: {result[:300]}...")
            
            if found_codebases:
                print(f"✅ SUCCESS: Found real codebase data")
            else:
                print(f"❌ FAILURE: No real codebase data found")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🔍 Testing T5810 with Explicit Prompts")
    print("=" * 70)
    print("Goal: Make T5810 use tool results properly with explicit instructions")
    
    # Test explicit prompts
    test_explicit_prompts()
    
    # Compare servers
    test_direct_comparison()
    
    print(f"\n🎯 CONCLUSION")
    print("=" * 70)
    print("If T5810 still doesn't use tool results with explicit prompts,")
    print("the issue is likely in the model's instruction following,")
    print("not in the tool integration code.")

if __name__ == "__main__":
    main()
