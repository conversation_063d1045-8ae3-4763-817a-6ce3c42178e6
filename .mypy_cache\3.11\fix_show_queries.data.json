{".class": "MypyFile", "_fullname": "fix_show_queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DirectOllamaCodeAnalyzer": {".class": "SymbolTableNode", "cross_ref": "direct_ollama_code_analyzer.DirectOllamaCodeAnalyzer", "kind": "Gdef"}, "FixedDirectOllamaCodeAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["direct_ollama_code_analyzer.DirectOllamaCodeAnalyzer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fix_show_queries.FixedDirectOllamaCodeAnalyzer", "name": "FixedDirectOllamaCodeAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fix_show_queries.FixedDirectOllamaCodeAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fix_show_queries", "mro": ["fix_show_queries.FixedDirectOllamaCodeAnalyzer", "direct_ollama_code_analyzer.DirectOllamaCodeAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "chat_with_tools": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "user_message", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.FixedDirectOllamaCodeAnalyzer.chat_with_tools", "name": "chat_with_tools", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fix_show_queries.FixedDirectOllamaCodeAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fix_show_queries.FixedDirectOllamaCodeAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fix_show_queries.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.main", "name": "main", "type": null}}, "preprocess_query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.preprocess_query", "name": "preprocess_query", "type": null}}, "test_enhanced_analyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.test_enhanced_analyzer", "name": "test_enhanced_analyzer", "type": null}}, "test_multiple_show_queries": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.test_multiple_show_queries", "name": "test_multiple_show_queries", "type": null}}, "test_show_query_fix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fix_show_queries.test_show_query_fix", "name": "test_show_query_fix", "type": null}}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\fix_show_queries.py"}