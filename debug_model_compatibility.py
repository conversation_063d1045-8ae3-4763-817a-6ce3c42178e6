#!/usr/bin/env python3
"""
Debug Model Compatibility Issues
Comprehensive testing to identify why the Code Analyzer tool fails with different models
"""

import requests
import json
import time
from typing import Dict, Any, List

class ModelCompatibilityTester:
    def __init__(self):
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        self.tool_id = "code_analyzer_tool"
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_available_models(self) -> List[str]:
        """Get list of available models from OpenWebUI"""
        try:
            response = requests.get(f"{self.base_url}/api/models", headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get("data", []):
                    model_id = model.get("id", "")
                    if model_id and not model_id.startswith("openai/"):
                        models.append(model_id)
                return models
            else:
                print(f"❌ Failed to get models: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error getting models: {e}")
            return []
    
    def check_tool_enabled_for_model(self, model: str) -> bool:
        """Check if the tool is enabled for a specific model"""
        try:
            # This is a heuristic - we'll try a simple tool call and see if it works
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": "list codebases"}],
                "tool_ids": [self.tool_id],
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result.get("choices", [{}])[0].get("message", {})
                
                # Check if tool was called
                if "tool_calls" in message:
                    return True
                
                # Check if content suggests tool usage
                content = message.get("content", "").lower()
                if any(indicator in content for indicator in [
                    "available codebases", "codebase", "code analyzer", "📚"
                ]):
                    return True
                    
            return False
            
        except Exception as e:
            print(f"   Error checking tool for {model}: {e}")
            return False
    
    def test_model_response(self, model: str, query: str) -> Dict[str, Any]:
        """Test a specific model with a query"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": query}],
            "tool_ids": [self.tool_id],
            "stream": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            elapsed = time.time() - start_time
            
            result = {
                "model": model,
                "query": query,
                "status_code": response.status_code,
                "response_time": elapsed,
                "success": False,
                "tool_called": False,
                "content": "",
                "error": None
            }
            
            if response.status_code == 200:
                data = response.json()
                message = data.get("choices", [{}])[0].get("message", {})
                
                result["success"] = True
                result["content"] = message.get("content", "")
                result["tool_called"] = "tool_calls" in message
                
                # Additional analysis
                if result["tool_called"]:
                    tool_calls = message.get("tool_calls", [])
                    result["tool_calls_count"] = len(tool_calls)
                    result["tool_functions"] = [tc.get("function", {}).get("name") for tc in tool_calls]
                
            else:
                result["error"] = response.text[:200]
                
            return result
            
        except Exception as e:
            return {
                "model": model,
                "query": query,
                "success": False,
                "error": str(e),
                "tool_called": False
            }
    
    def run_comprehensive_test(self):
        """Run comprehensive compatibility tests"""
        print("🔍 Model Compatibility Testing")
        print("=" * 60)

        # Test only these specific models
        models = [
            "llama3:latest",
            "T5810 - .llama3:latest",
            "smollm2:1.7b"
        ]

        print(f"🎯 Testing {len(models)} specific models:")
        for model in models:
            print(f"   • {model}")
        
        # Test queries
        test_queries = [
            "list codebases",
            "select codebase library-management-system",
            "search for memory functions"
        ]
        
        results = {}
        
        for model in models:
            print(f"\n🧪 Testing model: {model}")
            print("-" * 40)
            
            # Check if tool is enabled
            tool_enabled = self.check_tool_enabled_for_model(model)
            print(f"   Tool enabled: {'✅' if tool_enabled else '❌'}")
            
            model_results = []
            
            for query in test_queries:
                print(f"   Testing: '{query}'")
                result = self.test_model_response(model, query)
                model_results.append(result)
                
                status = "✅" if result["success"] and result["tool_called"] else "❌"
                print(f"      {status} Status: {result['status_code']}, Tool called: {result['tool_called']}")
                
                if result.get("error"):
                    print(f"      Error: {result['error']}")
            
            results[model] = {
                "tool_enabled": tool_enabled,
                "test_results": model_results
            }
        
        # Summary
        print("\n📊 SUMMARY")
        print("=" * 60)
        
        working_models = []
        broken_models = []
        
        for model, data in results.items():
            tool_calls_working = any(r["tool_called"] for r in data["test_results"])
            
            if tool_calls_working:
                working_models.append(model)
                print(f"✅ {model} - Working")
            else:
                broken_models.append(model)
                print(f"❌ {model} - Not working")
                
                # Analyze why it's not working
                if not data["tool_enabled"]:
                    print(f"   → Tool not enabled for this model")
                else:
                    print(f"   → Tool enabled but not being called")
        
        print(f"\n📈 Results: {len(working_models)}/{len(models)} models working")
        
        if broken_models:
            print(f"\n🔧 Troubleshooting for broken models:")
            print("1. Check if tool is enabled in OpenWebUI settings")
            print("2. Verify model supports function calling")
            print("3. Check model context length requirements")
            print("4. Test with simpler queries first")
        
        return results

def main():
    tester = ModelCompatibilityTester()
    results = tester.run_comprehensive_test()
    
    # Save results to file
    with open("model_compatibility_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: model_compatibility_results.json")

if __name__ == "__main__":
    main()
