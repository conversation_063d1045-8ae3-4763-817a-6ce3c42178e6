# Code Analyzer Docker Logging Setup

## 🎯 **Overview**

The Docker container now has proper logging infrastructure with dedicated directories and comprehensive log management for both the Code Analyzer Server and Web Management Interface.

## 📁 **Log Directory Structure**

### **Container Log Directories**
```
/var/log/supervisor/
├── supervisord.log                    # Supervisor daemon logs
├── code-analyzer-server.out.log       # Code analyzer stdout
├── code-analyzer-server.err.log       # Code analyzer stderr  
├── web-management-server.out.log      # Web management stdout
└── web-management-server.err.log      # Web management stderr
```

### **Docker Volume Mapping**
```yaml
volumes:
  - supervisor_logs:/var/log/supervisor # Persist supervisor logs
```

## 🔧 **Dockerfile Changes**

### **Directory Creation**
```dockerfile
# Create necessary directories
RUN mkdir -p templates && \
    mkdir -p /var/log/supervisor && \
    mkdir -p /var/run && \
    chmod 755 /var/log/supervisor
```

### **Benefits**
- ✅ **Proper permissions** for log directories
- ✅ **Persistent logging** across container restarts
- ✅ **Separate stdout/stderr** streams for each service
- ✅ **Supervisor daemon logging** for process management

## 📊 **Log Management Commands**

### **Basic Log Viewing**
```bash
# List all log files
docker exec code-analyzer-server ls -la /var/log/supervisor/

# View recent logs (last 20 lines)
docker exec code-analyzer-server tail -20 /var/log/supervisor/code-analyzer-server.out.log
docker exec code-analyzer-server tail -20 /var/log/supervisor/web-management-server.out.log

# View error logs
docker exec code-analyzer-server tail -20 /var/log/supervisor/code-analyzer-server.err.log
docker exec code-analyzer-server tail -20 /var/log/supervisor/web-management-server.err.log
```

### **Real-time Log Following**
```bash
# Follow Code Analyzer logs
docker exec code-analyzer-server tail -f /var/log/supervisor/code-analyzer-server.out.log

# Follow Web Management logs  
docker exec code-analyzer-server tail -f /var/log/supervisor/web-management-server.out.log

# Follow all logs simultaneously
docker exec code-analyzer-server tail -f /var/log/supervisor/*.log
```

### **Supervisor Management**
```bash
# Check service status
docker exec code-analyzer-server supervisorctl status

# Restart specific service
docker exec code-analyzer-server supervisorctl restart code-analyzer-server
docker exec code-analyzer-server supervisorctl restart web-management-server

# View supervisor logs
docker exec code-analyzer-server tail -f /var/log/supervisor/supervisord.log
```

## 🛠️ **Log Monitoring Script**

### **Interactive Log Monitor (`monitor_logs.sh`)**
```bash
# Make executable
chmod +x monitor_logs.sh

# Run interactive monitor
./monitor_logs.sh

# Quick status check
./monitor_logs.sh --status

# Follow specific service logs
./monitor_logs.sh --follow-analyzer
./monitor_logs.sh --follow-web
./monitor_logs.sh --follow-all

# Show recent logs
./monitor_logs.sh --recent
```

### **Script Features**
- ✅ **Interactive menu** for easy log navigation
- ✅ **Real-time log following** with Ctrl+C to stop
- ✅ **Service status checking** with health endpoints
- ✅ **Supervisor status monitoring**
- ✅ **Quick status overview** of both services
- ✅ **Command-line options** for automation

## 🔍 **Troubleshooting with Logs**

### **Common Issues and Log Locations**

#### **1. Container Won't Start**
```bash
# Check Docker container logs
docker logs code-analyzer-server

# Check supervisor startup
docker exec code-analyzer-server cat /var/log/supervisor/supervisord.log
```

#### **2. Code Analyzer Server Issues**
```bash
# Check application logs
docker exec code-analyzer-server tail -50 /var/log/supervisor/code-analyzer-server.out.log

# Check for errors
docker exec code-analyzer-server tail -50 /var/log/supervisor/code-analyzer-server.err.log
```

#### **3. Web Management Interface Issues**
```bash
# Check web server logs
docker exec code-analyzer-server tail -50 /var/log/supervisor/web-management-server.out.log

# Check for web errors
docker exec code-analyzer-server tail -50 /var/log/supervisor/web-management-server.err.log
```

#### **4. Process Management Issues**
```bash
# Check supervisor status
docker exec code-analyzer-server supervisorctl status

# Check supervisor logs
docker exec code-analyzer-server tail -50 /var/log/supervisor/supervisord.log
```

## 📈 **Log Rotation and Maintenance**

### **Supervisor Log Rotation**
Supervisor automatically handles log rotation for service logs. Configuration in `supervisord.conf`:

```ini
[program:code-analyzer-server]
stdout_logfile=/var/log/supervisor/code-analyzer-server.out.log
stderr_logfile=/var/log/supervisor/code-analyzer-server.err.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_maxbytes=50MB  
stderr_logfile_backups=10
```

### **Manual Log Cleanup**
```bash
# Clear specific log file
docker exec code-analyzer-server truncate -s 0 /var/log/supervisor/code-analyzer-server.out.log

# Clear all logs
docker exec code-analyzer-server find /var/log/supervisor -name "*.log" -exec truncate -s 0 {} \;
```

## 🎯 **Benefits of Proper Logging**

### **✅ Advantages**
1. **Persistent Logs**: Logs survive container restarts
2. **Separate Streams**: stdout/stderr separated for each service
3. **Easy Debugging**: Clear log locations for troubleshooting
4. **Process Monitoring**: Supervisor logs for process management
5. **Real-time Monitoring**: Live log following capabilities
6. **Automated Rotation**: Prevents disk space issues

### **📊 Monitoring Capabilities**
- **Service Health**: Monitor both services independently
- **Error Detection**: Separate error logs for quick issue identification
- **Performance Tracking**: Application logs for performance analysis
- **Process Management**: Supervisor logs for restart/failure tracking

## 🚀 **Production Recommendations**

### **Log Aggregation**
Consider integrating with log aggregation systems:
- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Fluentd** for log forwarding
- **Prometheus** + **Grafana** for metrics
- **Syslog** for centralized logging

### **Monitoring Integration**
```bash
# Export logs to external systems
docker exec code-analyzer-server tail -f /var/log/supervisor/*.log | your-log-processor

# Health check integration
curl http://home-ai-server.local:5002/health
curl http://home-ai-server.local:5003/api/health
```

This comprehensive logging setup provides full visibility into both services running in the Docker container, making debugging and monitoring much more effective! 🎉
