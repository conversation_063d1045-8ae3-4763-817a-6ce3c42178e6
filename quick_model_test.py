#!/usr/bin/env python3
"""
Quick Model Test - Fast diagnosis of tool calling issues
"""

import requests
import json

def test_model(model_name):
    """Test a single model quickly"""
    print(f"\n🧪 Testing: {model_name}")
    print("-" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Simple test query
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            
            # Check for tool calls
            tool_calls = message.get("tool_calls", [])
            content = message.get("content", "")
            
            print(f"   Tool calls: {len(tool_calls)}")
            print(f"   Content length: {len(content)}")
            
            if tool_calls:
                print("   ✅ TOOL WAS CALLED")
                for i, tc in enumerate(tool_calls):
                    func_name = tc.get("function", {}).get("name", "unknown")
                    print(f"      Tool {i+1}: {func_name}")
            else:
                print("   ❌ TOOL NOT CALLED")
                # Check if content suggests the model tried to help without tools
                if any(word in content.lower() for word in ["codebase", "code", "analysis"]):
                    print("   📝 Model responded about code (without tool)")
                else:
                    print("   📝 Model gave generic response")
                
                # Show first 200 chars of response
                print(f"   Response: {content[:200]}...")
        
        elif response.status_code == 400:
            print("   ❌ BAD REQUEST")
            try:
                error = response.json()
                print(f"   Error: {error}")
            except:
                print(f"   Error: {response.text[:200]}")
        
        else:
            print(f"   ❌ HTTP ERROR: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")

def main():
    print("🚀 Quick Model Tool Test")
    print("=" * 50)
    
    models = [
        "llama3:latest",
        "T5810 - .llama3:latest", 
        "smollm2:1.7b"
    ]
    
    for model in models:
        test_model(model)
    
    print("\n📊 ANALYSIS")
    print("=" * 50)
    print("If tool is not being called:")
    print("1. ❌ Tool not enabled for model in OpenWebUI settings")
    print("2. ❌ Model doesn't support function calling")
    print("3. ❌ Tool ID mismatch")
    print("4. ❌ Model context too small for tool definitions")
    print("5. ❌ OpenWebUI configuration issue")

if __name__ == "__main__":
    main()
