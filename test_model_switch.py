#!/usr/bin/env python3
"""
Test switching to a faster model
"""

import requests
import time

def test_model_switch():
    """Test the recommended faster model"""
    print("🚀 Testing Model Switch to deepseek-coder:6.7b")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test the recommended model
    model = "deepseek-coder:6.7b"
    query = "list codebases"
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print(f"📤 Testing: {model}")
    print(f"🔍 Query: {query}")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=90
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            
            # Check for real codebase data
            real_codebases = ["utils", "z80emu", "go-example-master", "TypeScript-Node-Starter-master"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            print(f"✅ Response received in {response_time:.2f}s")
            print(f"📝 Content length: {len(content)} chars")
            print(f"🎯 Real codebases found: {found_real}")
            
            if found_real:
                print(f"🎉 SUCCESS: Model switch working!")
                print(f"💡 Expected speedup: ~46% faster than llama3:latest")
                return True
            else:
                print(f"⚠️ Model responded but may not be using tools properly")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🎯 Model Switch Test")
    print("=" * 50)
    
    success = test_model_switch()
    
    print(f"\n📋 Instructions:")
    print("1. In OpenWebUI, select 'deepseek-coder:6.7b' from model dropdown")
    print("2. Make sure code_analyzer_tool is enabled for this model")
    print("3. Test with your typical code analysis queries")
    
    print(f"\n💡 Expected Benefits:")
    print("• 46% faster responses (44s vs 81s)")
    print("• Same code analysis quality")
    print("• Lower memory usage")
    print("• Better code understanding")

if __name__ == "__main__":
    main()
