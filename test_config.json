{"test_environment": {"server_url": "http://home-ai-server.local:5002", "openwebui_url": "http://home-ai-server.local:8080", "api_key": "sk-320242e0335e45a4b1fa4752f758f9ab", "timeout": 30, "test_codebase": "utils"}, "test_categories": {"smoke_tests": {"description": "Quick tests to verify basic functionality", "timeout": 60, "tests": [{"name": "Server Health Check", "command": ["python", "debug-testing/check_server_status.py"], "critical": true}, {"name": "Quick Status Check", "command": ["python", "debug-testing/quick_status_check.py"], "critical": true}]}, "unit_tests": {"description": "Individual component testing", "timeout": 120, "tests": [{"name": "Server API Tests", "command": ["python", "test_suite.py", "TestCodeAnalyzerServer"], "critical": true}, {"name": "Plugin Unit Tests", "command": ["python", "test_suite.py", "TestOpenWebUIPlugin"], "critical": true}, {"name": "Caching System Tests", "command": ["python", "test_suite.py", "TestIntelligentCaching"], "critical": false}]}, "integration_tests": {"description": "Component interaction testing", "timeout": 180, "tests": [{"name": "End-to-End Integration", "command": ["python", "test_suite.py", "TestIntegration"], "critical": true}, {"name": "API Integration", "command": ["python", "debug-testing/test_code_analyzer_server_apis.py"], "critical": true}, {"name": "Complete Functionality", "command": ["python", "debug-testing/verify_complete_functionality.py"], "critical": false}]}, "performance_tests": {"description": "Performance and benchmark testing", "timeout": 600, "tests": [{"name": "Response Time Benchmarks", "command": ["python", "performance_test_suite.py", "--benchmark"], "critical": false}, {"name": "Cache Performance Tests", "command": ["python", "performance_test_suite.py", "--cache-test"], "critical": false}, {"name": "Collection Performance", "command": ["python", "debug-testing/test_collections.py", "--benchmark"], "critical": false}]}, "regression_tests": {"description": "Regression testing against baseline", "timeout": 300, "tests": [{"name": "Language Support Regression", "command": ["python", "debug-testing/test_all_languages.py"], "critical": true}, {"name": "Enhanced Search Regression", "command": ["python", "debug-testing/test_enhanced_search_fix.py"], "critical": false}, {"name": "Codebase Selection Regression", "command": ["python", "debug-testing/test_codebase_selection.py"], "critical": false}]}}, "performance_thresholds": {"avg_response_time": 3.0, "p95_response_time": 5.0, "cache_hit_improvement": 0.5, "memory_usage_mb": 500, "error_rate": 0.05}, "test_data": {"benchmark_queries": ["memory management functions", "error handling patterns", "network operations", "file I/O operations", "string manipulation", "data structures", "algorithm implementations", "security functions"], "test_codebases": ["auto-detect"], "filter_combinations": [{"filter_type": "function"}, {"filter_type": "class"}, {"filter_language": "c"}, {"filter_language": "cpp"}, {"filter_language": "python"}, {"filter_type": "function", "filter_language": "c"}]}, "reporting": {"report_dir": "test_reports", "baseline_file": "performance_baseline.json", "formats": ["markdown", "json", "html"], "include_performance_graphs": true, "include_coverage_report": false}, "ci_cd": {"fail_on_critical_test_failure": true, "fail_on_performance_regression": false, "max_test_duration": 1800, "retry_failed_tests": 1, "parallel_execution": false}}