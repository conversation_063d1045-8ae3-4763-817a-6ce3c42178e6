#!/usr/bin/env python3
"""
Verify that the updated tool file is properly deployed
"""

import os
import hashlib

def check_tool_file():
    """Check if the tool file contains our fixes"""
    print("🔍 Checking Tool File Deployment")
    print("=" * 60)
    
    # Expected locations for the tool file
    possible_locations = [
        "/app/backend/data/tools/open_webui_code_analyzer_tool.py",
        "~/home-ai-system/openwebui/tools/open_webui_code_analyzer_tool.py",
        "./open_webui_code_analyzer_tool.py"
    ]
    
    for location in possible_locations:
        expanded_location = os.path.expanduser(location)
        print(f"\n📁 Checking: {expanded_location}")
        
        if os.path.exists(expanded_location):
            print(f"   ✅ File exists")
            
            try:
                with open(expanded_location, 'r') as f:
                    content = f.read()
                
                # Check for our specific fixes
                fixes_to_check = [
                    "FIXED_VERSION_2025_07_19_v2",
                    "codebase_name': target_codebase",
                    "_detect_codebase_in_query",
                    "analyze complexity', 'code complexity analysis', 'complexity analysis'"
                ]
                
                found_fixes = []
                for fix in fixes_to_check:
                    if fix in content:
                        found_fixes.append(fix)
                        print(f"   ✅ Found: {fix}")
                    else:
                        print(f"   ❌ Missing: {fix}")
                
                print(f"\n   📊 Summary: {len(found_fixes)}/{len(fixes_to_check)} fixes found")
                
                if len(found_fixes) == len(fixes_to_check):
                    print(f"   🎉 ALL FIXES PRESENT - Tool file is updated correctly")
                    
                    # Get file hash for verification
                    file_hash = hashlib.md5(content.encode()).hexdigest()[:8]
                    print(f"   🔑 File hash: {file_hash}")
                    
                    return True
                else:
                    print(f"   ❌ MISSING FIXES - Tool file needs to be updated")
                    return False
                    
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
                return False
        else:
            print(f"   ❌ File not found")
    
    return False

def main():
    print("🔍 Tool File Deployment Verification")
    print("=" * 70)
    
    is_updated = check_tool_file()
    
    print(f"\n🎯 RESULT:")
    if is_updated:
        print("✅ Tool file is properly updated with all fixes")
        print("💡 If you're still getting complexity analysis, try:")
        print("   1. Restart OpenWebUI: docker-compose restart openwebui")
        print("   2. Clear browser cache and refresh")
        print("   3. Check OpenWebUI Admin Panel → Tools → Refresh")
    else:
        print("❌ Tool file is missing fixes or not found")
        print("💡 You need to:")
        print("   1. Copy the updated open_webui_code_analyzer_tool.py to the correct location")
        print("   2. Restart OpenWebUI")
        print("   3. Verify the tool loads the updated version")

if __name__ == "__main__":
    main()
