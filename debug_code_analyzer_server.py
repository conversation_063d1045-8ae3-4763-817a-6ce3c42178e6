#!/usr/bin/env python3
"""
Debug the code analyzer server to see why it's not finding codebases
"""

import requests
import json

def check_code_analyzer_server():
    """Check the code analyzer server status and endpoints"""
    print("🔍 Checking Code Analyzer Server")
    print("=" * 50)
    
    base_url = "http://192.168.0.77:5002"
    
    # Test 1: Health check
    print("\n1️⃣ Health Check:")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print(f"✅ Server is healthy")
            data = response.json()
            print(f"📊 Status: {data}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check exception: {e}")
    
    # Test 2: List all available endpoints
    print("\n2️⃣ Available Endpoints:")
    endpoints_to_test = [
        "/",
        "/docs",
        "/health",
        "/list_codebases",
        "/tools/list_codebases",
        "/search",
        "/query",
        "/codebases"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Exception - {e}")
    
    # Test 3: Try different list codebase endpoints
    print("\n3️⃣ Testing Codebase Listing:")
    codebase_endpoints = [
        "/list_codebases",
        "/tools/list_codebases", 
        "/codebases",
        "/api/codebases"
    ]
    
    for endpoint in codebase_endpoints:
        try:
            # Try GET
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                codebases = data.get('codebases', data.get('results', []))
                print(f"   ✅ GET {endpoint}: {len(codebases)} codebases")
                if codebases:
                    print(f"      Codebases: {codebases}")
                continue
            
            # Try POST
            response = requests.post(f"{base_url}{endpoint}", json={}, timeout=10)
            if response.status_code == 200:
                data = response.json()
                codebases = data.get('codebases', data.get('results', []))
                print(f"   ✅ POST {endpoint}: {len(codebases)} codebases")
                if codebases:
                    print(f"      Codebases: {codebases}")
                continue
                
            print(f"   ❌ {endpoint}: {response.status_code}")
            
        except Exception as e:
            print(f"   ❌ {endpoint}: Exception - {e}")
    
    # Test 4: Check if server is running in Docker
    print("\n4️⃣ Docker Container Check:")
    try:
        # Try to get server info that might indicate Docker environment
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Server is responding (likely in Docker)")
        else:
            print(f"❌ Server not responding properly")
    except Exception as e:
        print(f"❌ Cannot reach server - Docker container may be down")
        print(f"💡 Try: docker-compose up -d code-analyzer-server")

def check_direct_server_access():
    """Check if we can access the server directly on localhost"""
    print("\n🔍 Checking Direct Server Access")
    print("=" * 50)
    
    # If running in Docker with host networking, try localhost
    localhost_urls = [
        "http://localhost:5002",
        "http://127.0.0.1:5002",
        "http://home-ai-server.local:5002"
    ]
    
    for url in localhost_urls:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} is accessible")
                
                # Try to list codebases
                try:
                    cb_response = requests.post(f"{url}/tools/list_codebases", json={}, timeout=10)
                    if cb_response.status_code == 200:
                        data = cb_response.json()
                        codebases = data.get('codebases', [])
                        print(f"   📊 Codebases found: {len(codebases)}")
                        if codebases:
                            print(f"   📝 Available: {codebases}")
                        else:
                            print(f"   ⚠️ No codebases found - may need indexing")
                    else:
                        print(f"   ❌ Codebase listing failed: {cb_response.status_code}")
                except Exception as e:
                    print(f"   ❌ Codebase listing exception: {e}")
                    
                return url
            else:
                print(f"❌ {url}: {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")
    
    return None

def suggest_fixes():
    """Suggest fixes for the code analyzer server issues"""
    print("\n💡 SUGGESTED FIXES")
    print("=" * 50)
    
    print("1️⃣ Check if Docker containers are running:")
    print("   docker-compose ps")
    print("   docker-compose up -d code-analyzer-server")
    
    print("\n2️⃣ Check if codebases need to be indexed:")
    print("   # Access web management interface")
    print("   http://home-ai-server.local:5003")
    print("   # Look for 'Create Vector Database' or 'Reprocess' options")
    
    print("\n3️⃣ Check server logs:")
    print("   docker-compose logs code-analyzer-server")
    
    print("\n4️⃣ Restart the code analyzer server:")
    print("   docker-compose restart code-analyzer-server")
    
    print("\n5️⃣ If utils codebase exists but isn't indexed:")
    print("   # Use the web interface to create vector database for utils")
    print("   # Or use API: POST /tools/create_vector_database")
    print("   # Payload: {'codebase_name': 'utils'}")

def main():
    print("🔍 Code Analyzer Server Diagnosis")
    print("=" * 60)
    print("Investigating why T5810 can't find the utils codebase")
    
    # Check the server
    check_code_analyzer_server()
    
    # Try direct access
    working_url = check_direct_server_access()
    
    # Provide suggestions
    suggest_fixes()
    
    print(f"\n🎯 CONCLUSION")
    print("=" * 60)
    
    if working_url:
        print(f"✅ Server is accessible at: {working_url}")
        print(f"❌ But no codebases are indexed")
        print(f"💡 SOLUTION: Index the utils codebase using the web interface")
    else:
        print(f"❌ Code analyzer server is not accessible")
        print(f"💡 SOLUTION: Start the Docker containers")
        print(f"   docker-compose up -d")

if __name__ == "__main__":
    main()
