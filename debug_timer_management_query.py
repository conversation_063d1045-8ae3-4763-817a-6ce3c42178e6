#!/usr/bin/env python3
"""
Debug the timer management query hallucination
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
import time

def test_timer_query_step_by_step():
    """Test the timer management query step by step"""
    print("🔍 Testing Timer Management Query")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # First, test if there are actually timer-related functions in utils
    print("1️⃣ Testing direct tool execution for timer functions:")
    
    tool_result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'timer time timeout clock',
        'max_results': 10
    })
    
    print(f"Tool result type: {type(tool_result)}")
    
    if 'error' in tool_result:
        print(f"❌ Tool error: {tool_result['error']}")
        return False
    elif 'results' in tool_result and tool_result['results']:
        results = tool_result['results']
        print(f"✅ Found {len(results)} timer-related results")
        
        # Show first few results
        for i, result in enumerate(results[:3]):
            content = result.get('content', '')[:200]
            metadata = result.get('metadata', {})
            file_path = metadata.get('file_path', 'Unknown')
            
            print(f"   Result {i+1}: {file_path}")
            print(f"   Content: {content}...")
            print()
        
        return True
    else:
        print(f"⚠️ No timer-related results found in utils codebase")
        print(f"This might explain why T5810 is hallucinating")
        return False

def test_timer_query_with_t5810():
    """Test the full T5810 integration for timer query"""
    print(f"\n2️⃣ Testing T5810 integration:")
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    query = "Show timer management functions in the utils codebase. Use the query_codebase tool to search for timer, timeout, and clock functions."
    
    print(f"Query: {query}")
    
    start_time = time.time()
    result = analyzer.chat_with_tools(query)
    end_time = time.time()
    
    response_time = end_time - start_time
    
    print(f"⏱️  Response time: {response_time:.2f}s")
    print(f"📝 Result length: {len(result)}")
    print(f"📊 Result preview:")
    print(result[:500] + "..." if len(result) > 500 else result)
    
    # Check for hallucination vs real code
    hallucination_indicators = [
        "import time",
        "class Timer:",
        "def start(self):",
        "Example usage:",
        "This code defines",
        "Note that this is just one possible implementation"
    ]
    
    real_code_indicators = [
        "Triangle MicroWorks",
        "/app/source_code/utils",
        "tmw",
        "Copyright",
        ".h",
        ".c"
    ]
    
    found_hallucination = [ind for ind in hallucination_indicators if ind in result]
    found_real_code = [ind for ind in real_code_indicators if ind in result]
    
    print(f"\n🔍 Analysis:")
    print(f"Hallucination indicators: {found_hallucination}")
    print(f"Real code indicators: {found_real_code}")
    
    if found_real_code and not found_hallucination:
        print(f"✅ SUCCESS: Shows real timer code")
        return True
    elif found_hallucination:
        print(f"❌ HALLUCINATING: Generating generic examples")
        return False
    else:
        print(f"⚠️ UNCLEAR: Neither clear hallucination nor real code")
        return False

def test_different_timer_queries():
    """Test different variations of timer queries"""
    print(f"\n3️⃣ Testing different timer query variations:")
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    queries = [
        "Find timer functions in utils codebase",
        "Search utils for timeout management code",
        "Show clock-related functions from utils",
        "Use query_codebase to find timer code in utils codebase"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n[{i}] Query: {query}")
        print("-" * 40)
        
        try:
            result = analyzer.chat_with_tools(query)
            
            # Quick check for hallucination
            if "import time" in result or "class Timer:" in result:
                print(f"❌ HALLUCINATING: Generic Python examples")
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code", "tmw"]):
                print(f"✅ REAL CODE: Shows actual utils code")
            else:
                print(f"⚠️ UNCLEAR: {result[:100]}...")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🔍 Debugging Timer Management Query Hallucination")
    print("=" * 70)
    
    # Test if timer functions exist in utils
    timer_functions_exist = test_timer_query_step_by_step()
    
    # Test T5810 integration
    t5810_works = test_timer_query_with_t5810()
    
    # Test different variations
    test_different_timer_queries()
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    
    if not timer_functions_exist:
        print(f"💡 ROOT CAUSE: No timer functions found in utils codebase")
        print(f"   T5810 is hallucinating because there's no real timer code to show")
        print(f"   This is different from memory management where real code exists")
    elif timer_functions_exist and not t5810_works:
        print(f"💡 ROOT CAUSE: Timer functions exist but T5810 isn't using them")
        print(f"   Same issue as memory management - need better prompting")
    else:
        print(f"💡 UNCLEAR: Need more investigation")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"1. Check if utils codebase actually contains timer management code")
    print(f"2. If no timer code exists, T5810's response is technically correct")
    print(f"3. If timer code exists, apply the same fix as memory management")

if __name__ == "__main__":
    main()
