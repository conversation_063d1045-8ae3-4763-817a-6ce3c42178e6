#!/usr/bin/env python3
"""
Test which version of the tool is being used by checking the routing logic
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def test_tool_version():
    """Test the current tool version and routing logic"""
    print("🔍 Testing Current Tool Version and Routing")
    print("=" * 60)
    
    tool = Tools()
    
    # Check the server URL configuration
    print(f"📊 Tool Configuration:")
    print(f"   Server URL: {tool.valves.code_analyzer_server_url}")
    print(f"   Current Codebase: {tool.valves.current_codebase}")
    
    # Test the problematic query
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"\n🔍 Testing Query Routing:")
    print(f"Query: {query}")
    
    # Check intent detection
    intent = tool._detect_query_intent(query)
    print(f"Detected Intent: {intent}")
    
    if intent == "codebase_management":
        print(f"❌ PROBLEM: Still detecting as management intent")
        print(f"   This will route to complexity analysis")
        
        # Check what specific phrase is triggering this
        query_lower = query.lower()
        management_phrases = [
            'list codebase', 'show codebase', 'available codebase',
            'select codebase', 'choose codebase', 'switch codebase',
            'process codebase', 'index codebase', 'delete codebase',
            'stats', 'statistics', 'status',
            'get stats', 'show stats', 'codebase stats',
            'get statistics', 'show statistics', 'codebase statistics',
            'analyze complexity', 'code complexity analysis', 'complexity analysis'
        ]
        
        found_phrases = [phrase for phrase in management_phrases if phrase in query_lower]
        print(f"   Triggering phrases: {found_phrases}")
        
    elif intent == "code_analysis":
        print(f"✅ CORRECT: Detected as code analysis")
        print(f"   This will get context injection")
    else:
        print(f"⚠️ UNEXPECTED: Intent = {intent}")
    
    # Test the complexity routing specifically
    print(f"\n🔧 Testing Complexity Routing:")
    complexity_phrases = ['analyze complexity', 'code complexity analysis', 'complexity analysis']
    query_lower = query.lower()
    
    complexity_match = any(phrase in query_lower for phrase in complexity_phrases)
    print(f"Complexity phrases: {complexity_phrases}")
    print(f"Query matches complexity: {complexity_match}")
    
    if complexity_match:
        print(f"❌ PROBLEM: Query incorrectly matches complexity phrases")
    else:
        print(f"✅ CORRECT: Query does not match complexity phrases")

async def test_old_vs_new_routing():
    """Test if the tool has the old or new routing logic"""
    print(f"\n🔍 Testing Old vs New Routing Logic")
    print("=" * 60)
    
    tool = Tools()
    
    # Test queries that should behave differently in old vs new versions
    test_cases = [
        {
            "query": "tell me about a modbus holding register and the functions used to manage it",
            "should_be": "code_analysis",
            "old_behavior": "codebase_management"
        },
        {
            "query": "analyze complexity in modbus codebase", 
            "should_be": "codebase_management",
            "old_behavior": "codebase_management"
        },
        {
            "query": "what is a coil in the modbus codebase",
            "should_be": "code_analysis", 
            "old_behavior": "code_analysis"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        expected = test_case["should_be"]
        old_behavior = test_case["old_behavior"]
        
        print(f"\n[{i}] Query: {query[:50]}...")
        
        intent = tool._detect_query_intent(query)
        print(f"    Detected: {intent}")
        print(f"    Expected: {expected}")
        
        if intent == expected:
            print(f"    ✅ CORRECT: Using new routing logic")
        elif intent == old_behavior and old_behavior != expected:
            print(f"    ❌ PROBLEM: Using old routing logic")
        else:
            print(f"    ⚠️ UNEXPECTED: Neither old nor new behavior")

async def main():
    print("🔍 Testing Tool Version and Routing Logic")
    print("=" * 70)
    
    await test_tool_version()
    await test_old_vs_new_routing()
    
    print(f"\n💡 DIAGNOSIS:")
    print("If the tool shows 'codebase_management' intent for the holding register query,")
    print("then OpenWebUI is still using the OLD version of the tool file.")
    print("\nSOLUTION: Force OpenWebUI to reload the tool:")
    print("1. docker-compose restart openwebui")
    print("2. Admin Panel → Tools → Refresh code_analyzer_tool")
    print("3. Clear browser cache")

if __name__ == "__main__":
    asyncio.run(main())
