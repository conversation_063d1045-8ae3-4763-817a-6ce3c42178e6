#!/usr/bin/env python3
"""
Debug why the search is failing for timer management queries
"""

import requests
import json

def test_direct_search_endpoints():
    """Test the search endpoints directly"""
    print("🔍 Testing Direct Search Endpoints")
    print("=" * 60)
    
    base_url = "http://192.168.0.77:5002"
    
    # Test different search endpoints
    endpoints_to_test = [
        ("/search", {"query": "timer management", "codebase_name": "utils", "n_results": 5}),
        ("/tools/query_codebase", {"codebase_name": "utils", "query": "timer management", "max_results": 5}),
        ("/tools/framework_query", {"codebase_name": "utils", "query": "timer management", "n_results": 5})
    ]
    
    for endpoint, payload in endpoints_to_test:
        print(f"\n📤 Testing {endpoint}:")
        print(f"   Payload: {payload}")
        
        try:
            response = requests.post(f"{base_url}{endpoint}", json=payload, timeout=30)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                # Check different result formats
                if 'results' in data and data['results']:
                    results = data['results']
                    print(f"   ✅ Found {len(results)} results")
                    
                    # Show first result
                    first_result = results[0]
                    if isinstance(first_result, dict):
                        content = first_result.get('content', '')[:200]
                        metadata = first_result.get('metadata', {})
                        file_path = metadata.get('file_path', 'Unknown')
                        print(f"   📄 First result: {file_path}")
                        print(f"   📝 Content: {content}...")
                    else:
                        print(f"   📝 First result: {str(first_result)[:200]}...")
                        
                elif 'result' in data:
                    result = data['result']
                    print(f"   📝 Result: {result[:200]}...")
                    
                    if "No relevant code context found" in result:
                        print(f"   ❌ SEARCH FAILED: No results found")
                    else:
                        print(f"   ✅ SEARCH SUCCESS: Found content")
                        
                else:
                    print(f"   ⚠️ Unexpected format: {list(data.keys())}")
                    
            else:
                print(f"   ❌ HTTP Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_different_timer_queries():
    """Test different timer-related queries"""
    print(f"\n🧪 Testing Different Timer Queries")
    print("=" * 60)
    
    base_url = "http://192.168.0.77:5002"
    
    queries = [
        "timer management",
        "timer",
        "tmwtimer", 
        "timeout",
        "clock",
        "time",
        "timer functions",
        "timer management functions"
    ]
    
    for query in queries:
        print(f"\n📤 Query: '{query}'")
        
        try:
            response = requests.post(
                f"{base_url}/search",
                json={"query": query, "codebase_name": "utils", "n_results": 3},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                if results:
                    print(f"   ✅ Found {len(results)} results")
                    
                    # Check if results contain timer-related content
                    for i, result in enumerate(results[:2]):
                        content = result.get('content', '')
                        if any(term in content.lower() for term in ['timer', 'timeout', 'clock', 'tmwtimer']):
                            print(f"   🎯 Result {i+1}: Contains timer content")
                        else:
                            print(f"   ⚠️ Result {i+1}: May not be timer-related")
                else:
                    print(f"   ❌ No results found")
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_what_t5810_receives():
    """Test what T5810 actually receives from the search"""
    print(f"\n🎯 Testing What T5810 Receives")
    print("=" * 60)
    
    from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Test the exact tool execution that T5810 would do
    print("1️⃣ Testing direct tool execution:")
    
    result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'timer management',
        'max_results': 5
    })
    
    print(f"Tool result type: {type(result)}")
    print(f"Tool result keys: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
    
    if 'error' in result:
        print(f"❌ Tool error: {result['error']}")
    elif 'results' in result:
        results = result['results']
        print(f"✅ Found {len(results)} results")
        
        if results:
            # Show what T5810 would see
            first_result = results[0]
            content = first_result.get('content', '')
            print(f"📝 First result content length: {len(content)}")
            print(f"📄 First result preview: {content[:300]}...")
            
            # Check if it's actually timer-related
            if any(term in content.lower() for term in ['timer', 'tmwtimer', 'timeout']):
                print(f"✅ Contains timer-related content")
            else:
                print(f"⚠️ May not be timer-related")
        else:
            print(f"❌ Empty results list")
    else:
        print(f"⚠️ Unexpected result format: {result}")

def main():
    print("🔍 Debugging Search Failure for Timer Management")
    print("=" * 70)
    
    # Test direct endpoints
    test_direct_search_endpoints()
    
    # Test different queries
    test_different_timer_queries()
    
    # Test what T5810 receives
    test_what_t5810_receives()
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    print("If the search endpoints return 'No relevant code context found',")
    print("then T5810 is hallucinating because it's not getting real timer code.")
    print("The fix would be to:")
    print("1. Improve the search query to find timer-related code")
    print("2. Or handle the 'no results' case better in T5810")

if __name__ == "__main__":
    main()
