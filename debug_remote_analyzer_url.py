#!/usr/bin/env python3
"""
Debug the RemoteOllamaAnalyzer URL issue
"""

from open_webui_code_analyzer_tool import RemoteOllamaAnalyzer

def test_remote_analyzer_urls():
    """Test what URLs the RemoteOllamaAnalyzer actually uses"""
    print("🔍 Testing RemoteOllamaAnalyzer URLs")
    print("=" * 60)
    
    # Create analyzers like the main tool does
    lynn_pc_analyzer = RemoteOllamaAnalyzer("http://************:11434")
    t5810_analyzer = RemoteOllamaAnalyzer("http://************:11434")
    
    print(f"lynn-pc analyzer:")
    print(f"  Ollama URL: {lynn_pc_analyzer.ollama_url}")
    print(f"  Code Analyzer URL: {lynn_pc_analyzer.code_analyzer_url}")
    
    print(f"\nT5810 analyzer:")
    print(f"  Ollama URL: {t5810_analyzer.ollama_url}")
    print(f"  Code Analyzer URL: {t5810_analyzer.code_analyzer_url}")
    
    # Check if there's any URL manipulation happening
    if lynn_pc_analyzer.code_analyzer_url == "http://************:5002":
        print(f"\n✅ lynn-pc analyzer correctly uses home-ai-server for code analysis")
    else:
        print(f"\n❌ lynn-pc analyzer uses wrong URL: {lynn_pc_analyzer.code_analyzer_url}")
    
    # Test a tool execution to see what URL it actually hits
    print(f"\n🔧 Testing tool execution URL:")
    
    try:
        # This should hit http://************:5002, not http://************:5002
        result = lynn_pc_analyzer.execute_tool('list_codebases', {})
        
        if 'codebases' in result:
            print(f"✅ Tool execution successful - hit correct URL")
            print(f"   Found {len(result['codebases'])} codebases")
        elif 'error' in result:
            print(f"❌ Tool execution failed: {result['error']}")
            
            # Check if the error mentions the wrong URL
            if "************:5002" in result['error']:
                print(f"🔍 ERROR: Tool is hitting lynn-pc:5002 instead of home-ai-server:5002")
            elif "************:5002" in result['error']:
                print(f"✅ Tool correctly hits home-ai-server:5002 (but server may be down)")
        else:
            print(f"⚠️ Unexpected result: {result}")
            
    except Exception as e:
        print(f"❌ Exception during tool execution: {e}")

def test_url_construction_bug():
    """Test if there's a bug in URL construction"""
    print(f"\n🔍 Testing URL Construction")
    print("=" * 60)
    
    analyzer = RemoteOllamaAnalyzer("http://************:11434")
    
    # Check if the analyzer is somehow modifying the URL based on the ollama_url
    print(f"Ollama URL: {analyzer.ollama_url}")
    print(f"Code Analyzer URL: {analyzer.code_analyzer_url}")
    
    # Check if there's any dynamic URL construction
    expected_url = "http://************:5002"
    actual_url = analyzer.code_analyzer_url
    
    if actual_url == expected_url:
        print(f"✅ URL construction is correct")
    else:
        print(f"❌ URL construction is wrong!")
        print(f"   Expected: {expected_url}")
        print(f"   Actual: {actual_url}")
        
        # Check if it's extracting IP from ollama_url
        if "************" in actual_url:
            print(f"🔍 BUG: Code analyzer URL is using lynn-pc IP instead of home-ai-server")

def main():
    print("🔍 Debugging RemoteOllamaAnalyzer URL Issue")
    print("=" * 70)
    print("Goal: Find why requests go to lynn-pc:5002 instead of home-ai-server:5002")
    
    # Test the URLs
    test_remote_analyzer_urls()
    
    # Test for URL construction bugs
    test_url_construction_bug()
    
    print(f"\n💡 EXPECTED BEHAVIOR:")
    print("RemoteOllamaAnalyzer should ALWAYS use home-ai-server:5002 for code analysis,")
    print("regardless of which Ollama server it's configured to use for LLM calls.")

if __name__ == "__main__":
    main()
