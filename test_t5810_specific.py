#!/usr/bin/env python3
"""
Test T5810 model specifically to debug the remaining issue
"""

import requests
import json
import time

def test_t5810_model():
    """Test T5810 model with detailed debugging"""
    print("🔍 Testing T5810 Model Specifically")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test with T5810 model
    model = "T5810 - .llama3:latest"
    
    print(f"🧪 Testing model: {model}")
    print("-" * 30)
    
    # Test query
    query = "list codebases"
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print(f"📤 Sending request...")
    print(f"   Model: {model}")
    print(f"   Query: {query}")
    print(f"   Tool IDs: {payload['tool_ids']}")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        elapsed = time.time() - start_time
        
        print(f"📥 Response received in {elapsed:.2f}s")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract response details
            choices = data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                print(f"   Content length: {len(content)} chars")
                print(f"   Tool calls: {len(tool_calls)}")
                
                # Show first 300 chars of content
                print(f"\n📝 Response content:")
                print("-" * 20)
                print(content[:300] + ("..." if len(content) > 300 else ""))
                print("-" * 20)
                
                # Analyze the response
                print(f"\n🔍 Response Analysis:")
                
                # Check for real codebase indicators
                real_codebases = ["utils", "z80emu", "library-management-system", "test_project", "bookstore"]
                found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
                
                # Check for generic indicators
                generic_indicators = ["wordpress", "react", "django", "popular", "open-source"]
                found_generic = [gi for gi in generic_indicators if gi.lower() in content.lower()]
                
                print(f"   Real codebases found: {found_real}")
                print(f"   Generic indicators: {found_generic}")
                
                if found_real:
                    print("   ✅ TOOL IS WORKING - Found real codebases!")
                elif found_generic:
                    print("   ❌ TOOL NOT WORKING - Generic response detected")
                else:
                    print("   ❓ UNCLEAR - Response doesn't match expected patterns")
                
                # Check for tool-specific indicators
                tool_indicators = [
                    "enhanced search", "chunks found", "selected codebase", 
                    "available codebases", "code context", "documents found"
                ]
                found_tool = [ti for ti in tool_indicators if ti.lower() in content.lower()]
                
                if found_tool:
                    print(f"   🔧 Tool indicators found: {found_tool}")
                else:
                    print("   🔧 No tool-specific indicators found")
                
                # Show tool calls if any
                if tool_calls:
                    print(f"\n🛠️ Tool Calls:")
                    for i, tc in enumerate(tool_calls):
                        func_name = tc.get("function", {}).get("name", "unknown")
                        print(f"   {i+1}. {func_name}")
                else:
                    print(f"\n🛠️ No tool calls detected")
                
            else:
                print("   ❌ No choices in response")
                
        elif response.status_code == 400:
            print("   ❌ Bad Request (400)")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error text: {response.text[:200]}")
                
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except requests.exceptions.Timeout:
        print("   ❌ Request timeout")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_code_analyzer_server():
    """Test if the code analyzer server is reachable"""
    print(f"\n🌐 Testing Code Analyzer Server")
    print("-" * 30)
    
    url = "http://192.168.0.77:5002/health"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ Server reachable: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   Collections: {len(data.get('collections', []))}")
                print(f"   Total chunks: {data.get('total_chunks', 'unknown')}")
            except:
                print(f"   Response: {response.text[:100]}...")
        
    except Exception as e:
        print(f"❌ Server not reachable: {e}")

def main():
    # Test server connectivity first
    test_code_analyzer_server()
    
    # Test T5810 model
    test_t5810_model()
    
    print(f"\n💡 TROUBLESHOOTING GUIDE:")
    print("=" * 50)
    print("If tool is still not working:")
    print("1. Check if tool configuration was saved properly")
    print("2. Try restarting OpenWebUI")
    print("3. Check T5810 machine can reach 192.168.0.77:5002")
    print("4. Verify tool is enabled for T5810 model in OpenWebUI")
    print("5. Check OpenWebUI logs for errors")

if __name__ == "__main__":
    main()
