#!/usr/bin/env python3
"""
Test the search endpoint directly to see if it's working
"""

import requests
import json

def test_search_endpoint():
    """Test the search endpoint that's failing in OpenWebUI"""
    print("🔍 Testing Search Endpoint Directly")
    print("=" * 60)
    
    url = "http://192.168.0.77:5002/search"
    payload = {
        "query": "holding register",
        "codebase_name": "modbus",
        "n_results": 3
    }
    
    print(f"📤 URL: {url}")
    print(f"📦 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📥 Status: {response.status_code}")
        print(f"📊 Content-Length: {response.headers.get('content-length', 'Unknown')}")
        
        if response.status_code == 200:
            data = response.json()
            
            if isinstance(data, dict) and 'results' in data:
                results = data['results']
                print(f"✅ SUCCESS: Found {len(results)} results")
                
                if results:
                    first_result = results[0]
                    content = first_result.get('content', '')
                    metadata = first_result.get('metadata', {})
                    file_path = metadata.get('file_path', 'Unknown')
                    
                    print(f"📄 First result: {file_path}")
                    print(f"📝 Content preview: {content[:200]}...")
                    
                    # Check for real holding register content
                    if any(term in content.lower() for term in ['holding', 'register', 'triangle microworks']):
                        print(f"🎯 Contains real holding register content")
                        return True
                    else:
                        print(f"⚠️ May not contain holding register content")
                        return False
                else:
                    print(f"❌ Empty results list")
                    return False
            else:
                print(f"⚠️ Unexpected response format")
                print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                return False
                
        elif response.status_code == 400:
            print(f"❌ 400 BAD REQUEST")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error: {response.text}")
            return False
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_codebases_endpoint():
    """Test if the modbus codebase is available"""
    print(f"\n📊 Testing Codebases Endpoint")
    print("=" * 60)
    
    url = "http://192.168.0.77:5002/codebases"
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if 'codebases' in data:
                codebases = [cb.get('name', '') for cb in data['codebases']]
                print(f"✅ Available codebases: {codebases}")
                
                if 'modbus' in codebases:
                    print(f"✅ modbus codebase is available")
                    return True
                else:
                    print(f"❌ modbus codebase NOT found")
                    return False
            else:
                print(f"⚠️ Unexpected response format")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🔍 Testing Code Analyzer Server Endpoints")
    print("=" * 70)
    
    # Test if modbus codebase exists
    codebases_work = test_codebases_endpoint()
    
    # Test search endpoint
    search_works = test_search_endpoint()
    
    print(f"\n🎯 RESULTS")
    print("=" * 70)
    print(f"Codebases endpoint: {'✅ WORKING' if codebases_work else '❌ BROKEN'}")
    print(f"Search endpoint:    {'✅ WORKING' if search_works else '❌ BROKEN'}")
    
    if codebases_work and search_works:
        print(f"\n✅ SERVER IS WORKING CORRECTLY")
        print(f"The issue is likely in the OpenWebUI tool configuration or caching")
        print(f"\n💡 NEXT STEPS:")
        print(f"1. Restart OpenWebUI: docker-compose restart openwebui")
        print(f"2. Clear browser cache and refresh")
        print(f"3. Check OpenWebUI tool configuration")
    elif not codebases_work:
        print(f"\n❌ MODBUS CODEBASE NOT AVAILABLE")
        print(f"The server is running but modbus codebase is missing")
    elif not search_works:
        print(f"\n❌ SEARCH ENDPOINT BROKEN")
        print(f"The server has issues with the search functionality")

if __name__ == "__main__":
    main()
