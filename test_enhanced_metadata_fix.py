#!/usr/bin/env python3
"""
Test the enhanced metadata detection fix
"""

import requests

def test_enhanced_metadata_detection():
    """Test if the enhanced metadata detection is working correctly"""
    print("🧪 Testing Enhanced Metadata Detection Fix")
    print("=" * 50)
    
    try:
        # Get codebase list
        response = requests.post(
            "http://192.168.0.77:5002/tools/list_codebases",
            json={},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json().get('result', '')
            
            print("📋 Codebase Status After Fix:")
            print("-" * 30)
            
            # Parse the response to check for improvements
            lines = result.split('\n')
            enhanced_count = 0
            basic_count = 0
            total_count = 0
            
            for line in lines:
                if 'Enhanced Metadata: Yes' in line:
                    enhanced_count += 1
                elif 'Enhanced Metadata: No' in line:
                    basic_count += 1
                
                if line.strip().startswith('**') and any(emoji in line for emoji in ['✅', '🚀', '⚠️']):
                    total_count += 1
            
            print(f"📊 Summary:")
            print(f"   Total codebases: {total_count}")
            print(f"   Enhanced metadata: {enhanced_count}")
            print(f"   Basic metadata: {basic_count}")
            print(f"   Enhancement rate: {(enhanced_count/total_count*100):.1f}%" if total_count > 0 else "   Enhancement rate: 0%")
            
            # Show first few codebases as examples
            print(f"\n📝 Sample Output:")
            print("-" * 30)
            sample_lines = lines[:20]  # First 20 lines
            for line in sample_lines:
                if line.strip():
                    print(line)
            
            # Check for improvements
            if enhanced_count > 0:
                print(f"\n✅ SUCCESS: Enhanced metadata detection is working!")
                print(f"   {enhanced_count} codebases now show enhanced metadata")
            else:
                print(f"\n⚠️ PARTIAL: No enhanced metadata detected yet")
                print(f"   This might be expected if codebases need reprocessing")
            
            # Check for reduced recommendations
            generic_recs = result.count("Re-process codebase to enable enhanced features")
            if generic_recs == 0:
                print(f"✅ Recommendation spam eliminated!")
            else:
                print(f"⚠️ Still showing {generic_recs} generic recommendations")
            
            return enhanced_count > 0
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_health_check():
    """Test if health check shows improved enhanced metadata counts"""
    print(f"\n🏥 Testing Health Check Enhancement Counts")
    print("-" * 30)
    
    try:
        response = requests.get("http://192.168.0.77:5002/health", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            enhanced_collections = data.get('enhanced_collections_count', 0)
            total_collections = data.get('chromadb_collections', 0)
            
            print(f"Enhanced collections: {enhanced_collections}")
            print(f"Total collections: {total_collections}")
            
            if enhanced_collections > 0:
                print(f"✅ Health check shows enhanced collections!")
                enhancement_rate = (enhanced_collections / total_collections * 100) if total_collections > 0 else 0
                print(f"   Enhancement rate: {enhancement_rate:.1f}%")
                return True
            else:
                print(f"⚠️ Health check shows no enhanced collections")
                return False
                
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check exception: {e}")
        return False

def main():
    print("🚀 Testing Enhanced Metadata Detection Fix")
    print("=" * 50)
    print("This test verifies that the presentation issue has been fixed.")
    
    # Test codebase listing
    listing_ok = test_enhanced_metadata_detection()
    
    # Test health check
    health_ok = test_health_check()
    
    print(f"\n🎯 RESULTS")
    print("=" * 50)
    
    if listing_ok and health_ok:
        print("🎉 COMPLETE SUCCESS!")
        print("✅ Enhanced metadata detection is working")
        print("✅ Health check shows correct counts")
        print("✅ Presentation issue has been fixed")
        
        print(f"\n💡 What this means:")
        print("• Your codebases already have enhanced features")
        print("• The system was just not detecting them correctly")
        print("• No reprocessing needed - it was a display bug")
        
    elif listing_ok:
        print("✅ PARTIAL SUCCESS!")
        print("✅ Enhanced metadata detection is working")
        print("⚠️ Health check needs attention")
        
    elif health_ok:
        print("✅ PARTIAL SUCCESS!")
        print("⚠️ Codebase listing needs attention")
        print("✅ Health check is working")
        
    else:
        print("❌ NEEDS MORE WORK")
        print("⚠️ Enhanced metadata detection still has issues")
        print("💡 May need to check the actual metadata in the database")
    
    print(f"\n📋 Next Steps:")
    print("1. Restart the code analyzer server to pick up changes")
    print("2. Test the codebase listing again")
    print("3. Check if enhanced features are working in queries")

if __name__ == "__main__":
    main()
