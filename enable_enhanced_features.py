#!/usr/bin/env python3
"""
Enable enhanced features for all codebases
"""

import requests
import time
import json

def get_codebases():
    """Get list of all codebases"""
    try:
        response = requests.post("http://************:5002/tools/list_codebases", json={}, timeout=30)
        if response.status_code == 200:
            # Parse the response to extract codebase names
            result = response.json().get('result', '')
            
            # Extract codebase names from the formatted response
            codebases = []
            lines = result.split('\n')
            for line in lines:
                if line.startswith('**✅') or line.startswith('**⚠️'):
                    # Extract codebase name between ** markers
                    parts = line.split('**')
                    if len(parts) >= 3:
                        codebase_name = parts[2].strip()
                        codebases.append(codebase_name)
            
            return codebases
        else:
            print(f"Error getting codebases: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error: {e}")
        return []

def enhance_codebase(codebase_name):
    """Enable enhanced features for a specific codebase"""
    print(f"🔄 Processing {codebase_name} with enhanced features...")
    
    try:
        # Use the re-process endpoint with enhanced features
        payload = {
            "codebase_name": codebase_name,
            "enable_enhanced_features": True,
            "force_reprocess": True
        }
        
        response = requests.post(
            "http://************:5002/tools/process_codebase",
            json=payload,
            timeout=300  # 5 minutes timeout for processing
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {codebase_name}: Enhanced features enabled")
            return True
        else:
            print(f"❌ {codebase_name}: Error {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏳ {codebase_name}: Processing timeout (may still be running)")
        return False
    except Exception as e:
        print(f"❌ {codebase_name}: Error - {e}")
        return False

def check_enhancement_status():
    """Check the current enhancement status"""
    try:
        response = requests.get("http://************:5002/health", timeout=30)
        if response.status_code == 200:
            data = response.json()
            enhanced_count = data.get('enhanced_codebases', 0)
            total_count = data.get('available_codebases', 0)
            
            print(f"📊 Enhancement Status: {enhanced_count}/{total_count} codebases enhanced")
            return enhanced_count, total_count
        else:
            print(f"Error checking status: {response.status_code}")
            return 0, 0
    except Exception as e:
        print(f"Error: {e}")
        return 0, 0

def main():
    print("🚀 Enabling Enhanced Features for All Codebases")
    print("=" * 60)
    
    # Check current status
    enhanced_before, total_before = check_enhancement_status()
    
    # Get list of codebases
    print("\n📋 Getting list of codebases...")
    codebases = get_codebases()
    
    if not codebases:
        print("❌ Could not get codebase list")
        return
    
    print(f"Found {len(codebases)} codebases:")
    for cb in codebases:
        print(f"   • {cb}")
    
    # Process each codebase
    print(f"\n🔄 Processing codebases with enhanced features...")
    print("This may take several minutes per codebase...")
    
    successful = 0
    failed = 0
    
    for i, codebase in enumerate(codebases, 1):
        print(f"\n[{i}/{len(codebases)}] Processing {codebase}...")
        
        if enhance_codebase(codebase):
            successful += 1
        else:
            failed += 1
        
        # Small delay between processing
        if i < len(codebases):
            time.sleep(2)
    
    # Check final status
    print(f"\n📊 Final Results:")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    
    # Check enhancement status again
    print(f"\n🔍 Checking final enhancement status...")
    enhanced_after, total_after = check_enhancement_status()
    
    if enhanced_after > enhanced_before:
        print(f"🎉 Success! Enhanced codebases increased from {enhanced_before} to {enhanced_after}")
    else:
        print(f"⚠️ Enhancement count unchanged: {enhanced_after}")
    
    print(f"\n💡 Next steps:")
    print("1. Check the web interface at http://home-ai-server.local:5003")
    print("2. Test enhanced search capabilities")
    print("3. Verify improved code analysis results")

if __name__ == "__main__":
    main()
