#!/usr/bin/env python3
"""
Verify lynn-pc tool calling is working properly
"""

import requests
import json

def test_remote_tool_execution(server_name, server_url):
    """Test that a remote server can actually execute tools"""
    print(f"🧪 Verifying {server_name} Tool Execution")
    print("=" * 50)

    ollama_url = server_url
    
    # Simple tool that should definitely work
    tools = [{
        'type': 'function',
        'function': {
            'name': 'list_codebases',
            'description': 'List all available codebases for analysis',
            'parameters': {
                'type': 'object',
                'properties': {},
                'required': [],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Please list all available codebases using the list_codebases tool'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print(f"📤 Testing explicit tool request on {server_name}")
    print(f"🔗 URL: {ollama_url}/api/chat")

    try:
        response = requests.post(
            f"{ollama_url}/api/chat",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ Response received!")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content: {content[:200]}...")
            
            if tool_calls:
                print(f"🎉 SUCCESS: {server_name} is calling tools!")
                for i, tc in enumerate(tool_calls):
                    func = tc.get('function', {})
                    name = func.get('name', 'unknown')
                    args = func.get('arguments', '{}')
                    print(f"   Tool {i+1}: {name}")
                    print(f"   Arguments: {args}")
                return True
            else:
                print(f"⚠️ {server_name} responded but didn't call tools")
                print(f"   This might be a prompt issue, not a technical issue")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_code_analyzer_server_connection():
    """Test if we can reach the code analyzer server from here"""
    print(f"\n🔗 Testing Code Analyzer Server Connection")
    print("-" * 40)
    
    code_analyzer_url = "http://192.168.0.77:5002"
    
    try:
        # Test the list_codebases endpoint
        response = requests.post(
            f"{code_analyzer_url}/tools/list_codebases",
            json={},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Code analyzer server is reachable!")
            print(f"📊 Response: {str(data)[:200]}...")
            return True
        else:
            print(f"❌ Code analyzer server error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cannot reach code analyzer server: {e}")
        return False

def test_full_workflow(server_name, server_url):
    """Test the complete workflow: remote server -> tool call -> code analyzer -> response"""
    print(f"\n🔄 Testing Complete Workflow on {server_name}")
    print("-" * 40)

    from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

    analyzer = DirectOllamaCodeAnalyzer(server_url)
    
    # Simple query that should definitely trigger a tool call
    query = "Use the list_codebases tool to show me all available codebases"
    
    print(f"📤 Query: {query}")
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📥 Result: {result[:300]}...")
    
    # Check if we got real codebase data
    real_codebases = ["utils", "z80emu", "go-example-master", "TypeScript-Node-Starter-master"]
    found_real = [cb for cb in real_codebases if cb.lower() in result.lower()]
    
    if found_real:
        print(f"✅ SUCCESS: Found real codebases: {found_real}")
        return True
    else:
        print(f"⚠️ No real codebase data found")
        return False

def test_server(server_name, server_url):
    """Test a specific server"""
    print(f"\n🎯 {server_name} Tool Calling Verification")
    print("=" * 60)

    # Test 1: Basic tool calling
    tool_calling_works = test_remote_tool_execution(server_name, server_url)

    # Test 2: Code analyzer server connection (only test once)
    server_reachable = test_code_analyzer_server_connection()

    # Test 3: Full workflow
    workflow_works = test_full_workflow(server_name, server_url)

    print(f"\n🎯 {server_name} VERIFICATION RESULTS")
    print("=" * 60)
    print(f"{server_name} tool calling:     {'✅ Working' if tool_calling_works else '❌ Not working'}")
    print(f"Code analyzer server:     {'✅ Reachable' if server_reachable else '❌ Not reachable'}")
    print(f"Complete workflow:        {'✅ Working' if workflow_works else '❌ Not working'}")

    if all([tool_calling_works, server_reachable, workflow_works]):
        print(f"\n🎉 {server_name} COMPLETE SUCCESS!")
        print(f"✅ {server_name} can successfully use tools for code analysis")
        print("✅ Remote Ollama tool calling is fully functional")
        print("✅ This bypasses all OpenWebUI limitations")
        return True
    else:
        print(f"\n⚠️ {server_name} PARTIAL SUCCESS")
        if tool_calling_works:
            print(f"✅ Basic tool calling works on {server_name}")
        if server_reachable:
            print("✅ Code analyzer server is accessible")
        if not workflow_works:
            print("❌ Full workflow needs debugging")
        return False

def main():
    print("🚀 Remote Ollama Tool Calling Verification")
    print("=" * 70)

    # Test servers
    servers = [
        ("lynn-pc", "http://192.168.0.32:11434"),
        ("T5810", "http://192.168.0.11:11434")
    ]

    results = {}

    for server_name, server_url in servers:
        results[server_name] = test_server(server_name, server_url)

    # Summary
    print(f"\n🎯 FINAL SUMMARY")
    print("=" * 70)

    for server_name, success in results.items():
        status = "✅ Working" if success else "❌ Issues"
        print(f"{server_name:15}: {status}")

    working_servers = [name for name, success in results.items() if success]

    if working_servers:
        print(f"\n🎉 SUCCESS: {len(working_servers)} server(s) working!")
        print(f"✅ Working servers: {', '.join(working_servers)}")
        print("✅ Remote Ollama tool calling is proven!")
    else:
        print(f"\n❌ No servers working properly")
        print("💡 Check llama3.1:latest installation and network connectivity")

if __name__ == "__main__":
    main()
