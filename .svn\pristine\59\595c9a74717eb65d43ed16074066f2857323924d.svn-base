# RAG Server Restart Instructions

## Issue
The enhanced search ChromaDB query syntax has been fixed in the code, but the Code Analysis server container needs to be restarted to pick up the changes.

## Fixed Code
The `_build_enhanced_filters` method in `main.py` has been updated to use proper ChromaDB syntax for multiple filters:

**Before (broken):**
```python
filters = {}
if filter_type:
    filters["type"] = {"$eq": filter_type}
if filter_language:
    filters["language"] = {"$eq": filter_language}
return filters if filters else None
```

**After (fixed):**
```python
conditions = []
if filter_type:
    conditions.append({"type": {"$eq": filter_type}})
if filter_language:
    conditions.append({"language": {"$eq": filter_language}})

if len(conditions) == 1:
    return conditions[0]
else:
    return {"$and": conditions}  # Proper ChromaDB syntax
```

## How to Apply the Fix

### Option 1: SSH to home-ai-server (Recommended)
```bash
# SSH to the server
ssh home-ai-server

# Navigate to the project directory
cd /path/to/code_analyzer_server

# Rebuild and restart the Code Analysis server container
docker-compose build code-analyzer-server
docker-compose up -d code-analyzer-server
```

### Option 2: Copy files and restart
```bash
# Copy the updated main.py to the server
scp main.py home-ai-server:/path/to/code_analyzer_server/

# SSH and restart
ssh home-ai-server "cd /path/to/code_analyzer_server && docker-compose restart code-analyzer-server"
```

### Option 3: Manual restart via Portainer
1. Open http://home-ai-server.local:9000 (Portainer)
2. Navigate to Containers
3. Find `code-analyzer-server`
4. Click "Restart"

## Verification
After restarting, run the test script:
```bash
python test_enhanced_search_fix.py
```

You should see:
```
✅ ENHANCED SEARCH IS WORKING!
🎉 ENHANCED SEARCH FIX IS WORKING!
💡 Multiple filters can now be used together
```

## What This Fixes
- **Multiple filter support**: Can now use `filter_type` AND `filter_language` together
- **Proper ChromaDB syntax**: Uses `{"$and": [conditions]}` for multiple filters
- **Enhanced search functionality**: The `/tools/enhanced_search` endpoint will work correctly

## Test Query
The following query should now work without errors:
```json
{
    "query": "memory allocation",
    "codebase_name": "utils", 
    "filter_language": "c",
    "filter_type": "function"
}
```
