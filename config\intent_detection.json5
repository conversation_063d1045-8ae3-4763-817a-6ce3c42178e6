{
  // Intent Detection Configuration for Code Analyzer Server
  // This file configures how queries are analyzed and routed
  
  "keywords": {
    // Core programming and software development terms
    "programming": [
      "function", "method", "class", "variable", "algorithm", "implementation",
      "code", "program", "software", "debug", "error", "bug", "compile",
      "memory", "pointer", "array", "loop", "condition", "return", "struct",
      "namespace", "template", "inheritance", "polymorphism", "encapsulation",
      "malloc", "free", "new", "delete", "import", "include", "header",
      "socket", "network", "tcp", "udp", "protocol", "buffer", "thread",
      "mutex", "semaphore", "async", "sync", "callback", "api", "library"
    ],
    
    // Technical configuration and system terms
    "technical": [
      "timer", "timers", "timeout", "configuration", "config", "settings",
      "setting", "parameter", "parameters", "option", "options",
      "handler", "handlers", "processor", "processing", "manager", "management"
    ],
    
    // Code analysis and metrics terms
    "analysis": [
      "complexity", "analysis", "analyze", "metrics", "performance",
      "profiling", "benchmark", "optimization", "refactor", "review",
      "quality", "maintainability", "cyclomatic", "cognitive"
    ],
    
    // Protocol and communication terms (especially for industrial/embedded systems)
    "protocol": [
      "register", "registers", "modbus", "holding", "coil", "discrete",
      "read", "write", "request", "response", "frame", "packet",
      "serial", "ethernet", "communication", "device", "slave", "master",
      "bacnet", "opcua", "mqtt", "canbus", "profinet", "fieldbus"
    ],
    
    // Programming language names
    "languages": [
      "python", "javascript", "typescript", "java", "csharp", "cpp", "c++",
      "rust", "go", "php", "ruby", "swift", "kotlin", "scala", "perl",
      "bash", "powershell", "sql", "html", "css", "xml", "json", "yaml"
    ],
    
    // Data structures and algorithms
    "data_structures": [
      "list", "array", "vector", "map", "dictionary", "hash", "tree",
      "graph", "queue", "stack", "heap", "linked", "binary", "search",
      "sort", "algorithm", "recursive", "iterative"
    ]
  },
  
  "patterns": {
    // Question patterns that indicate code-related queries
    "code_questions": [
      "how does", "how is", "how are", "show me", "find", "explain",
      "what is", "what are", "where is", "where are", "tell me about",
      "describe", "functions used", "methods used", "implementation of",
      "working of", "purpose of", "role of"
    ],
    
    // Management operation phrases
    "management": [
      "list codebase", "show codebase", "available codebase",
      "select codebase", "choose codebase", "switch codebase",
      "process codebase", "index codebase", "delete codebase",
      "get stats", "show stats", "codebase stats",
      "get statistics", "show statistics", "codebase statistics"
    ],
    
    // Help-related phrases
    "help": [
      "codebase analyzer help", "codebase_analyzer help", "analyzer help",
      "codebase help", "help with codebase", "help with analyzer",
      "help with code analysis", "codebase analyzer guide", "analyzer guide",
      "codebase analysis help", "how to analyze code", "codebase documentation",
      "analyzer documentation", "code search help", "code context help"
    ]
  },
  
  "routing_rules": {
    // Complexity analysis routing
    "complexity_analysis": {
      "triggers": [
        "analyze complexity", "code complexity analysis", "complexity analysis",
        "complexity metrics", "cyclomatic complexity", "cognitive complexity"
      ],
      "action": "route_to_complexity_analyzer",
      "extract_codebase": true,
      "priority": "high"
    },
    
    // Code context retrieval routing
    "code_context": {
      "triggers": ["code_related_detected"],
      "action": "route_to_context_retrieval", 
      "auto_inject": true,
      "priority": "medium"
    },
    
    // Management operations routing
    "management": {
      "triggers": ["management_phrase_detected"],
      "action": "route_to_management",
      "priority": "high"
    },
    
    // Help system routing
    "help": {
      "triggers": ["help_phrase_detected"],
      "action": "route_to_help",
      "priority": "high"
    }
  },
  
  "detection_settings": {
    // Minimum confidence threshold for intent detection
    "min_confidence": 0.6,
    
    // Enable fuzzy matching for keywords
    "fuzzy_matching": false,
    
    // Case sensitivity for pattern matching
    "case_sensitive": false,
    
    // Enable regex pattern matching
    "regex_patterns": true,
    
    // Maximum number of keywords to return in debug info
    "max_debug_keywords": 10
  },
  
  "regex_patterns": {
    // C-style function names (prefix_functionname)
    "c_style_functions": "\\b[a-z]+_[a-z_]+\\b",
    
    // UPPERCASE constants/macros
    "uppercase_constants": "\\b[A-Z][A-Z_]+[A-Z]\\b",
    
    // camelCase identifiers
    "camel_case": "\\b[a-z]+[A-Z][a-zA-Z]*\\b",
    
    // File extensions
    "file_extensions": "\\.[a-z]{1,4}\\b"
  }
}
