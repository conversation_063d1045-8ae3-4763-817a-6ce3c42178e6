#!/usr/bin/env python3
"""
Debug Code Analyzer Tool with lynn-pc Ollama
Comprehensive testing to identify issues with remote Ollama setup
"""

import requests
import json
import time
import socket

class LynnPCToolDebugger:
    def __init__(self):
        self.openwebui_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        self.code_analyzer_url = "http://************:5002"  # home-ai-server
        self.lynn_pc_ip = None  # Will detect automatically
        self.lynn_pc_ollama_url = None
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def detect_lynn_pc_ip(self):
        """Try to detect lynn-pc IP address"""
        print("🔍 Detecting lynn-pc IP address...")
        
        # Common IP ranges to try
        ip_ranges = [
            "192.168.0.{}",
            "192.168.1.{}",
            "10.0.0.{}"
        ]
        
        for ip_range in ip_ranges:
            for i in range(100, 200):  # Try common host IDs
                ip = ip_range.format(i)
                try:
                    # Quick port check
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex((ip, 11434))
                    sock.close()
                    
                    if result == 0:
                        # Verify it's actually Ollama
                        try:
                            response = requests.get(f"http://{ip}:11434/api/tags", timeout=2)
                            if response.status_code == 200:
                                print(f"✅ Found lynn-pc Ollama at: {ip}")
                                self.lynn_pc_ip = ip
                                self.lynn_pc_ollama_url = f"http://{ip}:11434"
                                return True
                        except:
                            continue
                except:
                    continue
        
        print("❌ Could not detect lynn-pc IP automatically")
        return False
    
    def test_lynn_pc_ollama(self):
        """Test lynn-pc Ollama connectivity and models"""
        print(f"\n🧪 Testing lynn-pc Ollama")
        print("-" * 40)
        
        if not self.lynn_pc_ollama_url:
            print("❌ lynn-pc Ollama URL not set")
            return False
        
        try:
            # Test basic connectivity
            response = requests.get(f"{self.lynn_pc_ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ lynn-pc Ollama accessible")
                print(f"   Available models: {len(models.get('models', []))}")
                
                for model in models.get('models', []):
                    name = model.get('name', 'unknown')
                    size = model.get('size', 0)
                    print(f"   • {name} ({size // (1024*1024*1024):.1f}GB)")
                
                return True
            else:
                print(f"❌ lynn-pc Ollama error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ lynn-pc Ollama connection failed: {e}")
            return False
    
    def test_code_analyzer_server(self):
        """Test code analyzer server on home-ai-server"""
        print(f"\n🌐 Testing Code Analyzer Server")
        print("-" * 40)
        
        try:
            response = requests.get(f"{self.code_analyzer_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Code analyzer server accessible")
                print(f"   Collections: {len(data.get('collections', []))}")
                print(f"   Total chunks: {data.get('total_chunks', 'unknown')}")
                
                # List collections
                collections = data.get('collections', [])
                if collections:
                    print(f"   Available codebases:")
                    for collection in collections:
                        print(f"   • {collection}")
                
                return True
            else:
                print(f"❌ Code analyzer server error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Code analyzer server connection failed: {e}")
            return False
    
    def get_openwebui_models(self):
        """Get list of models from OpenWebUI"""
        print(f"\n📋 Getting OpenWebUI Models")
        print("-" * 40)
        
        try:
            response = requests.get(f"{self.openwebui_url}/api/models", headers=self.headers, timeout=10)
            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get('data', [])
                
                print(f"✅ Found {len(models)} models in OpenWebUI")
                
                lynn_pc_models = []
                for model in models:
                    model_id = model.get('id', '')
                    if 'lynn-pc' in model_id.lower() or self.lynn_pc_ip in model_id:
                        lynn_pc_models.append(model_id)
                        print(f"   🎯 lynn-pc model: {model_id}")
                
                if not lynn_pc_models:
                    print(f"   ⚠️ No lynn-pc models found")
                    print(f"   💡 You may need to add lynn-pc Ollama connection in OpenWebUI")
                
                return lynn_pc_models
            else:
                print(f"❌ OpenWebUI models error: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ OpenWebUI models failed: {e}")
            return []
    
    def test_tool_with_model(self, model_name):
        """Test the code analyzer tool with a specific model"""
        print(f"\n🛠️ Testing Tool with Model: {model_name}")
        print("-" * 50)
        
        test_queries = [
            "list codebases",
            "select codebase library-management-system", 
            "search for memory functions"
        ]
        
        for query in test_queries:
            print(f"   Query: '{query}'")
            
            payload = {
                "model": model_name,
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False
            }
            
            try:
                start_time = time.time()
                response = requests.post(
                    f"{self.openwebui_url}/api/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                elapsed = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    message = data.get("choices", [{}])[0].get("message", {})
                    content = message.get("content", "")
                    tool_calls = message.get("tool_calls", [])
                    
                    # Analyze response
                    real_codebases = ["utils", "z80emu", "library-management-system", "test_project"]
                    found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
                    
                    generic_indicators = ["wordpress", "react", "popular", "open-source"]
                    found_generic = [gi for gi in generic_indicators if gi.lower() in content.lower()]
                    
                    if tool_calls:
                        print(f"      ✅ Tool called ({len(tool_calls)} calls) in {elapsed:.1f}s")
                    elif found_real:
                        print(f"      ✅ Tool working (found real codebases) in {elapsed:.1f}s")
                    elif found_generic:
                        print(f"      ❌ Generic response (tool not working) in {elapsed:.1f}s")
                    else:
                        print(f"      ❓ Unclear response in {elapsed:.1f}s")
                    
                    # Show preview
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"      📝 Preview: {preview}")
                    
                else:
                    print(f"      ❌ HTTP Error: {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ Exception: {e}")
            
            print()  # Empty line between queries
    
    def run_comprehensive_debug(self):
        """Run comprehensive debugging"""
        print("🚀 lynn-pc Code Analyzer Tool Debug")
        print("=" * 60)
        
        # Step 1: Detect lynn-pc
        if not self.detect_lynn_pc_ip():
            print("\n💡 Manual IP Detection:")
            print("1. Find lynn-pc IP: hostname -I (on lynn-pc)")
            print("2. Test Ollama: curl http://[IP]:11434/api/tags")
            print("3. Update this script with correct IP")
            return
        
        # Step 2: Test lynn-pc Ollama
        if not self.test_lynn_pc_ollama():
            print("\n🔧 lynn-pc Ollama Issues:")
            print("1. Ensure Ollama is running: systemctl status ollama")
            print("2. Check network binding: OLLAMA_HOST=0.0.0.0")
            print("3. Check firewall: sudo ufw allow 11434")
            return
        
        # Step 3: Test code analyzer server
        if not self.test_code_analyzer_server():
            print("\n🔧 Code Analyzer Server Issues:")
            print("1. Check server is running on home-ai-server")
            print("2. Verify port 5002 is accessible")
            print("3. Check docker-compose status")
            return
        
        # Step 4: Get OpenWebUI models
        lynn_pc_models = self.get_openwebui_models()
        
        if not lynn_pc_models:
            print(f"\n🔧 OpenWebUI Configuration:")
            print(f"1. Go to OpenWebUI → Admin → Settings → Connections")
            print(f"2. Add Ollama connection: {self.lynn_pc_ollama_url}")
            print(f"3. Name it 'lynn-pc' or similar")
            print(f"4. Refresh models list")
            return
        
        # Step 5: Test tool with lynn-pc models
        for model in lynn_pc_models:
            self.test_tool_with_model(model)
        
        # Summary
        print("\n📊 DEBUG SUMMARY")
        print("=" * 60)
        print("✅ lynn-pc Ollama: Working")
        print("✅ Code Analyzer Server: Working") 
        print("✅ OpenWebUI Models: Found lynn-pc models")
        print("🧪 Tool Testing: See results above")
        
        print(f"\n💡 Next Steps:")
        print("1. If tool not working: Check tool is enabled for lynn-pc models")
        print("2. If generic responses: Verify tool server URL in tool config")
        print("3. If network errors: Check connectivity between components")

def main():
    debugger = LynnPCToolDebugger()
    
    # Allow manual IP override
    import sys
    if len(sys.argv) > 1:
        debugger.lynn_pc_ip = sys.argv[1]
        debugger.lynn_pc_ollama_url = f"http://{sys.argv[1]}:11434"
        print(f"🔧 Using manual lynn-pc IP: {sys.argv[1]}")
    
    debugger.run_comprehensive_debug()

if __name__ == "__main__":
    main()
