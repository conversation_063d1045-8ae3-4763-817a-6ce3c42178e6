#!/usr/bin/env python3
"""
Server-Side Intent Detection Test Script

This script tests the server-side intent detection system independently
of the OpenWebUI tool. It directly calls the server endpoints to verify
intent detection is working correctly.
"""

import asyncio
import aiohttp
import json
import sys
from typing import List, Dict, Any
from datetime import datetime

class IntentDetectionTester:
    """Test server-side intent detection system"""
    
    def __init__(self, server_url: str = "http://localhost:5002"):
        self.server_url = server_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_server_health(self) -> bool:
        """Test if server is available"""
        try:
            async with self.session.get(f"{self.server_url}/health", timeout=5) as response:
                if response.status == 200:
                    print("✅ Server is healthy")
                    return True
                else:
                    print(f"⚠️ Server returned status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Server health check failed: {e}")
            return False
    
    async def get_intent_config(self) -> Dict[str, Any]:
        """Get server-side intent configuration"""
        try:
            async with self.session.get(f"{self.server_url}/tools/intent_config", timeout=10) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    print(f"❌ Failed to get config: {response.status}")
                    return {}
        except Exception as e:
            print(f"❌ Error getting config: {e}")
            return {}
    
    async def detect_intent(self, query: str) -> Dict[str, Any]:
        """Detect intent for a single query"""
        try:
            payload = {"query": query}
            async with self.session.post(
                f"{self.server_url}/tools/detect_intent", 
                json=payload, 
                timeout=10
            ) as response:
                return await response.json()
        except Exception as e:
            return {"error": str(e), "intent": "error", "confidence": 0.0}
    
    async def test_queries(self, test_queries: List[str]) -> List[Dict[str, Any]]:
        """Test multiple queries and return results"""
        results = []
        
        for query in test_queries:
            print(f"\n🔍 Testing: '{query[:60]}{'...' if len(query) > 60 else ''}'")
            
            result = await self.detect_intent(query)
            result["query"] = query
            results.append(result)
            
            # Print immediate result
            intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            suggested_action = result.get('suggested_action', 'unknown')
            
            print(f"   Intent: {intent} (confidence: {confidence:.2f})")
            print(f"   Action: {suggested_action}")
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
            
            # Show matched keywords/patterns
            matched_keywords = result.get('matched_keywords', [])
            matched_patterns = result.get('matched_patterns', [])
            
            if matched_keywords:
                print(f"   Keywords: {', '.join(matched_keywords[:3])}{'...' if len(matched_keywords) > 3 else ''}")
            
            if matched_patterns:
                print(f"   Patterns: {', '.join(matched_patterns[:2])}{'...' if len(matched_patterns) > 2 else ''}")
        
        return results

    def categorize_results(self, results: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Categorize results by query type for detailed analysis"""
        categories = {
            "Original Problem": [],
            "Specific Functions": [],
            "Memory Management": [],
            "Protocol Specific": [],
            "Multi-Language": [],
            "Complexity Analysis": [],
            "Management Operations": [],
            "Help Queries": [],
            "Edge Cases": [],
            "Error Conditions": []
        }

        for result in results:
            query = result.get('query', '').lower()

            if "modbus holding register" in query and "functions used to manage" in query:
                categories["Original Problem"].append(result)
            elif any(func in query for func in ['tmwmem_', 'mbmem_', 'mmbdata_', 'smbsesn_']):
                categories["Specific Functions"].append(result)
            elif any(term in query for term in ['memory', 'malloc', 'alloc', 'free']):
                categories["Memory Management"].append(result)
            elif any(proto in query for proto in ['modbus', 'bacnet', 'opcua', 'mqtt', 'canbus']):
                categories["Protocol Specific"].append(result)
            elif any(lang in query for lang in ['typescript', 'rust', 'z80', 'verilog', 'c#']):
                categories["Multi-Language"].append(result)
            elif 'complexity' in query:
                categories["Complexity Analysis"].append(result)
            elif any(mgmt in query for mgmt in ['list', 'select', 'stats', 'process', 'index']):
                categories["Management Operations"].append(result)
            elif any(help_term in query for help_term in ['help', 'documentation', 'guide']):
                categories["Help Queries"].append(result)
            elif query == '' or 'weather' in query or 'cake' in query or len(query) < 3:
                categories["Edge Cases"].append(result)
            elif 'error' in result:
                categories["Error Conditions"].append(result)

        return categories

    def print_summary(self, results: List[Dict[str, Any]]):
        """Print test summary"""
        print("\n" + "=" * 80)
        print("📊 **INTENT DETECTION TEST SUMMARY**")
        print("=" * 80)
        
        # Count intents
        intent_counts = {}
        error_count = 0
        total_confidence = 0
        
        for result in results:
            intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
            total_confidence += confidence
            
            if 'error' in result:
                error_count += 1
        
        print(f"**Total Queries:** {len(results)}")
        print(f"**Errors:** {error_count}")
        print(f"**Average Confidence:** {total_confidence / len(results):.2f}")

        # Calculate confidence distribution
        high_confidence = sum(1 for r in results if r.get('confidence', 0) >= 0.9)
        medium_confidence = sum(1 for r in results if 0.7 <= r.get('confidence', 0) < 0.9)
        low_confidence = sum(1 for r in results if 0.3 <= r.get('confidence', 0) < 0.7)
        very_low_confidence = sum(1 for r in results if r.get('confidence', 0) < 0.3)

        print("**Confidence Distribution:**")
        print(f"  • High (≥0.9): {high_confidence} queries ({high_confidence/len(results)*100:.1f}%)")
        print(f"  • Medium (0.7-0.9): {medium_confidence} queries ({medium_confidence/len(results)*100:.1f}%)")
        print(f"  • Low (0.3-0.7): {low_confidence} queries ({low_confidence/len(results)*100:.1f}%)")
        print(f"  • Very Low (<0.3): {very_low_confidence} queries ({very_low_confidence/len(results)*100:.1f}%)")

        print("\n**Intent Distribution:**")
        for intent, count in sorted(intent_counts.items()):
            percentage = (count / len(results)) * 100
            print(f"  • {intent}: {count} queries ({percentage:.1f}%)")
        
        # Show categorized analysis
        categories = self.categorize_results(results)
        print("\n**Query Category Analysis:**")
        for category, category_results in categories.items():
            if category_results:
                avg_confidence = sum(r.get('confidence', 0) for r in category_results) / len(category_results)
                print(f"  • {category}: {len(category_results)} queries (avg confidence: {avg_confidence:.2f})")

        print("\n**Sample Results by Category:**")
        for category, category_results in categories.items():
            if category_results:
                print(f"\n**{category}:**")
                # Show up to 3 examples from each category
                for i, result in enumerate(category_results[:3]):
                    query = result.get('query', 'unknown')
                    intent = result.get('intent', 'unknown')
                    confidence = result.get('confidence', 0.0)
                    action = result.get('suggested_action', 'unknown')

                    print(f"  {i+1}. '{query[:60]}{'...' if len(query) > 60 else ''}'")
                    print(f"     → {intent} (conf: {confidence:.2f}) → {action}")

                    if 'error' in result:
                        print(f"     ❌ Error: {result['error']}")

                if len(category_results) > 3:
                    print(f"     ... and {len(category_results) - 3} more queries in this category")

async def main():
    """Main test function"""
    print("🧪 **Server-Side Intent Detection Test**")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Comprehensive test queries covering extensive edge cases based on source_code analysis
    test_queries = [
        # === ORIGINAL PROBLEM CASE ===
        "tell me about a modbus holding register and the functions used to manage it",

        # === SPECIFIC FUNCTION QUERIES (from actual codebase) ===
        "what is tmwmem_alloc function",
        "show me tmwmem_free implementation",
        "how does mbmem_malloc work",
        "explain tmwdlist_addEntry function",
        "find tmwchnl_openChannel implementation",
        "what does tmwtimer_start do",
        "show me mmbdata_readHoldingRegs function",
        "how is smbsesn_processRequest implemented",
        "what is the purpose of tmwphys_openChannel",
        "explain tmwsctr_getNextCounter function",

        # === MEMORY MANAGEMENT QUERIES ===
        "show me memory management functions",
        "how does memory allocation work",
        "what are the memory allocation routines in utils",
        "explain memory deallocation in modbus",
        "how is memory handled in tmwmem.c",
        "show me all malloc and free functions",
        "what memory pools are used",
        "how does the memory allocator work",

        # === MODBUS PROTOCOL SPECIFIC ===
        "how does modbus communication work",
        "what are modbus holding registers",
        "explain modbus master implementation",
        "show me modbus slave functions",
        "how does modbus frame processing work",
        "what is modbus exception handling",
        "explain modbus data structures",
        "how are modbus sessions managed",
        "what is the modbus link layer",
        "show me modbus diagnostic functions",

        # === C/C++ SPECIFIC PATTERNS ===
        "what does TMW_CHNL_INFO struct contain",
        "show me TMWMEM_ALLOC_TYPE enum values",
        "explain TMWDEFS_BOOL usage",
        "what is TMWCNFG_MAX_CHANNELS defined as",
        "how is TMW_PHYS_CONTEXT used",
        "show me all #define macros in tmwcnfg.h",
        "what callback functions are defined",
        "explain the TMW_SESN structure",

        # === TYPESCRIPT/JAVASCRIPT QUERIES ===
        "how does the bookstore API work",
        "show me TypeScript interfaces in bookstore",
        "what Express routes are defined",
        "explain the user authentication system",
        "how does order processing work",
        "what is the database schema",
        "show me async/await patterns",
        "explain the middleware functions",

        # === C# .NET QUERIES ===
        "how does the Z80 emulator work",
        "what is the Z80 instruction set implementation",
        "show me C# classes in Essenbee.Z80",
        "explain the CPU registers implementation",
        "how does memory mapping work in Z80",
        "what are the Z80 opcodes",
        "show me the disassembler implementation",
        "how does the debugger interface work",

        # === RUST QUERIES ===
        "show me Rust modules in rust-starter",
        "how does Cargo.toml configuration work",
        "what are the Rust macros defined",
        "explain ownership patterns in Rust code",
        "show me trait implementations",
        "how does error handling work in Rust",

        # === GO QUERIES ===
        "show me Go packages in go-example",
        "how does the Go web server work",
        "what are the Go interfaces defined",
        "explain goroutine usage",
        "show me channel implementations",
        "how does Go error handling work",

        # === VERILOG/HARDWARE QUERIES ===
        "how does the VGA to SPI interface work",
        "show me Verilog modules",
        "explain the FPGA driver implementation",
        "what are the hardware signals defined",
        "how does the TFT interface work",

        # === COMPLEXITY ANALYSIS QUERIES ===
        "analyze complexity of utils codebase",
        "complexity analysis for modbus",
        "code complexity analysis",
        "cyclomatic complexity of tmwmem functions",
        "cognitive complexity analysis",
        "analyze code metrics for bookstore",
        "complexity analysis of Z80 emulator",
        "performance analysis of memory allocator",

        # === MANAGEMENT QUERIES ===
        "list codebases",
        "select codebase utils",
        "choose codebase modbus",
        "switch to bookstore codebase",
        "get stats",
        "show statistics",
        "process codebase",
        "index codebase z80emu",
        "delete codebase test_project",
        "available codebases",
        "codebase statistics",
        "show codebase status",

        # === HELP QUERIES ===
        "help with codebase analyzer",
        "analyzer documentation",
        "what can this tool do",
        "codebase analyzer help",
        "how to analyze code",
        "analyzer guide",
        "code search help",
        "documentation",
        "usage instructions",
        "help",

        # === PROTOCOL VARIATIONS ===
        "bacnet protocol implementation",
        "opcua server configuration",
        "mqtt message handling",
        "canbus frame processing",
        "profinet communication",
        "ethernet protocol stack",
        "serial communication handling",
        "tcp socket implementation",
        "udp packet processing",

        # === ALGORITHM AND DATA STRUCTURE QUERIES ===
        "explain the algorithm used in sorting",
        "how does binary search work",
        "what data structures are used",
        "show me linked list implementation",
        "explain hash table usage",
        "how does the tree structure work",
        "what sorting algorithms are implemented",
        "show me queue implementations",
        "explain stack operations",

        # === FILE AND DIRECTORY SPECIFIC ===
        "show me functions in tmwmem.c",
        "what's in mbmem.h header file",
        "explain tmwcnfg.h configuration",
        "show me bookstore.ts implementation",
        "what's in Program.cs file",
        "explain main.rs functionality",
        "show me socket_manager.cpp",
        "what's in buffer_pool.h",

        # === CROSS-LANGUAGE QUERIES ===
        "compare C and C++ implementations",
        "show me TypeScript vs JavaScript differences",
        "how does C# differ from C++",
        "compare Rust and Go implementations",
        "show me functional vs imperative patterns",

        # === ERROR HANDLING AND DEBUGGING ===
        "how is error handling implemented",
        "show me exception handling code",
        "what debugging functions are available",
        "explain error codes and messages",
        "how are failures handled",
        "show me logging implementations",
        "what diagnostic functions exist",

        # === PERFORMANCE AND OPTIMIZATION ===
        "how is performance optimized",
        "show me optimization techniques",
        "what are the bottlenecks",
        "explain memory optimization",
        "how is speed improved",
        "show me caching implementations",
        "what profiling is available",

        # === TESTING AND VALIDATION ===
        "show me unit tests",
        "how is testing implemented",
        "what test frameworks are used",
        "explain validation logic",
        "show me test cases",
        "how is code coverage measured",

        # === CONFIGURATION AND SETUP ===
        "how is configuration handled",
        "show me setup procedures",
        "what configuration files exist",
        "explain initialization process",
        "how are defaults set",
        "show me build configuration",

        # === EDGE CASES AND BOUNDARY CONDITIONS ===
        "",  # Empty query
        " ",  # Whitespace only
        "a",  # Single character
        "random unrelated question about weather and cooking recipes",
        "this is not a technical query at all just random words",
        "what is the meaning of life universe and everything",
        "how to bake a chocolate cake with vanilla frosting",

        # === VERY LONG QUERIES ===
        "a very long query that contains multiple technical terms like function method class variable algorithm implementation memory allocation pointer arithmetic data structure linked list binary tree hash table sorting algorithm quicksort mergesort heapsort but doesn't really make much sense as a coherent question about code analysis and just keeps going on and on with more technical terms like protocol modbus bacnet opcua mqtt canbus profinet ethernet tcp udp socket network communication buffer management thread synchronization mutex semaphore deadlock race condition",

        # === MIXED CASE AND FORMATTING ===
        "WHAT IS TMWMEM_ALLOC FUNCTION",
        "Show Me Memory Management Functions",
        "how does MODBUS communication work?",
        "explain the Z80 EMULATOR implementation!!!",
        "   show me functions with extra spaces   ",
        "show\tme\ttab\tseparated\tquery",
        "show me functions\nwith newlines\nin query",

        # === AMBIGUOUS QUERIES ===
        "show me the implementation",  # No specific function/feature mentioned
        "how does it work",  # No specific "it" referenced
        "explain the algorithm",  # No specific algorithm mentioned
        "what does this do",  # No specific "this" referenced
        "find the bug",  # No specific bug context
        "optimize the code",  # No specific code mentioned

        # === PARTIAL MATCHES AND TYPOS ===
        "tmwmem_aloc function",  # Missing 'l' in alloc
        "modbus holdng register",  # Missing 'i' in holding
        "memorry management",  # Extra 'r' in memory
        "algoritm implementation",  # Missing 'h' in algorithm
        "netwrok communication",  # Swapped 'o' and 'r'
        "functon definition",  # Missing 'i' in function

        # === CONTEXT-DEPENDENT QUERIES ===
        "show me the latest version",
        "what changed recently",
        "compare with previous implementation",
        "show me the differences",
        "what's new in this version",
        "explain the updates",

        # === MULTI-PART QUERIES ===
        "show me tmwmem_alloc and tmwmem_free functions",
        "explain modbus master and slave implementations",
        "compare C and TypeScript memory management",
        "show me both input and output functions",
        "explain read and write operations",
        "what are the pros and cons of this approach",
    ]
    
    server_url = "http://localhost:5002"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"🌐 Server URL: {server_url}")
    
    async with IntentDetectionTester(server_url) as tester:
        # Test server health
        if not await tester.test_server_health():
            print("❌ Server is not available. Please start the code analyzer server.")
            return
        
        # Get configuration info
        print("\n📋 Getting server configuration...")
        config = await tester.get_intent_config()
        if config:
            print(f"   Config Path: {config.get('config_path', 'unknown')}")
            print(f"   Config Exists: {'✅' if config.get('config_exists', False) else '❌'}")
            
            keyword_counts = config.get('keyword_categories', {})
            if keyword_counts:
                total_keywords = sum(keyword_counts.values())
                print(f"   Total Keywords: {total_keywords} across {len(keyword_counts)} categories")
        
        # Run tests
        print(f"\n🧪 Testing {len(test_queries)} comprehensive queries...")
        print("📊 Test categories include:")
        print("   • Original problem cases")
        print("   • Specific function queries from actual codebase")
        print("   • Memory management patterns")
        print("   • Multi-language support (C/C++, TypeScript, C#, Rust, Go, Verilog)")
        print("   • Protocol-specific queries (Modbus, BACnet, OPC-UA, etc.)")
        print("   • Complexity analysis variations")
        print("   • Management operations")
        print("   • Help and documentation")
        print("   • Edge cases and error conditions")
        print("   • Typos and malformed queries")
        print("   • Ambiguous and context-dependent queries")
        print()

        results = await tester.test_queries(test_queries)
        
        # Print summary
        tester.print_summary(results)
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("✅ Intent detection test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
