#!/usr/bin/env python3
"""
Server-Side Intent Detection Test Script

This script tests the server-side intent detection system independently
of the OpenWebUI tool. It directly calls the server endpoints to verify
intent detection is working correctly.
"""

import asyncio
import aiohttp
import json
import sys
from typing import List, Dict, Any
from datetime import datetime

class IntentDetectionTester:
    """Test server-side intent detection system"""
    
    def __init__(self, server_url: str = "http://localhost:5002"):
        self.server_url = server_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_server_health(self) -> bool:
        """Test if server is available"""
        try:
            async with self.session.get(f"{self.server_url}/health", timeout=5) as response:
                if response.status == 200:
                    print("✅ Server is healthy")
                    return True
                else:
                    print(f"⚠️ Server returned status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Server health check failed: {e}")
            return False
    
    async def get_intent_config(self) -> Dict[str, Any]:
        """Get server-side intent configuration"""
        try:
            async with self.session.get(f"{self.server_url}/tools/intent_config", timeout=10) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    print(f"❌ Failed to get config: {response.status}")
                    return {}
        except Exception as e:
            print(f"❌ Error getting config: {e}")
            return {}
    
    async def detect_intent(self, query: str) -> Dict[str, Any]:
        """Detect intent for a single query"""
        try:
            payload = {"query": query}
            async with self.session.post(
                f"{self.server_url}/tools/detect_intent", 
                json=payload, 
                timeout=10
            ) as response:
                return await response.json()
        except Exception as e:
            return {"error": str(e), "intent": "error", "confidence": 0.0}
    
    async def test_queries(self, test_queries: List[str]) -> List[Dict[str, Any]]:
        """Test multiple queries and return results"""
        results = []
        
        for query in test_queries:
            print(f"\n🔍 Testing: '{query[:60]}{'...' if len(query) > 60 else ''}'")
            
            result = await self.detect_intent(query)
            result["query"] = query
            results.append(result)
            
            # Print immediate result
            intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            suggested_action = result.get('suggested_action', 'unknown')
            
            print(f"   Intent: {intent} (confidence: {confidence:.2f})")
            print(f"   Action: {suggested_action}")
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
            
            # Show matched keywords/patterns
            matched_keywords = result.get('matched_keywords', [])
            matched_patterns = result.get('matched_patterns', [])
            
            if matched_keywords:
                print(f"   Keywords: {', '.join(matched_keywords[:3])}{'...' if len(matched_keywords) > 3 else ''}")
            
            if matched_patterns:
                print(f"   Patterns: {', '.join(matched_patterns[:2])}{'...' if len(matched_patterns) > 2 else ''}")
        
        return results
    
    def print_summary(self, results: List[Dict[str, Any]]):
        """Print test summary"""
        print("\n" + "=" * 80)
        print("📊 **INTENT DETECTION TEST SUMMARY**")
        print("=" * 80)
        
        # Count intents
        intent_counts = {}
        error_count = 0
        total_confidence = 0
        
        for result in results:
            intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
            total_confidence += confidence
            
            if 'error' in result:
                error_count += 1
        
        print(f"**Total Queries:** {len(results)}")
        print(f"**Errors:** {error_count}")
        print(f"**Average Confidence:** {total_confidence / len(results):.2f}")
        
        print("\n**Intent Distribution:**")
        for intent, count in sorted(intent_counts.items()):
            percentage = (count / len(results)) * 100
            print(f"  • {intent}: {count} queries ({percentage:.1f}%)")
        
        print("\n**Detailed Results:**")
        for i, result in enumerate(results, 1):
            query = result.get('query', 'unknown')
            intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            action = result.get('suggested_action', 'unknown')
            
            print(f"\n{i:2d}. Query: '{query[:50]}{'...' if len(query) > 50 else ''}'")
            print(f"    Intent: {intent} | Confidence: {confidence:.2f} | Action: {action}")
            
            if 'error' in result:
                print(f"    ❌ Error: {result['error']}")

async def main():
    """Main test function"""
    print("🧪 **Server-Side Intent Detection Test**")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Default test queries covering various scenarios
    test_queries = [
        # The original problematic query
        "tell me about a modbus holding register and the functions used to manage it",
        
        # Code analysis queries
        "what is tmwmem_alloc function",
        "show me memory management functions", 
        "how does memory allocation work",
        "explain the algorithm used in sorting",
        "find all functions that handle network communication",
        
        # Complexity analysis queries
        "analyze complexity of utils codebase",
        "complexity analysis for modbus",
        "code complexity analysis",
        
        # Management queries
        "list codebases",
        "select codebase utils",
        "get stats",
        "show statistics",
        "process codebase",
        
        # Help queries
        "help with codebase analyzer",
        "analyzer documentation",
        "what can this tool do",
        
        # Protocol-specific queries
        "how does modbus communication work",
        "bacnet protocol implementation",
        "opcua server configuration",
        
        # Edge cases
        "",  # Empty query
        "random unrelated question about weather",
        "a very long query that contains multiple technical terms like function method class variable algorithm implementation but doesn't really make much sense as a coherent question about code analysis",
    ]
    
    server_url = "http://localhost:5002"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"🌐 Server URL: {server_url}")
    
    async with IntentDetectionTester(server_url) as tester:
        # Test server health
        if not await tester.test_server_health():
            print("❌ Server is not available. Please start the code analyzer server.")
            return
        
        # Get configuration info
        print("\n📋 Getting server configuration...")
        config = await tester.get_intent_config()
        if config:
            print(f"   Config Path: {config.get('config_path', 'unknown')}")
            print(f"   Config Exists: {'✅' if config.get('config_exists', False) else '❌'}")
            
            keyword_counts = config.get('keyword_categories', {})
            if keyword_counts:
                total_keywords = sum(keyword_counts.values())
                print(f"   Total Keywords: {total_keywords} across {len(keyword_counts)} categories")
        
        # Run tests
        print(f"\n🧪 Testing {len(test_queries)} queries...")
        results = await tester.test_queries(test_queries)
        
        # Print summary
        tester.print_summary(results)
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("✅ Intent detection test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
