{".class": "MypyFile", "_fullname": "debug_lynn_pc_tool", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LynnPCToolDebugger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger", "name": "LynnPCToolDebugger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "debug_lynn_pc_tool", "mro": ["debug_lynn_pc_tool.LynnPCToolDebugger", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.__init__", "name": "__init__", "type": null}}, "api_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.api_key", "name": "api_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "code_analyzer_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.code_analyzer_url", "name": "code_analyzer_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "detect_lynn_pc_ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.detect_lynn_pc_ip", "name": "detect_lynn_pc_ip", "type": null}}, "get_openwebui_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.get_openwebui_models", "name": "get_openwebui_models", "type": null}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.headers", "name": "headers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lynn_pc_ip": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.lynn_pc_ip", "name": "lynn_pc_ip", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lynn_pc_ollama_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.lynn_pc_ollama_url", "name": "lynn_pc_ollama_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "openwebui_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.openwebui_url", "name": "openwebui_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_comprehensive_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.run_comprehensive_debug", "name": "run_comprehensive_debug", "type": null}}, "test_code_analyzer_server": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.test_code_analyzer_server", "name": "test_code_analyzer_server", "type": null}}, "test_lynn_pc_ollama": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.test_lynn_pc_ollama", "name": "test_lynn_pc_ollama", "type": null}}, "test_tool_with_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.test_tool_with_model", "name": "test_tool_with_model", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "debug_lynn_pc_tool.LynnPCToolDebugger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "debug_lynn_pc_tool.LynnPCToolDebugger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "debug_lynn_pc_tool.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "debug_lynn_pc_tool.main", "name": "main", "type": null}}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\debug_lynn_pc_tool.py"}