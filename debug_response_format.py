#!/usr/bin/env python3
"""
Debug the actual response format from the search endpoint
"""

import requests
import json

def test_search_response_format():
    """Test the actual response format from the search endpoint"""
    print("🔍 Testing Search Response Format")
    print("=" * 60)
    
    try:
        response = requests.post(
            "http://192.168.0.77:5002/search",
            json={
                "query": "coil",
                "codebase_name": "modbus",
                "n_results": 3
            },
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"Response type: {type(data)}")
            print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if isinstance(data, dict):
                print(f"\nDetailed response structure:")
                for key, value in data.items():
                    print(f"  {key}: {type(value)} - {len(value) if isinstance(value, (list, str)) else value}")
                
                # Check the results format
                if 'results' in data:
                    results = data['results']
                    print(f"\nResults format:")
                    print(f"  Type: {type(results)}")
                    print(f"  Length: {len(results)}")
                    
                    if results:
                        first_result = results[0]
                        print(f"  First result type: {type(first_result)}")
                        print(f"  First result keys: {list(first_result.keys()) if isinstance(first_result, dict) else 'Not a dict'}")
                        
                        if isinstance(first_result, dict):
                            content = first_result.get('content', '')
                            metadata = first_result.get('metadata', {})
                            print(f"  Content length: {len(content)}")
                            print(f"  Metadata: {metadata}")
                            print(f"  Content preview: {content[:200]}...")
            
            elif isinstance(data, list):
                print(f"\nResponse is a list with {len(data)} items")
                if data:
                    first_item = data[0]
                    print(f"First item type: {type(first_item)}")
                    print(f"First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not a dict'}")
            
            print(f"\nRaw response preview:")
            print(json.dumps(data, indent=2)[:500] + "..." if len(json.dumps(data)) > 500 else json.dumps(data, indent=2))
            
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def test_openwebui_tool_processing():
    """Test how the OpenWebUI tool would process this response"""
    print(f"\n🔧 Testing OpenWebUI Tool Processing")
    print("=" * 60)
    
    try:
        response = requests.post(
            "http://192.168.0.77:5002/search",
            json={
                "query": "coil",
                "codebase_name": "modbus",
                "n_results": 3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"OpenWebUI tool receives: {type(data)}")
            
            # Simulate the OpenWebUI tool logic
            if isinstance(data, list):
                print(f"✅ Tool expects list, got list")
                chunks = data
                chunk_count = len(chunks)
                print(f"   Chunks: {chunk_count}")
                
                if chunks:
                    print(f"   ✅ Would format {chunk_count} chunks")
                else:
                    print(f"   ❌ Would return 'No relevant code context found'")
                    
            elif isinstance(data, dict):
                print(f"❌ Tool expects list, got dict")
                print(f"   This is why it's failing!")
                
                # Check if we can extract results
                if 'results' in data:
                    results = data['results']
                    print(f"   💡 Could extract {len(results)} results from 'results' key")
                    
                    if results:
                        print(f"   ✅ These results contain real coil data")
                    else:
                        print(f"   ❌ Results list is empty")
                else:
                    print(f"   ❌ No 'results' key found")
            
        else:
            print(f"HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"Exception: {e}")

def main():
    print("🔍 Debugging Response Format Issue")
    print("=" * 70)
    print("Goal: Understand why OpenWebUI tool gets 'No relevant code context found'")
    
    # Test actual response format
    test_search_response_format()
    
    # Test how OpenWebUI tool processes it
    test_openwebui_tool_processing()
    
    print(f"\n💡 LIKELY SOLUTION:")
    print("The OpenWebUI tool expects a list but gets a dict with 'results' key.")
    print("Need to fix the response parsing in the OpenWebUI tool.")

if __name__ == "__main__":
    main()
