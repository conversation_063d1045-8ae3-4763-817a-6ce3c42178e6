# Nginx configuration for lynn-pc tool proxy
# Place this in /etc/nginx/sites-available/tool-proxy on lynn-pc

server {
    listen 5002;
    server_name localhost;
    
    # Logging
    access_log /var/log/nginx/tool-proxy-access.log;
    error_log /var/log/nginx/tool-proxy-error.log;
    
    # Proxy settings
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # Forward all requests to home-ai-server
    location / {
        proxy_pass http://************:5002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle CORS if needed
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # Health check endpoint
    location /proxy-health {
        return 200 '{"proxy":"lynn-pc","target":"************:5002","status":"running"}';
        add_header Content-Type application/json;
    }
}
