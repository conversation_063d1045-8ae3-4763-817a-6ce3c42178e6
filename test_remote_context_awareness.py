#!/usr/bin/env python3
"""
Test if explicit remote context instructions help LYNN-PC model use tools
"""

import requests
import json

def test_explicit_remote_context():
    """Test LYNN-PC model with explicit remote execution instructions"""
    print("🧪 Testing LYNN-PC with Explicit Remote Context")
    print("=" * 60)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test different levels of explicit context
    test_scenarios = [
        {
            "name": "Basic Tool Request",
            "prompt": "list codebases"
        },
        {
            "name": "Explicit Tool Instruction",
            "prompt": "Use the code_analyzer_tool to list codebases. Call the tool function directly."
        },
        {
            "name": "Remote Context Awareness",
            "prompt": """You are running on a remote machine (lynn-pc) but have access to tools that execute on a different server (home-ai-server at 192.168.0.77:5002). 
            
When you use the code_analyzer_tool, it will make network calls to the code analyzer server for you. You should use this tool to list the available codebases.

Please use the code_analyzer_tool now to list codebases."""
        },
        {
            "name": "Detailed Network Context",
            "prompt": """IMPORTANT CONTEXT:
- You (the model) are running on lynn-pc (192.168.0.32)
- The code analyzer tool will connect to home-ai-server (192.168.0.77:5002) 
- This is a remote network call that will work automatically
- You have access to a code_analyzer_tool that can list codebases
- The tool server is confirmed working and accessible

Please use the code_analyzer_tool to list the available codebases. The network connectivity is already established and working."""
        },
        {
            "name": "Function Call Directive",
            "prompt": """You must call the code_analyzer_tool function to list codebases. 

The tool execution will happen remotely on home-ai-server (192.168.0.77:5002) but this is handled automatically. Do not provide a generic response - you must actually call the tool function.

Call the list_codebases function now."""
        }
    ]
    
    results = {}
    
    for scenario in test_scenarios:
        print(f"\n🔍 Scenario: {scenario['name']}")
        print("-" * 40)
        print(f"Prompt: {scenario['prompt'][:100]}...")
        
        payload = {
            "model": "LYNN-PC.llama3:latest",
            "messages": [{"role": "user", "content": scenario["prompt"]}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=90
            )
            
            if response.status_code == 200:
                data = response.json()
                message = data.get("choices", [{}])[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                # Analyze response
                real_codebases = ["utils", "z80emu", "library-management-system", "test_project"]
                found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
                
                generic_indicators = ["i don't have", "i can't", "i'm not able", "generic", "popular"]
                found_generic = [gi for gi in generic_indicators if gi.lower() in content.lower()]
                
                tool_indicators = ["code_analyzer", "function", "tool", "call"]
                found_tool_mentions = [ti for ti in tool_indicators if ti.lower() in content.lower()]
                
                # Determine result
                if len(tool_calls) > 0:
                    result = "✅ TOOL CALLED"
                    success = True
                elif found_real:
                    result = "✅ REAL DATA (tool worked)"
                    success = True
                elif found_generic:
                    result = "❌ GENERIC RESPONSE"
                    success = False
                elif found_tool_mentions:
                    result = "⚠️ MENTIONS TOOLS (but doesn't call)"
                    success = False
                else:
                    result = "❓ UNCLEAR"
                    success = False
                
                print(f"Result: {result}")
                print(f"Tool calls: {len(tool_calls)}")
                print(f"Real codebases found: {found_real}")
                print(f"Content preview: {content[:150]}...")
                
                results[scenario["name"]] = {
                    "success": success,
                    "tool_calls": len(tool_calls),
                    "real_data": found_real,
                    "result": result,
                    "content_length": len(content)
                }
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                results[scenario["name"]] = {"success": False, "error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            print("❌ Timeout (might indicate tool processing)")
            results[scenario["name"]] = {"success": False, "error": "Timeout"}
        except Exception as e:
            print(f"❌ Exception: {e}")
            results[scenario["name"]] = {"success": False, "error": str(e)}
    
    # Summary
    print(f"\n📊 SUMMARY RESULTS")
    print("=" * 60)
    
    successful_scenarios = []
    for name, result in results.items():
        if result.get("success", False):
            successful_scenarios.append(name)
            print(f"✅ {name}: {result.get('result', 'Success')}")
        else:
            print(f"❌ {name}: {result.get('error', result.get('result', 'Failed'))}")
    
    if successful_scenarios:
        print(f"\n🎉 SUCCESS! These approaches worked:")
        for scenario in successful_scenarios:
            print(f"   • {scenario}")
        print(f"\n💡 This proves that explicit context instructions can help!")
    else:
        print(f"\n❌ No scenarios succeeded")
        print(f"💡 The issue may be deeper than context awareness")
    
    return results

def compare_with_local_model():
    """Quick comparison with local model for reference"""
    print(f"\n🔄 Quick Local Model Comparison")
    print("-" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3:latest",
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            real_codebases = ["utils", "z80emu", "library-management-system"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            print(f"Local model result:")
            print(f"   Tool calls: {len(tool_calls)}")
            print(f"   Real codebases: {found_real}")
            print(f"   Status: {'✅ Working' if len(tool_calls) > 0 or found_real else '❌ Not working'}")
            
    except Exception as e:
        print(f"Local model test failed: {e}")

def main():
    print("🚀 Remote Context Awareness Test")
    print("=" * 60)
    print("Testing if explicit remote context instructions help LYNN-PC model use tools")
    
    # Test remote context scenarios
    results = test_explicit_remote_context()
    
    # Compare with local model
    compare_with_local_model()
    
    print(f"\n🎯 CONCLUSION")
    print("=" * 60)
    if any(r.get("success", False) for r in results.values()):
        print("✅ Explicit context instructions CAN help remote models use tools!")
        print("💡 The issue was context awareness, not architecture limitation")
    else:
        print("❌ Context instructions didn't help")
        print("💡 The issue is likely architectural, not context-related")

if __name__ == "__main__":
    main()
