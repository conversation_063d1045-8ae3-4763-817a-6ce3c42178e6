#!/usr/bin/env python3
"""
Debug the response parsing issue in detail
"""

import requests
import json

def debug_actual_response_parsing():
    """Debug the actual response parsing step by step"""
    print("🔍 Debugging Response Parsing Step by Step")
    print("=" * 70)
    
    try:
        # Make the exact same request as the tool
        response = requests.post(
            "http://home-ai-server.local:5002/search",
            json={'query': 'coil', 'codebase_name': 'modbus', 'n_results': 5},
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        print(f"Content-Length: {response.headers.get('content-length', 'Unknown')}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"\n📊 Response Analysis:")
            print(f"Type: {type(data)}")
            print(f"Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            # Simulate the OpenWebUI tool parsing logic
            print(f"\n🔧 Simulating OpenWebUI Tool Parsing:")
            
            if isinstance(data, dict) and 'results' in data:
                print(f"✅ Condition 1: isinstance(data, dict) and 'results' in data = True")
                chunks = data['results']
                chunk_count = len(chunks)
                print(f"✅ Extracted chunks: {chunk_count} items")
                
                if chunks:
                    print(f"✅ Condition 2: chunks is not empty = True")
                    
                    # Check first chunk structure
                    first_chunk = chunks[0]
                    print(f"📄 First chunk type: {type(first_chunk)}")
                    print(f"📄 First chunk keys: {list(first_chunk.keys()) if isinstance(first_chunk, dict) else 'Not a dict'}")
                    
                    if isinstance(first_chunk, dict):
                        content = first_chunk.get("content", "")
                        metadata = first_chunk.get("metadata", {})
                        file_path = metadata.get("file_path", "unknown")
                        
                        print(f"📝 Content length: {len(content)}")
                        print(f"📁 File path: {file_path}")
                        print(f"📝 Content preview: {content[:200]}...")
                        
                        # This is what should be formatted
                        context_part = f"=== Code Section 1 ({file_path}) ===\n{content}"
                        print(f"📋 Formatted context length: {len(context_part)}")
                        
                        if len(context_part) > 100:
                            print(f"✅ Should return valid context")
                        else:
                            print(f"❌ Context too short")
                    else:
                        print(f"❌ First chunk is not a dict")
                else:
                    print(f"❌ Condition 2: chunks is empty")
                    print(f"   This would cause 'No relevant code context found'")
                    
            elif isinstance(data, list):
                print(f"⚠️ Condition 1: Got list instead of dict")
                chunks = data
                chunk_count = len(chunks)
                print(f"   Chunks: {chunk_count}")
            else:
                print(f"❌ Condition 1: Unexpected data type or missing 'results' key")
                print(f"   Data type: {type(data)}")
                if isinstance(data, dict):
                    print(f"   Available keys: {list(data.keys())}")
                    
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_openwebui_tool_url():
    """Test if the OpenWebUI tool is using the correct URL"""
    print(f"\n🔧 Testing OpenWebUI Tool URL Configuration")
    print("=" * 70)
    
    # The tool uses home-ai-server.local:5002, let's test both URLs
    urls_to_test = [
        "http://home-ai-server.local:5002/search",
        "http://192.168.0.77:5002/search"
    ]
    
    for url in urls_to_test:
        print(f"\n📤 Testing: {url}")
        
        try:
            response = requests.post(
                url,
                json={'query': 'coil', 'codebase_name': 'modbus', 'n_results': 3},
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    results = data['results']
                    print(f"   ✅ Found {len(results)} results")
                else:
                    print(f"   ❌ Unexpected response format")
            else:
                print(f"   ❌ HTTP Error: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def main():
    print("🔍 Debugging Response Parsing Issue")
    print("=" * 80)
    print("Goal: Understand why parsing still fails despite getting data")
    
    # Debug the actual parsing
    debug_actual_response_parsing()
    
    # Test URL differences
    test_openwebui_tool_url()

if __name__ == "__main__":
    main()
