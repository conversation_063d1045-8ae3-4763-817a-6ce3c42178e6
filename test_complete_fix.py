#!/usr/bin/env python3
"""
Test the complete fix for T5810 hallucination
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_complete_fix():
    """Test the complete fix for the string/int comparison error"""
    print("🔧 Testing Complete Fix for T5810 Hallucination")
    print("=" * 70)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test the exact query that was causing hallucination
    query = "show timer management in utils codebase"
    
    print(f"📤 Query: {query}")
    print("-" * 50)
    
    try:
        result = analyzer.chat_with_tools(query)
        
        print(f"📝 Result length: {len(result)}")
        print(f"📊 Result preview:")
        print(result[:600] + "..." if len(result) > 600 else result)
        
        # Check for the specific error message
        if "problem with comparing strings and integers" in result:
            print(f"❌ STILL GETTING COMPARISON ERROR")
            return False
        
        # Check for hallucination vs real code
        hallucination_indicators = [
            "import time",
            "class Timer:",
            "def start(self):",
            "Example usage:",
            "Here's an example",
            "This code defines"
        ]
        
        real_code_indicators = [
            "tmwtimer",
            "Triangle MicroWorks",
            "/app/source_code/utils",
            "_restartSystemTimer",
            "TMWTIMER",
            "pChannel"
        ]
        
        found_hallucination = [ind for ind in hallucination_indicators if ind in result]
        found_real_code = [ind for ind in real_code_indicators if ind in result]
        
        print(f"\n🔍 Analysis:")
        print(f"Hallucination indicators: {found_hallucination}")
        print(f"Real code indicators: {found_real_code}")
        
        if found_real_code and not found_hallucination:
            print(f"✅ SUCCESS: Shows real timer code!")
            return True
        elif not found_hallucination:
            print(f"⚪ NEUTRAL: No hallucination but no clear real code either")
            return True
        else:
            print(f"❌ FAILURE: Still hallucinating")
            return False
            
    except Exception as e:
        print(f"❌ Exception during test: {e}")
        return False

def test_tool_execution_with_string_args():
    """Test tool execution with string arguments to ensure no errors"""
    print(f"\n🔧 Testing Tool Execution with String Arguments")
    print("=" * 70)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test with string max_results (like T5810 sends)
    arguments = {
        'codebase_name': 'utils',
        'query': 'timer management',
        'max_results': '5'  # String instead of int
    }
    
    print(f"📤 Testing with string max_results: {arguments}")
    
    try:
        result = analyzer.execute_tool('query_codebase', arguments)
        
        print(f"✅ Tool execution successful")
        print(f"📊 Result type: {type(result)}")
        
        if 'results' in result:
            results = result['results']
            print(f"📝 Found {len(results)} results")
            
            if results:
                first_content = results[0].get('content', '')[:200]
                print(f"📄 First result: {first_content}...")
                return True
            else:
                print(f"⚠️ Empty results")
                return False
        else:
            print(f"❌ No results in response: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Tool execution failed: {e}")
        return False

def test_multiple_queries_final():
    """Final test of multiple queries"""
    print(f"\n🧪 Final Test: Multiple Show Queries")
    print("=" * 70)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    queries = [
        "show timer management in utils codebase",
        "show memory management in utils codebase",
        "show network functions in utils codebase"
    ]
    
    results = []
    
    for i, query in enumerate(queries, 1):
        print(f"\n[{i}] Query: {query}")
        print("-" * 40)
        
        try:
            result = analyzer.chat_with_tools(query)
            
            # Check for comparison error
            if "problem with comparing strings and integers" in result:
                print(f"❌ COMPARISON ERROR")
                results.append(False)
                continue
            
            # Check for hallucination
            if any(term in result for term in ["import ", "class ", "def ", "Example usage:"]):
                print(f"❌ HALLUCINATING")
                results.append(False)
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code", "tmw"]):
                print(f"✅ REAL CODE")
                results.append(True)
            else:
                print(f"⚪ UNCLEAR")
                results.append(None)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append(False)
    
    success_count = sum(1 for r in results if r is True)
    total_count = len(results)
    success_rate = success_count / total_count
    
    print(f"\n📊 Final Results: {success_count}/{total_count} successful ({success_rate:.1%})")
    
    return success_rate >= 0.8  # 80% success rate

def main():
    print("🔧 Testing Complete Fix for T5810 Hallucination")
    print("=" * 80)
    print("Goal: Fix the string/int comparison error and stop hallucination")
    
    # Test the main fix
    main_fix_works = test_complete_fix()
    
    # Test tool execution with string args
    tool_works = test_tool_execution_with_string_args()
    
    # Test multiple queries
    multiple_works = test_multiple_queries_final()
    
    print(f"\n🎯 FINAL VERDICT")
    print("=" * 80)
    print(f"Main fix works:       {'✅ YES' if main_fix_works else '❌ NO'}")
    print(f"Tool execution works: {'✅ YES' if tool_works else '❌ NO'}")
    print(f"Multiple queries:     {'✅ YES' if multiple_works else '❌ NO'}")
    
    if main_fix_works and tool_works and multiple_works:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"✅ String/int comparison error fixed")
        print(f"✅ T5810 no longer hallucinates on 'show' queries")
        print(f"✅ Shows real timer management code from utils codebase")
        print(f"✅ High success rate across multiple query types")
        
        print(f"\n💡 The fix involved:")
        print(f"1. Converting string max_results to int: int(arguments.get('max_results', 10))")
        print(f"2. Ensuring max_results is always >= 1")
        print(f"3. Proper argument parsing for both dict and string formats")
    else:
        print(f"\n❌ STILL ISSUES REMAINING")
        print(f"Need further investigation and fixes")

if __name__ == "__main__":
    main()
