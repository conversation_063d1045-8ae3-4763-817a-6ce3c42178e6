#!/usr/bin/env python3
"""
Debug why the coil query returns "No relevant code context found"
"""

import requests

def test_coil_search_directly():
    """Test searching for coil in modbus codebase directly"""
    print("🔍 Testing Coil Search in Modbus Codebase")
    print("=" * 60)
    
    base_url = "http://192.168.0.77:5002"
    
    # Test different search approaches
    search_tests = [
        {"query": "coil", "codebase_name": "modbus", "n_results": 10},
        {"query": "coil modbus", "codebase_name": "modbus", "n_results": 10},
        {"query": "COIL", "codebase_name": "modbus", "n_results": 10},
        {"query": "discrete input", "codebase_name": "modbus", "n_results": 10},
        {"query": "holding register", "codebase_name": "modbus", "n_results": 10}
    ]
    
    for i, search_params in enumerate(search_tests, 1):
        print(f"\n{i}️⃣ Testing search: {search_params['query']}")
        
        try:
            response = requests.post(f"{base_url}/search", json=search_params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"   ✅ Found {len(results)} results")
                
                if results:
                    # Show first result
                    first_result = results[0]
                    content = first_result.get('content', '')
                    metadata = first_result.get('metadata', {})
                    file_path = metadata.get('file_path', 'Unknown')
                    
                    print(f"   📄 First result: {file_path}")
                    print(f"   📝 Content preview: {content[:200]}...")
                    
                    # Check if it actually contains coil-related content
                    coil_terms = ['coil', 'discrete', 'input', 'output', 'register']
                    found_terms = [term for term in coil_terms if term.lower() in content.lower()]
                    print(f"   🎯 Found terms: {found_terms}")
                else:
                    print(f"   ❌ No results found")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_modbus_codebase_exists():
    """Test if modbus codebase actually exists"""
    print(f"\n🔍 Testing if Modbus Codebase Exists")
    print("=" * 60)
    
    base_url = "http://192.168.0.77:5002"
    
    try:
        response = requests.get(f"{base_url}/codebases", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            codebases = data.get('codebases', [])
            
            print(f"Available codebases: {len(codebases)}")
            
            codebase_names = [cb.get('name', 'Unknown') for cb in codebases]
            print(f"Codebase names: {codebase_names}")
            
            if 'modbus' in codebase_names:
                print(f"✅ Modbus codebase exists")
                
                # Get modbus codebase details
                modbus_cb = next((cb for cb in codebases if cb.get('name') == 'modbus'), None)
                if modbus_cb:
                    print(f"📊 Modbus codebase details:")
                    for key, value in modbus_cb.items():
                        print(f"   {key}: {value}")
                        
                return True
            else:
                print(f"❌ Modbus codebase not found")
                print(f"Available: {codebase_names}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_openwebui_tool_query():
    """Test what the OpenWebUI tool actually does with the coil query"""
    print(f"\n🔍 Testing OpenWebUI Tool Query Processing")
    print("=" * 60)
    
    # Simulate what the OpenWebUI tool does
    query = "what is a coil in the modbus codebase"
    
    print(f"📤 Original query: {query}")
    
    # Check if it's detected as a code query
    code_indicators = ['codebase', 'function', 'class', 'method', 'code', 'implementation']
    is_code_query = any(indicator in query.lower() for indicator in code_indicators)
    
    print(f"🔍 Detected as code query: {is_code_query}")
    
    if is_code_query:
        # Test the search that would be performed
        try:
            response = requests.post(
                "http://192.168.0.77:5002/search",
                json={
                    "query": "coil",
                    "codebase_name": "modbus", 
                    "n_results": 10
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                if results:
                    print(f"✅ Search would return {len(results)} results")
                    return True
                else:
                    print(f"❌ Search returns no results - this causes 'No relevant code context found'")
                    return False
            else:
                print(f"❌ Search fails with HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Search exception: {e}")
            return False
    else:
        print(f"⚠️ Not detected as code query - would return empty")
        return False

def main():
    print("🔍 Debugging 'No relevant code context found' Issue")
    print("=" * 70)
    print("Query: 'what is a coil in the modbus codebase'")
    
    # Test if modbus codebase exists
    modbus_exists = test_modbus_codebase_exists()
    
    if modbus_exists:
        # Test coil searches
        test_coil_search_directly()
        
        # Test OpenWebUI tool processing
        tool_works = test_openwebui_tool_query()
        
        print(f"\n🎯 DIAGNOSIS")
        print("=" * 70)
        
        if tool_works:
            print(f"✅ The search should work - issue may be elsewhere")
        else:
            print(f"❌ The search returns no results for 'coil' in modbus codebase")
            print(f"💡 SOLUTIONS:")
            print(f"1. Check if modbus codebase contains coil-related code")
            print(f"2. Try different search terms: 'discrete input', 'holding register'")
            print(f"3. Re-index the modbus codebase if it's incomplete")
    else:
        print(f"\n❌ ROOT CAUSE: Modbus codebase doesn't exist or isn't indexed")
        print(f"💡 SOLUTION: Index the modbus codebase first")

if __name__ == "__main__":
    main()
