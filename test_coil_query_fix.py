#!/usr/bin/env python3
"""
Test the fix for the coil query issue
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def test_coil_query_fix():
    """Test the coil query with the fixed response parsing"""
    print("🔧 Testing Coil Query Fix")
    print("=" * 60)
    
    # Create the tool instance
    tool = Tools()
    
    # Set the modbus codebase
    tool.valves.current_codebase = "modbus"
    
    # Test the problematic query
    query = "what is a coil in the modbus codebase"
    
    print(f"📤 Query: {query}")
    print(f"🎯 Target codebase: {tool.valves.current_codebase}")
    print("-" * 50)
    
    try:
        # Test the get_code_context method directly
        context = await tool.get_code_context(
            query="coil",
            codebase_name="modbus",
            n_results=5
        )
        
        print(f"📝 Context length: {len(context) if context else 0}")
        
        if context and "No relevant code context found" not in context:
            print(f"✅ SUCCESS: Context retrieved successfully!")
            print(f"📊 Context preview:")
            print(context[:400] + "..." if len(context) > 400 else context)
            
            # Check if it contains coil-related content
            coil_terms = ['coil', 'discrete', 'modbus', '_processReadCoils']
            found_terms = [term for term in coil_terms if term.lower() in context.lower()]
            print(f"🎯 Found coil-related terms: {found_terms}")
            
            return True
        else:
            print(f"❌ FAILURE: Still getting 'No relevant code context found'")
            print(f"📝 Context: {context}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def test_full_query_processing():
    """Test the full query processing pipeline"""
    print(f"\n🚀 Testing Full Query Processing")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    query = "what is a coil in the modbus codebase"
    
    print(f"📤 Full query: {query}")
    
    try:
        # Test the full __call__ method
        result = await tool.__call__(query)
        
        print(f"📝 Result length: {len(result) if result else 0}")
        
        if result and "No relevant code context found" not in result:
            print(f"✅ SUCCESS: Full processing works!")
            print(f"📊 Result preview:")
            print(result[:500] + "..." if len(result) > 500 else result)
            
            # Check if it contains actual coil information
            if any(term in result.lower() for term in ['coil', 'discrete', 'modbus', 'register']):
                print(f"🎯 Contains coil-related information")
                return True
            else:
                print(f"⚠️ May not contain coil-specific information")
                return True
        else:
            print(f"❌ FAILURE: Full processing still fails")
            print(f"📝 Result: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def test_multiple_modbus_queries():
    """Test multiple modbus-related queries"""
    print(f"\n🧪 Testing Multiple Modbus Queries")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    queries = [
        "what is a coil in the modbus codebase",
        "show discrete inputs in modbus",
        "find holding registers in modbus codebase",
        "explain modbus function codes"
    ]
    
    results = []
    
    for i, query in enumerate(queries, 1):
        print(f"\n[{i}] Query: {query}")
        print("-" * 40)
        
        try:
            context = await tool.get_code_context(
                query=query.split()[-3] if "in" in query else query,  # Extract key term
                codebase_name="modbus",
                n_results=3
            )
            
            if context and "No relevant code context found" not in context:
                print(f"✅ SUCCESS: Found context")
                results.append(True)
            else:
                print(f"❌ FAILURE: No context found")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results)
    print(f"\n📊 Success rate: {success_rate:.1%} ({sum(results)}/{len(results)})")
    
    return success_rate >= 0.75

async def main():
    print("🔧 Testing Fix for 'No relevant code context found' Issue")
    print("=" * 80)
    
    # Test the context retrieval fix
    context_works = await test_coil_query_fix()
    
    # Test full query processing
    full_works = await test_full_query_processing()
    
    # Test multiple queries
    multiple_works = await test_multiple_modbus_queries()
    
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 80)
    print(f"Context retrieval:  {'✅ FIXED' if context_works else '❌ STILL BROKEN'}")
    print(f"Full processing:    {'✅ FIXED' if full_works else '❌ STILL BROKEN'}")
    print(f"Multiple queries:   {'✅ FIXED' if multiple_works else '❌ STILL BROKEN'}")
    
    if context_works and full_works:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ The 'No relevant code context found' issue is FIXED")
        print(f"✅ Coil queries now return real modbus code context")
        print(f"✅ OpenWebUI tool correctly parses search response format")
        
        print(f"\n💡 The fix involved:")
        print(f"• Updated response parsing to handle dict format with 'results' key")
        print(f"• Changed from expecting list directly to extracting from dict['results']")
        print(f"• Maintained backward compatibility with list format")
    else:
        print(f"\n❌ STILL ISSUES")
        print(f"The fix didn't completely resolve the problem")

if __name__ == "__main__":
    asyncio.run(main())
