#!/usr/bin/env python3
"""
Test the new auto-codebase detection feature
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def test_auto_codebase_detection():
    """Test the auto-codebase detection feature"""
    print("🎯 Testing Auto-Codebase Detection")
    print("=" * 60)
    
    tool = Tools()
    
    # Set current codebase to utils (different from what we'll detect)
    tool.valves.current_codebase = "utils"
    print(f"📊 Current codebase: {tool.valves.current_codebase}")
    
    # Test queries that should auto-detect different codebases
    test_cases = [
        {
            "query": "tell me about a modbus holding register and the functions used to manage it",
            "expected": "modbus",
            "description": "Should detect 'modbus' from the query"
        },
        {
            "query": "show me z80emu opcodes and instruction handling",
            "expected": "z80emu", 
            "description": "Should detect 'z80emu' from the query"
        },
        {
            "query": "find memory management in utils codebase",
            "expected": "utils",
            "description": "Should detect 'utils' from the query"
        },
        {
            "query": "explain networking_project socket implementation",
            "expected": "networking_project",
            "description": "Should detect hyphenated 'networking_project'"
        },
        {
            "query": "what are the timer functions in this codebase",
            "expected": "utils",
            "description": "Should fall back to current codebase (no detection)"
        },
        {
            "query": "show me test_project unit tests",
            "expected": "test_project",
            "description": "Should detect 'test_project' with underscore"
        }
    ]
    
    print(f"\n🔍 Testing Detection Logic:")
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"\n[{i}] {description}")
        print(f"    Query: {query}")
        
        # Test the detection method directly
        detected = tool._detect_codebase_in_query(query)
        print(f"    Detected: {detected}")
        print(f"    Expected: {expected}")
        
        if detected == expected:
            print(f"    ✅ CORRECT")
        elif detected is None and expected == "utils":
            print(f"    ✅ CORRECT (fallback to current)")
        else:
            print(f"    ❌ WRONG")

async def test_full_context_retrieval():
    """Test that auto-detection works in full context retrieval"""
    print(f"\n🚀 Testing Full Context Retrieval with Auto-Detection")
    print("=" * 60)
    
    tool = Tools()
    
    # Set current codebase to utils
    tool.valves.current_codebase = "utils"
    print(f"📊 Starting codebase: {tool.valves.current_codebase}")
    
    # Test the modbus query that should auto-switch to modbus
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"\n📤 Query: {query}")
    
    try:
        # This should auto-detect modbus and use it instead of utils
        context = await tool.get_code_context(
            query=query,
            n_results=3
        )
        
        print(f"📝 Context length: {len(context) if context else 0}")
        
        if context and "No relevant code context found" not in context:
            print(f"✅ SUCCESS: Got context")
            
            # Check if it contains modbus-specific content
            if any(term in context.lower() for term in ['modbus', 'holding register', 'triangle microworks']):
                print(f"🎯 Contains modbus-specific content")
                
                # Check the file paths to confirm it's from modbus codebase
                if '/app/source_code/modbus/' in context:
                    print(f"✅ CONFIRMED: Using modbus codebase files")
                else:
                    print(f"⚠️ May not be using modbus files")
            else:
                print(f"⚠️ May not contain modbus content")
                
            print(f"📊 Context preview:")
            print(context[:300] + "..." if len(context) > 300 else context)
            
        else:
            print(f"❌ FAILURE: No context retrieved")
            print(f"Context: {context}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

async def main():
    print("🎯 Testing Auto-Codebase Detection Feature")
    print("=" * 70)
    
    # Test the detection logic
    await test_auto_codebase_detection()
    
    # Test full integration
    await test_full_context_retrieval()
    
    print(f"\n💡 FEATURE SUMMARY:")
    print("✅ Auto-detects codebase names mentioned in queries")
    print("✅ Handles hyphenated names like 'networking_project'")
    print("✅ Falls back to current codebase if no detection")
    print("✅ Integrates with both get_code_context and smart_code_context")
    
    print(f"\n🎯 EXPECTED BEHAVIOR:")
    print("'tell me about modbus holding register' -> auto-switches to modbus codebase")
    print("'show z80emu opcodes' -> auto-switches to z80emu codebase")
    print("'find memory functions' -> uses current codebase")

if __name__ == "__main__":
    asyncio.run(main())
