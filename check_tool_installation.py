#!/usr/bin/env python3
"""
Check if the Code Analyzer tool is properly installed in OpenWebUI
"""

import requests
import json

def check_tool_installation():
    """Check if the tool is installed and configured correctly"""
    print("🔍 Checking Code Analyzer Tool Installation")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Check if we can access OpenWebUI API
    print("\n1️⃣ Testing OpenWebUI API Access...")
    try:
        # Try a simple API call that should work
        response = requests.get(f"{base_url}/api/models", headers=headers, timeout=10)
        if response.status_code == 200:
            print("   ✅ OpenWebUI API accessible")
            models = response.json()
            print(f"   📋 Found {len(models.get('data', []))} models")
        else:
            print(f"   ❌ API access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Cannot connect to OpenWebUI: {e}")
        return False
    
    # Test 2: Try to make a simple chat request WITHOUT tools
    print("\n2️⃣ Testing Basic Chat (No Tools)...")
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "Hello, can you respond?"}],
            "stream": False
        }
        
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"   ✅ Basic chat works: {content[:50]}...")
        else:
            print(f"   ❌ Basic chat failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Basic chat error: {e}")
        return False
    
    # Test 3: Try to make a chat request WITH the tool
    print("\n3️⃣ Testing Chat WITH Code Analyzer Tool...")
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "list codebases"}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False
        }
        
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"   Content length: {len(content)}")
            print(f"   Tool calls: {len(tool_calls)}")
            
            if tool_calls:
                print("   ✅ TOOL WAS CALLED!")
                for i, tc in enumerate(tool_calls):
                    func_name = tc.get("function", {}).get("name", "unknown")
                    print(f"      Tool {i+1}: {func_name}")
                return True
            else:
                print("   ❌ TOOL NOT CALLED")
                print(f"   Response: {content[:200]}...")
                
                # Check if the response suggests the tool doesn't exist
                if "tool" in content.lower() or "function" in content.lower():
                    print("   💡 Model mentioned tools - might be a configuration issue")
                else:
                    print("   💡 Model gave normal response - tool likely not installed")
                
                return False
        
        elif response.status_code == 400:
            print("   ❌ BAD REQUEST - Tool might not exist")
            try:
                error = response.json()
                print(f"   Error details: {error}")
            except:
                print(f"   Error text: {response.text[:200]}")
            return False
        
        else:
            print(f"   ❌ HTTP ERROR: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Tool test error: {e}")
        return False

def main():
    success = check_tool_installation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TOOL IS WORKING!")
        print("The issue might be model-specific configuration.")
    else:
        print("❌ TOOL IS NOT WORKING!")
        print("\n🔧 LIKELY CAUSES:")
        print("1. Tool not installed in OpenWebUI")
        print("2. Tool not enabled for the model")
        print("3. Tool has errors in its code")
        print("4. Tool ID mismatch")
        
        print("\n💡 NEXT STEPS:")
        print("1. Check OpenWebUI → Workspace → Tools")
        print("2. Verify 'Code Analyzer Tool' is installed")
        print("3. Check if it's enabled for llama3:latest")
        print("4. Look for error messages in the tool")

if __name__ == "__main__":
    main()
