{"data_mtime": 1752885880, "dep_lines": [6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30], "dependencies": ["direct_ollama_code_analyzer", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "61496c5767d4cf07b674ad88fa0f85ef1f7caba6", "id": "test_explicit_t5810_prompt", "ignore_all": false, "interface_hash": "cdf64ea97fd466bea89e0115ab7970b4516ea81b", "mtime": 1752885879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_explicit_t5810_prompt.py", "plugin_data": null, "size": 4718, "suppressed": [], "version_id": "1.15.0"}