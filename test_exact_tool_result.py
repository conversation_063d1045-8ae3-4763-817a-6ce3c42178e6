#!/usr/bin/env python3
"""
Test the exact tool result that T5810 is receiving
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
import json

def test_exact_tool_result():
    """Test exactly what T5810 is receiving from the tool"""
    print("🔍 Testing Exact Tool Result")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Execute the exact same tool call that T5810 would make
    tool_result = analyzer.execute_tool('query_codebase', {
        'codebase_name': 'utils',
        'query': 'malloc free alloc memory',
        'max_results': 10
    })
    
    print(f"Tool result type: {type(tool_result)}")
    print(f"Tool result keys: {tool_result.keys() if isinstance(tool_result, dict) else 'Not a dict'}")
    
    # Show the exact JSON that would be sent to T5810
    tool_result_json = json.dumps(tool_result)
    print(f"\nExact JSON sent to T5810:")
    print(f"Length: {len(tool_result_json)} characters")
    print(f"First 500 chars: {tool_result_json[:500]}...")
    
    # Check if there are error messages in the result
    if 'error' in tool_result:
        print(f"\n❌ ERROR FOUND IN TOOL RESULT:")
        print(f"Error: {tool_result['error']}")
        return False
    
    # Check if there are actual results
    if 'results' in tool_result and tool_result['results']:
        results = tool_result['results']
        print(f"\n✅ Found {len(results)} results")
        
        # Show first result in detail
        first_result = results[0]
        content = first_result.get('content', '')
        
        print(f"First result content length: {len(content)}")
        print(f"First result preview: {content[:300]}...")
        
        # Check if it contains real memory management code
        if any(term in content.lower() for term in ['malloc', 'free', 'void *', 'tmwmem']):
            print(f"✅ Contains real memory management code")
            return True
        else:
            print(f"❌ Doesn't contain expected memory management code")
            return False
    else:
        print(f"\n❌ No results in tool response")
        return False

def test_simplified_query():
    """Test with a very simple, direct query"""
    print(f"\n🧪 Testing Simplified Query")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Very direct query
    query = "Call query_codebase with codebase_name='utils' and query='malloc'. Show me the first result you get."
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result length: {len(result)}")
    print(f"Result: {result}")
    
    # Check if it shows actual code
    if 'tmwmem' in result.lower() or 'malloc' in result.lower():
        print(f"✅ Shows real code")
        return True
    else:
        print(f"❌ Still hallucinating")
        return False

def create_better_prompt():
    """Create a better prompt that forces T5810 to use tool results"""
    print(f"\n🎯 Testing Better Prompt")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # More explicit prompt
    query = """I need you to search for memory management functions in the utils codebase. 

INSTRUCTIONS:
1. Use the query_codebase tool with these exact parameters:
   - codebase_name: "utils"  
   - query: "malloc free alloc"
   - max_results: 5

2. After you get the tool results, show me the actual code content from the first result.

3. Do NOT make up any code examples. Only show what the tool actually returns.

Please execute this now."""
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result length: {len(result)}")
    print(f"Result preview: {result[:600]}...")
    
    # Check for real code indicators
    real_indicators = ['tmwmem', 'Triangle MicroWorks', '/app/source_code/utils']
    found_indicators = [ind for ind in real_indicators if ind in result]
    
    print(f"Real code indicators found: {found_indicators}")
    
    if found_indicators:
        print(f"✅ SUCCESS: Shows real code from utils codebase")
        return True
    else:
        print(f"❌ Still not showing real code")
        return False

def main():
    print("🔍 Testing Exact Tool Results for Memory Management Query")
    print("=" * 70)
    
    # Test 1: Check exact tool result
    tool_works = test_exact_tool_result()
    
    # Test 2: Simplified query
    if tool_works:
        simple_works = test_simplified_query()
    else:
        print("❌ Skipping simplified query - tool has issues")
        simple_works = False
    
    # Test 3: Better prompt
    if tool_works:
        better_prompt_works = create_better_prompt()
    else:
        print("❌ Skipping better prompt - tool has issues")
        better_prompt_works = False
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print(f"Tool execution:     {'✅ Working' if tool_works else '❌ Broken'}")
    print(f"Simple query:       {'✅ Working' if simple_works else '❌ Broken'}")
    print(f"Better prompt:      {'✅ Working' if better_prompt_works else '❌ Broken'}")
    
    if tool_works and not better_prompt_works:
        print(f"\n💡 CONCLUSION: Tool works but T5810 is not using results properly")
        print(f"   The issue is in prompt engineering or T5810's instruction following")
    elif not tool_works:
        print(f"\n💡 CONCLUSION: Tool execution has issues")
        print(f"   Need to fix the underlying tool implementation")

if __name__ == "__main__":
    main()
