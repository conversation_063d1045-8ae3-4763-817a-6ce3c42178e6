#!/usr/bin/env python3
"""
Web Interface Integration for Remote Ollama Code Analysis
Adds remote model support to the existing web interface
"""

from flask import Flask, render_template_string, request, jsonify
from hybrid_code_analyzer import HybridCodeAnalyzer
import time

app = Flask(__name__)
analyzer = HybridCodeAnalyzer()

# HTML Template with remote model support
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Hybrid Code Analyzer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .model-selector { margin: 20px 0; padding: 15px; background: #e8f4f8; border-radius: 5px; }
        .model-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 10px 0; }
        .model-group { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .model-group h4 { margin: 0 0 10px 0; color: #333; }
        .model-option { margin: 5px 0; }
        .query-section { margin: 20px 0; }
        .query-input { width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .submit-btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .submit-btn:hover { background: #005a87; }
        .results { margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px; min-height: 100px; }
        .performance-info { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin: 10px 0; }
        .perf-card { padding: 10px; background: #e8f4f8; border-radius: 5px; text-align: center; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.loading { background: #fff3cd; border: 1px solid #ffeaa7; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Hybrid Code Analyzer</h1>
        <p>Intelligent routing between local OpenWebUI and remote Ollama instances</p>
        
        <div class="model-selector">
            <h3>📋 Model Selection</h3>
            <div class="model-grid">
                <div class="model-group">
                    <h4>🏠 Local Models (OpenWebUI)</h4>
                    <div class="model-option">
                        <input type="radio" name="model" value="llama3:latest" id="llama3" checked>
                        <label for="llama3">llama3:latest (home-ai-server)</label>
                    </div>
                    <div class="model-option">
                        <input type="radio" name="model" value="deepseek-coder:6.7b" id="deepseek">
                        <label for="deepseek">deepseek-coder:6.7b (46% faster)</label>
                    </div>
                    <div class="model-option">
                        <input type="radio" name="model" value="qwen2.5-coder:7b" id="qwen">
                        <label for="qwen">qwen2.5-coder:7b (code-specialized)</label>
                    </div>
                </div>
                
                <div class="model-group">
                    <h4>🌐 Remote Models (Direct Ollama)</h4>
                    <div class="model-option">
                        <input type="radio" name="model" value="LYNN-PC.llama3.1:latest" id="lynn">
                        <label for="lynn">lynn-pc llama3.1 (RTX 3050)</label>
                    </div>
                    <div class="model-option">
                        <input type="radio" name="model" value="T5810.llama3.1:latest" id="t5810">
                        <label for="t5810">T5810 llama3.1 (RTX 3090) ⚡ Fastest</label>
                    </div>
                    <div class="model-option">
                        <input type="radio" name="model" value="auto" id="auto">
                        <label for="auto">🎯 Auto-select (prefer fast)</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="query-section">
            <h3>🔍 Code Analysis Query</h3>
            <textarea class="query-input" id="query" placeholder="Enter your code analysis query...
Examples:
• List all available codebases
• Find memory management functions in utils codebase
• What programming languages are used in z80emu?
• Get statistics for TypeScript-Node-Starter-master"></textarea>
            <br><br>
            <button class="submit-btn" onclick="analyzeCode()">🚀 Analyze Code</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="performance-info" id="performance" style="display: none;">
            <div class="perf-card">
                <strong>Response Time</strong><br>
                <span id="response-time">-</span>
            </div>
            <div class="perf-card">
                <strong>Backend Used</strong><br>
                <span id="backend">-</span>
            </div>
            <div class="perf-card">
                <strong>Model</strong><br>
                <span id="model-used">-</span>
            </div>
        </div>
        
        <div class="results">
            <h3>📊 Results</h3>
            <div id="results-content">Select a model and enter a query to get started...</div>
        </div>
    </div>

    <script>
        async function analyzeCode() {
            const query = document.getElementById('query').value.trim();
            const selectedModel = document.querySelector('input[name="model"]:checked').value;
            
            if (!query) {
                alert('Please enter a query');
                return;
            }
            
            // Show loading status
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status loading';
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = '🔄 Processing query...';
            
            // Hide previous results
            document.getElementById('performance').style.display = 'none';
            document.getElementById('results-content').innerHTML = 'Processing...';
            
            const startTime = Date.now();
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        model: selectedModel
                    })
                });
                
                const data = await response.json();
                const endTime = Date.now();
                const responseTime = (endTime - startTime) / 1000;
                
                if (data.success) {
                    // Show success status
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ Analysis complete!';
                    
                    // Show performance info
                    document.getElementById('response-time').textContent = responseTime.toFixed(2) + 's';
                    document.getElementById('backend').textContent = data.backend;
                    document.getElementById('model-used').textContent = data.model;
                    document.getElementById('performance').style.display = 'grid';
                    
                    // Show results
                    document.getElementById('results-content').innerHTML = 
                        '<pre>' + data.result + '</pre>';
                } else {
                    // Show error status
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ Error: ' + data.error;
                    
                    document.getElementById('results-content').innerHTML = 
                        '<div style="color: red;">Error: ' + data.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Network error: ' + error.message;
                
                document.getElementById('results-content').innerHTML = 
                    '<div style="color: red;">Network error: ' + error.message + '</div>';
            }
        }
        
        // Auto-hide status after 5 seconds
        setInterval(() => {
            const statusDiv = document.getElementById('status');
            if (statusDiv.style.display !== 'none' && 
                (statusDiv.className.includes('success') || statusDiv.className.includes('error'))) {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }, 1000);
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main web interface"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/analyze', methods=['POST'])
def analyze():
    """Handle code analysis requests"""
    try:
        data = request.json
        query = data.get('query', '')
        model = data.get('model', 'auto')
        
        if not query:
            return jsonify({'success': False, 'error': 'No query provided'})
        
        # Use the hybrid analyzer
        result = analyzer.analyze_code(query, model=model, prefer_fast=(model == 'auto'))
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/models')
def get_models():
    """Get available models"""
    return jsonify(analyzer.get_available_models())

@app.route('/benchmark')
def benchmark():
    """Benchmark all models"""
    results = analyzer.benchmark_models()
    return jsonify(results)

if __name__ == '__main__':
    print("🚀 Starting Hybrid Code Analyzer Web Interface")
    print("=" * 60)
    print("🌐 Local models: OpenWebUI integration")
    print("🔗 Remote models: Direct Ollama tool calling")
    print("📊 Available at: http://localhost:5003")
    
    app.run(host='0.0.0.0', port=5003, debug=True)
