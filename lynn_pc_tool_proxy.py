#!/usr/bin/env python3
"""
Tool Proxy for lynn-pc
Forwards tool requests to home-ai-server to make tools appear local
"""

from flask import Flask, request, jsonify
import requests
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# Configuration
HOME_AI_SERVER = "192.168.0.77:5002"
PROXY_PORT = 5002

@app.route('/health', methods=['GET'])
def health():
    """Health check - forward to actual server"""
    try:
        response = requests.get(f"http://{HOME_AI_SERVER}/health", timeout=10)
        return response.json(), response.status_code
    except Exception as e:
        return {"error": "Proxy cannot reach home-ai-server", "details": str(e)}, 503

@app.route('/tools/<path:tool_path>', methods=['POST'])
def proxy_tool(tool_path):
    """Proxy tool requests to home-ai-server"""
    try:
        # Forward the request
        url = f"http://{HOME_AI_SERVER}/tools/{tool_path}"
        
        # Get request data
        data = request.get_json() if request.is_json else {}
        headers = {'Content-Type': 'application/json'}
        
        app.logger.info(f"Proxying tool request: {tool_path}")
        app.logger.info(f"Data: {data}")
        
        # Forward to home-ai-server
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        app.logger.info(f"Response status: {response.status_code}")
        
        # Return the response
        return response.json(), response.status_code
        
    except requests.exceptions.Timeout:
        app.logger.error(f"Timeout forwarding to {HOME_AI_SERVER}")
        return {"error": "Tool server timeout"}, 504
    except requests.exceptions.ConnectionError:
        app.logger.error(f"Cannot connect to {HOME_AI_SERVER}")
        return {"error": "Tool server unreachable"}, 503
    except Exception as e:
        app.logger.error(f"Proxy error: {e}")
        return {"error": "Proxy error", "details": str(e)}, 500

@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def proxy_all(path):
    """Proxy all other requests"""
    try:
        url = f"http://{HOME_AI_SERVER}/{path}"
        
        # Forward the request with same method
        if request.method == 'GET':
            response = requests.get(url, params=request.args, timeout=30)
        elif request.method == 'POST':
            data = request.get_json() if request.is_json else request.form
            response = requests.post(url, json=data if request.is_json else None, 
                                   data=None if request.is_json else data, timeout=30)
        elif request.method == 'PUT':
            data = request.get_json() if request.is_json else request.form
            response = requests.put(url, json=data if request.is_json else None,
                                  data=None if request.is_json else data, timeout=30)
        elif request.method == 'DELETE':
            response = requests.delete(url, timeout=30)
        
        # Return response
        try:
            return response.json(), response.status_code
        except:
            return response.text, response.status_code
            
    except Exception as e:
        app.logger.error(f"Proxy error for {path}: {e}")
        return {"error": "Proxy error", "details": str(e)}, 500

@app.route('/')
def root():
    """Root endpoint - show proxy status"""
    try:
        # Test connection to home-ai-server
        response = requests.get(f"http://{HOME_AI_SERVER}/health", timeout=5)
        server_status = "✅ Connected" if response.status_code == 200 else f"❌ Error {response.status_code}"
    except:
        server_status = "❌ Unreachable"
    
    return {
        "proxy": "lynn-pc Tool Proxy",
        "version": "1.0",
        "target_server": HOME_AI_SERVER,
        "server_status": server_status,
        "proxy_port": PROXY_PORT,
        "endpoints": [
            "/health - Health check",
            "/tools/* - Tool endpoints", 
            "/* - All other endpoints"
        ]
    }

if __name__ == '__main__':
    print(f"🚀 Starting lynn-pc Tool Proxy")
    print(f"📡 Forwarding to: {HOME_AI_SERVER}")
    print(f"🔗 Local port: {PROXY_PORT}")
    print(f"💡 Models should use: http://localhost:{PROXY_PORT}")
    
    app.run(host='0.0.0.0', port=PROXY_PORT, debug=False)
