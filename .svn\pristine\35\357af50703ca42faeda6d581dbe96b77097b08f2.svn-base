# Vector Database Management System

## 🎯 **Overview**

Complete CRUD (Create, Read, Update, Delete) operations for vector databases across all interfaces: Code Analyzer Server API, Web Management Interface, and OpenWebUI Tool integration.

## 🚀 **Available Operations**

### **➕ Create**
- **Purpose**: Create new vector database for a codebase
- **Use Case**: First-time indexing of a new codebase
- **Behavior**: Fails if codebase already exists

### **🔄 Rebuild** 
- **Purpose**: Delete existing database and recreate from scratch
- **Use Case**: Complete refresh when database is corrupted or needs full reindexing
- **Behavior**: Destructive operation - deletes all existing data first

### **⚙️ Reprocess**
- **Purpose**: Update existing database with changes
- **Use Case**: Incremental updates when source code changes
- **Behavior**: Updates existing database without deletion

### **🗑️ Delete**
- **Purpose**: Permanently remove vector database
- **Use Case**: Remove unused codebases or clean up storage
- **Behavior**: Permanent deletion with confirmation required

### **📋 List**
- **Purpose**: View all available vector databases
- **Use Case**: See what codebases are indexed
- **Behavior**: Read-only operation

## 🔧 **Code Analyzer Server API**

### **Endpoints Added:**

#### **Create Codebase**
```http
POST /tools/create_codebase
Content-Type: application/json

{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
}
```

#### **Rebuild Codebase**
```http
POST /tools/rebuild_codebase
Content-Type: application/json

{
  "codebase_name": "my_project", 
  "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
}
```

#### **Reprocess Codebase**
```http
POST /tools/process_codebase
Content-Type: application/json

{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
}
```

#### **Delete Codebase**
```http
POST /tools/delete_codebase
Content-Type: application/json

{
  "codebase_name": "my_project"
}
```

#### **List Codebases**
```http
POST /tools/list_codebases
Content-Type: application/json

{}
```

### **Response Format:**
```json
{
  "success": true,
  "result": "Formatted result text with statistics",
  "codebase_name": "my_project",
  "processing_stats": {
    "files_processed": 150,
    "chunks_created": 1250,
    "functions_discovered": 89,
    "processing_time_seconds": 45.2
  }
}
```

## 🌐 **Web Management Interface**

### **New API Endpoints:**

#### **Create Vector Database**
```http
POST /api/vector_db/create
Content-Type: application/json

{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test"]
}
```

#### **Rebuild Vector Database**
```http
POST /api/vector_db/rebuild
Content-Type: application/json

{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test"]
}
```

#### **Reprocess Vector Database**
```http
POST /api/vector_db/reprocess
Content-Type: application/json

{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test"]
}
```

#### **Delete Vector Database**
```http
POST /api/vector_db/delete
Content-Type: application/json

{
  "codebase_name": "my_project"
}
```

### **Dashboard UI Features:**

#### **Vector Database Management Section**
- **Create New**: Green button for creating new databases
- **Rebuild**: Orange button for complete rebuild
- **Reprocess**: Blue button for incremental updates  
- **Delete**: Red button with confirmation for deletion

#### **Safety Features:**
- **Confirmation dialogs** for destructive operations
- **Double confirmation** for delete (type codebase name)
- **Progress indicators** during long operations
- **Detailed result display** with statistics
- **Auto-refresh** of codebase list after operations

#### **User Experience:**
- **Color-coded buttons** for easy identification
- **Helpful tooltips** explaining each operation
- **Real-time feedback** during processing
- **Error handling** with clear messages
- **5-minute timeout** for long operations

## 🔧 **OpenWebUI Tool Integration**

### **Integrated into Main Tool:**
The vector database management functions are now integrated into the main `open_webui_code_analyzer_tool.py` file. No separate tool installation required!

### **Available Functions in OpenWebUI:**

#### **Create Vector Database**
```
create_vector_database(codebase_name="my_project", exclude_dirs=["build", "test"])
```

#### **Rebuild Vector Database**
```
rebuild_vector_database(codebase_name="my_project", exclude_dirs=["build", "test"])
```

#### **Reprocess Vector Database**
```
reprocess_vector_database(codebase_name="my_project", exclude_dirs=["build", "test"])
```

#### **Delete Vector Database**
```
delete_vector_database(codebase_name="my_project")
```

#### **List Codebases (existing function)**
```
list_codebases()
```

### **OpenWebUI Integration:**
- **Integrated functions** in main code analyzer tool
- **Formatted responses** with detailed statistics and emojis
- **Error handling** with clear error messages
- **Timeout management** (5 minutes for processing, 1 minute for delete)
- **Async operations** for better performance

## 📊 **Operation Details**

### **Processing Statistics:**
Each operation returns detailed statistics:
- **Files processed**: Number of source files analyzed
- **Chunks created/updated**: Vector database entries
- **Functions discovered**: Code functions found and indexed
- **Processing time**: Time taken for operation
- **Languages detected**: Programming languages found
- **Deletion stats**: For rebuild/delete operations

### **Exclude Directories:**
Default excluded directories:
- `build` - Build artifacts
- `test` - Test files
- `bin` - Binary files
- `obj` - Object files
- `__pycache__` - Python cache
- `.git` - Git repository data

### **Timeouts:**
- **Create/Rebuild/Reprocess**: 5 minutes (300 seconds)
- **Delete**: 1 minute (60 seconds)
- **List**: 30 seconds

## 🔒 **Safety & Best Practices**

### **Confirmation Requirements:**
- **Delete**: Double confirmation with codebase name typing
- **Rebuild**: Single confirmation with warning about data loss
- **Create**: Check for existing codebase to prevent conflicts

### **Error Handling:**
- **Network timeouts** with graceful degradation
- **Server errors** with detailed error messages
- **Invalid parameters** with helpful suggestions
- **Concurrent operations** protection

### **Monitoring:**
- **Real-time progress** indicators
- **Detailed logging** of all operations
- **Statistics tracking** for performance monitoring
- **Auto-refresh** of status after operations

## 🎯 **Use Cases**

### **Development Workflow:**
1. **Initial Setup**: Use `Create` for new projects
2. **Code Changes**: Use `Reprocess` for incremental updates
3. **Major Refactoring**: Use `Rebuild` for complete refresh
4. **Project Cleanup**: Use `Delete` to remove unused codebases

### **Maintenance:**
- **Database Corruption**: Use `Rebuild` to fix issues
- **Performance Issues**: Use `Rebuild` to optimize
- **Storage Cleanup**: Use `Delete` for unused codebases
- **Monitoring**: Use `List` to check status

### **Integration:**
- **CI/CD Pipelines**: Automated reprocessing on code changes
- **Development Tools**: IDE integration via OpenWebUI
- **Web Interface**: Manual management via dashboard
- **API Integration**: Programmatic control via REST API

## 🚀 **Deployment**

### **Files to Deploy:**
- `main.py` - Updated with new endpoints
- `web_management_server.py` - Web API endpoints
- `templates/dashboard.html` - UI with management controls
- `open_webui_code_analyzer_tool.py` - Updated with vector DB management functions

### **Access Points:**
- **Web Dashboard**: `http://home-ai-server.local:5003`
- **API Endpoints**: `http://home-ai-server.local:5002/tools/*`
- **OpenWebUI Tool**: Available for function calling

This comprehensive vector database management system provides full CRUD operations across all interfaces, ensuring users have complete control over their codebase indexing and vector database lifecycle! 🎉
