# Intent Detection Testing

## 🧪 **Server-Side Intent Detection Test Script**

The `test_intent_detection.py` script provides comprehensive testing of the server-side intent detection system independently of the OpenWebUI tool.

## 🚀 **Usage**

### **Basic Usage:**
```bash
python test_intent_detection.py
```

### **Custom Server URL:**
```bash
python test_intent_detection.py http://your-server:5002
```

### **Requirements:**
```bash
pip install aiohttp
```

## 📊 **What It Tests**

### **Test Categories:**

1. **Original Problem Query**
   - "tell me about a modbus holding register and the functions used to manage it"

2. **Code Analysis Queries**
   - Function lookups: "what is tmwmem_alloc function"
   - Memory management: "show me memory management functions"
   - Algorithm explanations: "explain the algorithm used in sorting"

3. **Complexity Analysis Queries**
   - "analyze complexity of utils codebase"
   - "complexity analysis for modbus"
   - "code complexity analysis"

4. **Management Queries**
   - "list codebases"
   - "select codebase utils"
   - "get stats"

5. **Help Queries**
   - "help with codebase analyzer"
   - "analyzer documentation"

6. **Protocol-Specific Queries**
   - "how does modbus communication work"
   - "bacnet protocol implementation"

7. **Edge Cases**
   - Empty queries
   - Unrelated questions
   - Very long queries

## 📋 **Output Format**

### **Real-Time Results:**
```
🔍 Testing: 'tell me about a modbus holding register and the functions...'
   Intent: code_analysis (confidence: 0.85)
   Action: route_to_context_retrieval
   Keywords: modbus(protocol), register(protocol), tell me about(code_question)
   Patterns: tell me about(code_question)
```

### **Summary Report:**
```
📊 INTENT DETECTION TEST SUMMARY
================================================================================
Total Queries: 25
Errors: 0
Average Confidence: 0.78

Intent Distribution:
  • code_analysis: 15 queries (60.0%)
  • management: 5 queries (20.0%)
  • help: 3 queries (12.0%)
  • general: 2 queries (8.0%)

Detailed Results:
 1. Query: 'tell me about a modbus holding register and the...'
    Intent: code_analysis | Confidence: 0.85 | Action: route_to_context_retrieval
```

## 🔧 **Server Requirements**

### **Server Must Be Running:**
- Code analyzer server on port 5002 (or specified port)
- Intent detection service initialized
- Configuration file loaded

### **Required Endpoints:**
- `GET /health` - Server health check
- `GET /tools/intent_config` - Configuration information
- `POST /tools/detect_intent` - Intent detection

## 🎯 **Expected Results**

### **Key Test Cases:**

1. **Original Problem Query:**
   - **Expected:** `code_analysis` intent
   - **Action:** `route_to_context_retrieval`
   - **Keywords:** `modbus(protocol)`, `register(protocol)`

2. **Complexity Analysis:**
   - **Expected:** `code_analysis` intent
   - **Action:** `route_to_complexity_analyzer`
   - **Keywords:** `complexity(analysis)`, `analysis(analysis)`

3. **Management Operations:**
   - **Expected:** `management` intent
   - **Action:** `route_to_management`
   - **Patterns:** Management phrases

4. **Help Requests:**
   - **Expected:** `help` intent
   - **Action:** `route_to_help`
   - **Patterns:** Help phrases

## 🐛 **Troubleshooting**

### **Common Issues:**

1. **Server Not Available:**
   ```
   ❌ Server health check failed: Connection refused
   ```
   **Solution:** Start the code analyzer server

2. **Configuration Not Found:**
   ```
   Config Exists: ❌
   ```
   **Solution:** Ensure `config/intent_detection.json5` exists

3. **All Queries Return "general":**
   **Possible Causes:**
   - Configuration file empty or malformed
   - Intent detection service not initialized
   - Server-side errors

### **Debug Steps:**

1. **Check Server Health:**
   ```bash
   curl http://localhost:5002/health
   ```

2. **Check Configuration:**
   ```bash
   curl http://localhost:5002/tools/intent_config
   ```

3. **Test Single Query:**
   ```bash
   curl -X POST http://localhost:5002/tools/detect_intent \
     -H "Content-Type: application/json" \
     -d '{"query": "test query"}'
   ```

## 📈 **Performance Metrics**

### **Typical Performance:**
- **Response Time:** < 100ms per query
- **Accuracy:** > 90% for well-defined intents
- **Confidence:** 0.7-0.9 for clear matches

### **Benchmarking:**
The script automatically measures:
- Total test time
- Average confidence scores
- Error rates
- Intent distribution

## 🔄 **Continuous Testing**

### **Integration with Development:**
1. Run tests after configuration changes
2. Validate new keyword additions
3. Verify routing rule modifications
4. Test edge cases and error handling

### **Automated Testing:**
```bash
# Add to CI/CD pipeline
python test_intent_detection.py http://test-server:5002
```

This testing approach ensures the server-side intent detection system works correctly and provides detailed feedback for debugging and optimization.
