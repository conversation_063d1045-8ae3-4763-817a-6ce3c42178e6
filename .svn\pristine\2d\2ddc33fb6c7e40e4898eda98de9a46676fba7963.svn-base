#!/usr/bin/env python3
"""
Code Analyzer Web Management Server
Provides a web interface for managing the Code Analyzer Server at port 5002
Runs on port 5003 by default
"""

from fastapi import Fast<PERSON>I, Request, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
import requests
import json
import time
import os
from datetime import datetime
from typing import Dict, Any, Optional
import uvicorn

# Import hybrid code analyzer for remote Ollama integration
try:
    from hybrid_code_analyzer import HybridCodeAnalyzer
    HYBRID_ANALYZER_AVAILABLE = True
except ImportError:
    HYBRID_ANALYZER_AVAILABLE = False
    print("⚠️ Hybrid analyzer not available - remote Ollama features disabled")

# Configuration
CODE_ANALYZER_BASE_URL = os.getenv("CODE_ANALYZER_BASE_URL", "http://localhost:5002")
WEB_MANAGEMENT_PORT = int(os.getenv("WEB_MANAGEMENT_PORT", "5003"))

# Progress tracking for bulk operations
class ProgressTracker:
    def __init__(self):
        self.active_operations = {}
        self.websocket_connections = []

    async def start_operation(self, operation_id: str, total_items: int, operation_name: str):
        """Start tracking a new operation"""
        self.active_operations[operation_id] = {
            "operation_name": operation_name,
            "total_items": total_items,
            "completed_items": 0,
            "current_item": "",
            "status": "running",
            "start_time": datetime.now(),
            "results": []
        }
        await self.broadcast_progress(operation_id)

    async def update_progress(self, operation_id: str, current_item: str, completed: bool = False, result: dict | None = None):
        """Update progress for an operation"""
        if operation_id not in self.active_operations:
            return

        operation = self.active_operations[operation_id]
        operation["current_item"] = current_item

        if completed:
            operation["completed_items"] += 1
            if result:
                operation["results"].append(result)

        await self.broadcast_progress(operation_id)

    async def complete_operation(self, operation_id: str, final_status: str = "completed"):
        """Mark an operation as completed"""
        if operation_id not in self.active_operations:
            return

        operation = self.active_operations[operation_id]
        operation["status"] = final_status
        operation["end_time"] = datetime.now()

        await self.broadcast_progress(operation_id)

    async def broadcast_progress(self, operation_id: str):
        """Broadcast progress to all connected WebSocket clients"""
        if operation_id not in self.active_operations:
            return

        operation = self.active_operations[operation_id]
        progress_data = {
            "operation_id": operation_id,
            "operation_name": operation["operation_name"],
            "total_items": operation["total_items"],
            "completed_items": operation["completed_items"],
            "current_item": operation["current_item"],
            "status": operation["status"],
            "progress_percent": (operation["completed_items"] / operation["total_items"] * 100) if operation["total_items"] > 0 else 0,
            "results": operation["results"]
        }

        # Send to all connected WebSocket clients
        disconnected_clients = []
        for websocket in self.websocket_connections:
            try:
                await websocket.send_text(json.dumps(progress_data))
            except (WebSocketDisconnect, RuntimeError, Exception):
                disconnected_clients.append(websocket)

        # Remove disconnected clients
        for client in disconnected_clients:
            if client in self.websocket_connections:
                self.websocket_connections.remove(client)

    def add_websocket(self, websocket):
        """Add a WebSocket connection"""
        self.websocket_connections.append(websocket)

    def remove_websocket(self, websocket):
        """Remove a WebSocket connection"""
        if websocket in self.websocket_connections:
            self.websocket_connections.remove(websocket)

# Global progress tracker
progress_tracker = ProgressTracker()

# Global session storage for codebase selection
web_session_codebase: Optional[str] = None

app = FastAPI(
    title="Code Analyzer Management Interface",
    description="Web interface for managing the Code Analyzer Server",
    version="1.0.0"
)

# Setup templates and static files (create directory if it doesn't exist)
if not os.path.exists("templates"):
    os.makedirs("templates")
templates = Jinja2Templates(directory="templates")

# Initialize hybrid analyzer for remote Ollama integration
hybrid_analyzer = None
if HYBRID_ANALYZER_AVAILABLE:
    try:
        hybrid_analyzer = HybridCodeAnalyzer()
        print("✅ Hybrid Code Analyzer initialized - remote Ollama support enabled")
    except Exception as e:
        print(f"⚠️ Failed to initialize Hybrid Code Analyzer: {e}")
        HYBRID_ANALYZER_AVAILABLE = False

class CodeAnalyzerManager:
    """Manager class for interacting with the Code Analyzer Server"""
    
    def __init__(self, base_url: str = CODE_ANALYZER_BASE_URL):
        self.base_url = base_url
        self.last_health_check: Optional[datetime] = None
        self.health_status: Optional[Dict[str, Any]] = None
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status with enhanced metrics"""
        try:
            start_time = time.time()

            # Basic health
            response = requests.get(f"{self.base_url}/health", timeout=10)
            basic_health = response.json() if response.status_code == 200 else {"error": "Health check failed"}

            # Detailed health
            detailed_health: Dict[str, Any] = {}
            try:
                detailed_response = requests.get(f"{self.base_url}/health/detailed", timeout=10)
                detailed_health = detailed_response.json() if detailed_response.status_code == 200 else {}
            except Exception:
                pass

            # Status endpoint for additional info
            status_info: Dict[str, Any] = {}
            try:
                status_response = requests.get(f"{self.base_url}/status", timeout=5)
                status_info = status_response.json() if status_response.status_code == 200 else {}
            except Exception:
                pass

            # Analysis service health
            analysis_health: Dict[str, Any] = {}
            try:
                analysis_response = requests.get(f"{self.base_url}/analysis/health", timeout=5)
                analysis_health = analysis_response.json() if analysis_response.status_code == 200 else {}
            except Exception:
                pass

            # System resource information
            system_resources = self._get_system_resources()

            # Combine health data
            health_data = {
                "timestamp": datetime.now().isoformat(),
                "server_responsive": response.status_code == 200,
                "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                "total_check_time": time.time() - start_time,
                "basic_health": basic_health,
                "detailed_health": detailed_health,
                "status_info": status_info,
                "analysis_health": analysis_health,
                "system_resources": system_resources
            }
            
            self.last_health_check = datetime.now()
            self.health_status = health_data
            return health_data
            
        except requests.exceptions.ConnectionError:
            return {
                "timestamp": datetime.now().isoformat(),
                "server_responsive": False,
                "error": "Connection refused - Server may be down",
                "basic_health": {},
                "detailed_health": {}
            }
        except Exception as e:
            return {
                "timestamp": datetime.now().isoformat(),
                "server_responsive": False,
                "error": str(e),
                "basic_health": {},
                "detailed_health": {}
            }
    
    async def get_codebases_status(self) -> Dict[str, Any]:
        """Get status of all codebases"""
        try:
            response = requests.post(f"{self.base_url}/tools/list_codebases", json={}, timeout=10)
            if response.status_code == 200:
                data = response.json()
                result_text = data.get('result', '')
                
                # Parse codebase information from the result text
                codebases = []

                # Extract codebase names from the formatted result text
                # Look for lines that start with "**✅ " followed by codebase name
                import re
                codebase_pattern = r'\*\*✅\s+([a-zA-Z0-9_-]+)\*\*'
                matches = re.findall(codebase_pattern, result_text)

                # Also look for the rocket pattern "**🚀 codebase_name**"
                rocket_pattern = r'\*\*🚀\s+([a-zA-Z0-9_-]+)\*\*'
                rocket_matches = re.findall(rocket_pattern, result_text)
                matches.extend(rocket_matches)

                # Remove duplicates while preserving order
                seen = set()
                unique_matches = []
                for match in matches:
                    if match not in seen:
                        seen.add(match)
                        unique_matches.append(match)

                matches = unique_matches

                # Get stats for each discovered codebase
                for codebase_name in matches:
                    try:
                        stats_response = requests.post(f"{self.base_url}/tools/get_code_stats",
                                                     json={"codebase_name": codebase_name}, timeout=5)
                        stats = stats_response.json() if stats_response.status_code == 200 else {}

                        # Determine status based on stats response
                        if stats_response.status_code == 200:
                            status = "ready_basic"
                        else:
                            status = "unknown"

                        codebases.append({
                            "name": codebase_name,
                            "status": status,
                            "stats": stats.get('result', 'No stats available'),
                            "last_updated": datetime.now().isoformat()
                        })
                    except Exception as e:
                        # Add codebase even if stats fail
                        codebases.append({
                            "name": codebase_name,
                            "status": "error",
                            "stats": f"Error getting stats: {str(e)}",
                            "last_updated": datetime.now().isoformat()
                        })

                # If no codebases found through parsing, add a fallback
                if not codebases:
                    codebases.append({
                        "name": "No codebases detected",
                        "status": "error",
                        "stats": "Could not parse codebase list",
                        "last_updated": datetime.now().isoformat()
                    })
                
                return {
                    "success": True,
                    "codebases": codebases,
                    "total_codebases": len(codebases)
                }
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_system_resources(self) -> Dict[str, Any]:
        """Get system resource information"""
        import psutil

        try:
            # CPU information
            cpu_info = {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "count_logical": psutil.cpu_count(logical=True)
            }

            # Memory information
            memory = psutil.virtual_memory()
            memory_info = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "usage_percent": memory.percent
            }

            # Disk information
            disk = psutil.disk_usage('/')
            disk_info = {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_gb": round(disk.used / (1024**3), 2),
                "usage_percent": round((disk.used / disk.total) * 100, 1)
            }

            # Process information
            current_process = psutil.Process()
            process_info = {
                "memory_mb": round(current_process.memory_info().rss / (1024**2), 2),
                "cpu_percent": current_process.cpu_percent(),
                "threads": current_process.num_threads(),
                "open_files": len(current_process.open_files())
            }

            return {
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "process": process_info,
                "available": True
            }

        except Exception as e:
            return {
                "available": False,
                "error": str(e)
            }

    async def get_gpu_status(self) -> Dict[str, Any]:
        """Get comprehensive GPU infrastructure status"""
        try:
            # Get basic GPU status
            gpu_response = requests.get(f"{self.base_url}/tools/gpu_status", timeout=10)

            if gpu_response.status_code != 200:
                return {"success": False, "error": f"GPU status HTTP {gpu_response.status_code}"}

            gpu_data = gpu_response.json()

            # Get processing recommendations for sample workload
            try:
                rec_response = requests.post(f"{self.base_url}/tools/gpu_recommendations",
                                           json={"chunk_count": 100}, timeout=10)
                recommendations = rec_response.json() if rec_response.status_code == 200 else {}
            except Exception:
                recommendations = {}

            # Get framework status for additional GPU info
            try:
                framework_response = requests.get(f"{self.base_url}/tools/framework_status", timeout=10)
                framework_data = framework_response.json() if framework_response.status_code == 200 else {}
                framework_gpu = framework_data.get('gpu_infrastructure', {})
            except Exception:
                framework_gpu = {}

            # Combine all GPU information
            comprehensive_gpu_data = {
                "timestamp": datetime.now().isoformat(),
                "gpu_infrastructure_available": gpu_data.get('gpu_infrastructure_available', False),
                "available_gpus": gpu_data.get('available_gpus', 0),
                "supported_gpu_types": gpu_data.get('supported_gpu_types', 0),
                "gpu_details": gpu_data.get('gpu_details', {}),
                "supported_types": gpu_data.get('supported_types', []),
                "processing_recommendations": recommendations,
                "framework_gpu_info": framework_gpu,
                "performance_metrics": self._calculate_gpu_performance_metrics(gpu_data),
                "infrastructure_summary": self._generate_gpu_summary(gpu_data)
            }

            return {"success": True, "data": comprehensive_gpu_data}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_server_metrics(self) -> Dict[str, Any]:
        """Get comprehensive server performance metrics"""
        try:
            start_time = time.time()

            # Test multiple endpoints to get response times
            endpoints = [
                ("/health", "GET"),
                ("/status", "GET"),
                ("/health/detailed", "GET"),
                ("/analysis/health", "GET"),
                ("/analysis/status", "GET"),
                ("/tools/list_codebases", "POST"),
                ("/tools/gpu_status", "GET")
            ]

            endpoint_metrics = {}
            successful_endpoints = 0
            total_response_time = 0.0

            for endpoint, method in endpoints:
                try:
                    endpoint_start = time.time()

                    if method == "POST":
                        response = requests.post(f"{self.base_url}{endpoint}", json={}, timeout=5)
                    else:
                        response = requests.get(f"{self.base_url}{endpoint}", timeout=5)

                    response_time = time.time() - endpoint_start

                    endpoint_metrics[endpoint] = {
                        "response_time_ms": round(response_time * 1000, 2),
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "method": method,
                        "content_length": len(response.content) if response.content else 0
                    }

                    if response.status_code == 200:
                        successful_endpoints += 1
                        total_response_time += response_time

                except Exception as e:
                    endpoint_metrics[endpoint] = {
                        "response_time_ms": 0,
                        "status_code": 0,
                        "success": False,
                        "method": method,
                        "error": str(e),
                        "content_length": 0
                    }

            # Calculate performance metrics
            avg_response_time = total_response_time / successful_endpoints if successful_endpoints > 0 else 0
            success_rate = (successful_endpoints / len(endpoints)) * 100

            # Get system resources
            system_resources = self._get_system_resources()

            # Get service-specific metrics
            service_metrics = await self._get_service_metrics()

            # Compile comprehensive metrics
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "collection_time_ms": round((time.time() - start_time) * 1000, 2),
                "endpoint_performance": {
                    "total_endpoints_tested": len(endpoints),
                    "successful_endpoints": successful_endpoints,
                    "success_rate_percent": round(success_rate, 1),
                    "average_response_time_ms": round(avg_response_time * 1000, 2),
                    "endpoints": endpoint_metrics
                },
                "system_resources": system_resources,
                "service_metrics": service_metrics
            }

            return {"success": True, "metrics": metrics}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _get_service_metrics(self) -> Dict[str, Any]:
        """Get service-specific metrics and statistics"""
        try:
            service_metrics: Dict[str, Any] = {
                "codebases": {
                    "total": 0,
                    "active": 0,
                    "with_vector_data": 0
                },
                "analysis_service": {
                    "available": False,
                    "analyzed_codebases": 0
                },
                "gpu_infrastructure": {
                    "available": False,
                    "gpu_count": 0
                },
                "embedding_service": {
                    "provider": "unknown",
                    "status": "unknown"
                }
            }

            # Get codebase metrics directly from code analyzer server
            try:
                # Get basic codebase list
                list_response = requests.post(f"{self.base_url}/tools/list_codebases", json={}, timeout=10)
                if list_response.status_code == 200:
                    list_data = list_response.json()

                    # Extract codebase names
                    codebase_names = []
                    if isinstance(list_data, dict) and 'codebases' in list_data:
                        codebase_names = [cb.get('name') for cb in list_data['codebases'] if cb.get('name')]
                    elif isinstance(list_data, list):
                        codebase_names = [cb.get('name') for cb in list_data if cb.get('name')]
                    else:
                        # Fallback: scan source_code directory
                        import os
                        source_code_dir = "source_code"
                        if os.path.exists(source_code_dir):
                            codebase_names = [d for d in os.listdir(source_code_dir)
                                            if os.path.isdir(os.path.join(source_code_dir, d))]

                    service_metrics["codebases"]["total"] = len(codebase_names)

                    # Count active codebases (those with actual files)
                    active_count = 0
                    vector_count = 0

                    for codebase_name in codebase_names:
                        # Check if codebase has source files
                        source_stats = analyze_source_code_directory(codebase_name)
                        if source_stats.get('total_files', 0) > 0:
                            active_count += 1

                        # Check for vector data by trying enhanced stats
                        try:
                            stats_response = requests.post(f"{self.base_url}/tools/get_enhanced_stats",
                                                         json={"codebase_name": codebase_name}, timeout=5)
                            if stats_response.status_code == 200:
                                stats_data = stats_response.json()
                                result_text = stats_data.get('result', '')
                                if 'code chunks' in result_text and 'No data' not in result_text:
                                    vector_count += 1
                        except Exception:
                            pass

                    service_metrics["codebases"]["active"] = active_count
                    service_metrics["codebases"]["with_vector_data"] = vector_count

            except Exception:
                pass

            # Get analysis service metrics
            try:
                analysis_response = requests.get(f"{self.base_url}/analysis/status", timeout=5)
                if analysis_response.status_code == 200:
                    analysis_data = analysis_response.json()
                    service_metrics["analysis_service"]["available"] = True
                    service_metrics["analysis_service"]["analyzed_codebases"] = analysis_data.get('analyzed_codebases', 0)
            except Exception:
                pass

            # Get GPU metrics
            try:
                gpu_response = requests.get(f"{self.base_url}/tools/gpu_status", timeout=5)
                if gpu_response.status_code == 200:
                    gpu_data = gpu_response.json()
                    service_metrics["gpu_infrastructure"]["available"] = True
                    # Extract GPU count from response
                    if isinstance(gpu_data, dict) and 'result' in gpu_data:
                        result_text = gpu_data['result']
                        if 'GPU' in result_text:
                            service_metrics["gpu_infrastructure"]["gpu_count"] = result_text.count('GPU')
            except Exception:
                pass

            # Get embedding service info from health check
            try:
                health_response = requests.get(f"{self.base_url}/health", timeout=5)
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    service_metrics["embedding_service"]["provider"] = health_data.get('embedding_provider', 'unknown')
                    service_metrics["embedding_service"]["status"] = "healthy" if health_data.get('code_analyzer_service') == 'healthy' else 'unhealthy'
            except Exception:
                pass

            return service_metrics

        except Exception as e:
            return {"error": str(e)}

    def _calculate_gpu_performance_metrics(self, gpu_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate GPU performance metrics"""
        try:
            gpu_details = gpu_data.get('gpu_details', {})

            if not gpu_details:
                return {"available": False, "reason": "No GPU details available"}

            total_vram_gb = 0.0
            gpu_types = set()
            architectures = set()
            processing_speeds = []

            for host, details in gpu_details.items():
                specs = details.get('specifications', {})

                # Extract VRAM (remove 'GB' and convert to float)
                vram_str = specs.get('vram', '0GB')
                vram_gb = float(vram_str.replace('GB', '').strip()) if 'GB' in vram_str else 0
                total_vram_gb += vram_gb

                gpu_types.add(details.get('type', 'unknown'))
                architectures.add(specs.get('architecture', 'unknown'))
                processing_speeds.append(specs.get('processing_speed', 1.0))

            avg_processing_speed = sum(processing_speeds) / len(processing_speeds) if processing_speeds else 0

            return {
                "available": True,
                "total_vram_gb": total_vram_gb,
                "unique_gpu_types": len(gpu_types),
                "unique_architectures": len(architectures),
                "average_processing_speed": round(avg_processing_speed, 2),
                "gpu_types_list": list(gpu_types),
                "architectures_list": list(architectures),
                "total_gpu_hosts": len(gpu_details)
            }

        except Exception as e:
            return {"available": False, "error": str(e)}

    def _generate_gpu_summary(self, gpu_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate GPU infrastructure summary"""
        try:
            gpu_details = gpu_data.get('gpu_details', {})

            summary = {
                "infrastructure_status": "available" if gpu_data.get('gpu_infrastructure_available') else "unavailable",
                "total_gpus": gpu_data.get('available_gpus', 0),
                "supported_types": gpu_data.get('supported_gpu_types', 0),
                "deployment_ready": gpu_data.get('gpu_infrastructure_available', False) and gpu_data.get('available_gpus', 0) > 0
            }

            if gpu_details:
                # Categorize GPUs by tier
                tiers: dict[str, list[str]] = {}
                for host, details in gpu_details.items():
                    tier = details.get('tier', 'unknown')
                    if tier not in tiers:
                        tiers[tier] = []
                    tiers[tier].append(details.get('type', 'unknown'))

                summary["gpu_tiers"] = tiers
                summary["primary_gpu"] = list(gpu_details.values())[0] if gpu_details else None

            return summary

        except Exception as e:
            return {"error": str(e)}

# Initialize manager
manager = CodeAnalyzerManager()

# Web Routes
@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/api/health")
async def api_health():
    """API endpoint for health status"""
    health_data = await manager.get_health_status()
    return JSONResponse(content=health_data)

@app.get("/api/codebases")
async def api_codebases():
    """API endpoint for codebase status"""
    codebases_data = await manager.get_codebases_status()
    return JSONResponse(content=codebases_data)

@app.get("/api/codebases/detailed")
async def api_codebases_detailed():
    """API endpoint for detailed codebase statistics"""
    try:
        # Get basic codebase list
        response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/list_codebases", timeout=30)

        if response.status_code != 200:
            return JSONResponse(content={
                "success": False,
                "error": "Failed to get codebase list",
                "status_code": response.status_code
            })

        data = response.json()

        # Extract codebase names
        codebase_names = []
        if isinstance(data, dict) and 'codebases' in data:
            codebase_names = [cb.get('name') for cb in data['codebases'] if cb.get('name')]
        elif isinstance(data, list):
            codebase_names = [cb.get('name') for cb in data if cb.get('name')]
        else:
            # Fallback: scan source_code directory
            import os
            source_code_dir = "source_code"
            if os.path.exists(source_code_dir):
                codebase_names = [d for d in os.listdir(source_code_dir)
                                if os.path.isdir(os.path.join(source_code_dir, d))]

        # Get detailed stats for each codebase
        detailed_stats = {}
        total_files = 0
        total_lines = 0
        total_size = 0
        language_distribution = {}

        for codebase_name in codebase_names:
            try:
                # Get enhanced codebase statistics and actual source code analysis
                stats_response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/get_enhanced_stats", json={
                    "codebase_name": codebase_name
                }, timeout=30)

                # Also get actual source code file analysis
                source_stats = analyze_source_code_directory(codebase_name)

                if stats_response.status_code == 200:
                    stats_data = stats_response.json()
                    result_text = stats_data.get('result', '')

                    # Extract vector database statistics
                    import re

                    # Extract chunk and file counts from vector database
                    chunk_match = re.search(r'(\d+(?:,\d+)*)\s*code chunks', result_text)
                    vector_chunks = int(chunk_match.group(1).replace(',', '')) if chunk_match else 0

                    vector_file_match = re.search(r'across\s*(\d+(?:,\d+)*)\s*files', result_text)
                    vector_files = int(vector_file_match.group(1).replace(',', '')) if vector_file_match else 0

                    # Use source code analysis for actual file counts and sizes
                    files = source_stats.get('total_files', vector_files)
                    lines = source_stats.get('total_lines', 0)
                    size = source_stats.get('total_size_bytes', 0)
                    languages = source_stats.get('languages', {})

                    # Add to global language distribution
                    for lang_name, lang_data in languages.items():
                        lang_files = lang_data.get('files', 0)
                        if lang_name not in language_distribution:
                            language_distribution[lang_name] = 0
                        language_distribution[lang_name] += lang_files

                    # Calculate average lines per file
                    avg_lines_per_file = lines / files if files > 0 else 0

                    # Merge vector database stats with source code stats
                    detailed_stats[codebase_name] = {
                        'name': codebase_name,
                        'source_files': files,
                        'source_lines': lines,
                        'source_size_bytes': int(size),
                        'source_size_formatted': format_file_size(size),
                        'vector_chunks': vector_chunks,
                        'vector_files': vector_files,
                        'languages': languages,
                        'avg_lines_per_file': round(avg_lines_per_file, 1),
                        'status': 'active',
                        # For backward compatibility
                        'files': files,
                        'lines': lines,
                        'size_bytes': int(size),
                        'size_formatted': format_file_size(size)
                    }

                    # Add to totals
                    total_files += files
                    total_lines += lines
                    total_size += size

                else:
                    detailed_stats[codebase_name] = {
                        'name': codebase_name,
                        'status': 'error',
                        'error': f"HTTP {stats_response.status_code}"
                    }

            except Exception as e:
                detailed_stats[codebase_name] = {
                    'name': codebase_name,
                    'status': 'error',
                    'error': str(e)
                }

        # Calculate global averages
        avg_files_per_codebase = total_files / len(codebase_names) if codebase_names else 0
        avg_lines_per_codebase = total_lines / len(codebase_names) if codebase_names else 0

        return JSONResponse(content={
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_codebases": len(codebase_names),
                "total_files": total_files,
                "total_lines": total_lines,
                "total_size_bytes": int(total_size),
                "total_size_formatted": format_file_size(total_size),
                "avg_files_per_codebase": round(avg_files_per_codebase, 1),
                "avg_lines_per_codebase": round(avg_lines_per_codebase, 1),
                "language_distribution": language_distribution
            },
            "codebases": detailed_stats
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

def analyze_source_code_directory(codebase_name):
    """Analyze actual source code files in the directory"""

    source_code_dir = os.path.join("source_code", codebase_name)

    if not os.path.exists(source_code_dir):
        return {
            'total_files': 0,
            'total_lines': 0,
            'total_size_bytes': 0,
            'languages': {},
            'error': f"Source directory not found: {source_code_dir}"
        }

    # Language extensions mapping
    language_extensions = {
        '.c': 'C',
        '.h': 'C',
        '.cpp': 'C++',
        '.cxx': 'C++',
        '.cc': 'C++',
        '.hpp': 'C++',
        '.hxx': 'C++',
        '.cs': 'C#',
        '.py': 'Python',
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.tsx': 'TypeScript',
        '.jsx': 'JavaScript',
        '.java': 'Java',
        '.go': 'Go',
        '.rs': 'Rust',
        '.php': 'PHP',
        '.rb': 'Ruby',
        '.swift': 'Swift',
        '.kt': 'Kotlin',
        '.scala': 'Scala',
        '.r': 'R',
        '.m': 'Objective-C',
        '.mm': 'Objective-C++',
        '.pl': 'Perl',
        '.sh': 'Shell',
        '.bash': 'Shell',
        '.ps1': 'PowerShell',
        '.sql': 'SQL',
        '.html': 'HTML',
        '.css': 'CSS',
        '.scss': 'SCSS',
        '.less': 'LESS',
        '.xml': 'XML',
        '.json': 'JSON',
        '.yaml': 'YAML',
        '.yml': 'YAML',
        '.toml': 'TOML',
        '.ini': 'INI',
        '.cfg': 'Config',
        '.conf': 'Config',
        '.v': 'Verilog',
        '.sv': 'SystemVerilog',
        '.vhd': 'VHDL',
        '.vhdl': 'VHDL'
    }

    stats = {
        'total_files': 0,
        'total_lines': 0,
        'total_size_bytes': 0,
        'languages': {}
    }

    try:
        for root, dirs, files in os.walk(source_code_dir):
            # Skip common build/cache directories
            dirs[:] = [d for d in dirs if d not in ['.git', '.svn', 'node_modules', '__pycache__', 'build', 'dist', 'target', 'bin', 'obj']]

            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                # Skip binary files and common non-source files
                if file_ext in ['.exe', '.dll', '.so', '.dylib', '.a', '.lib', '.obj', '.o', '.pyc', '.class']:
                    continue

                try:
                    # Get file size
                    file_size = os.path.getsize(file_path)
                    stats['total_size_bytes'] += file_size
                    stats['total_files'] += 1

                    # Determine language
                    language = language_extensions.get(file_ext, 'Other')

                    if language not in stats['languages']:
                        stats['languages'][language] = {
                            'files': 0,
                            'lines': 0,
                            'size_bytes': 0
                        }

                    stats['languages'][language]['files'] += 1
                    stats['languages'][language]['size_bytes'] += file_size

                    # Count lines for text files
                    if file_ext in language_extensions or file_ext in ['.txt', '.md', '.rst']:
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                line_count = sum(1 for line in f if line.strip())
                                stats['total_lines'] += line_count
                                stats['languages'][language]['lines'] += line_count
                        except (OSError, IOError, UnicodeDecodeError, PermissionError):
                            # If we can't read the file, skip line counting
                            pass

                except (OSError, IOError):
                    # Skip files we can't access
                    continue

        return stats

    except Exception as e:
        return {
            'total_files': 0,
            'total_lines': 0,
            'total_size_bytes': 0,
            'languages': {},
            'error': str(e)
        }

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

@app.get("/api/gpu")
async def api_gpu():
    """API endpoint for GPU status"""
    gpu_data = await manager.get_gpu_status()
    return JSONResponse(content=gpu_data)

@app.get("/api/metrics")
async def api_metrics():
    """API endpoint for server metrics"""
    metrics_data = await manager.get_server_metrics()
    return JSONResponse(content=metrics_data)

@app.post("/api/control/restart")
async def api_restart_server():
    """Restart the code analyzer server (if running in Docker)"""
    try:
        # This would require Docker API access or system commands
        # For now, return a placeholder response
        return JSONResponse(content={
            "success": False,
            "message": "Server restart functionality requires Docker API integration",
            "suggestion": "Use: docker restart code-analyzer-server"
        })
    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/vector_db/create")
async def api_create_vector_db(request: Request):
    """Create vector database for a new codebase with progress tracking"""
    import uuid
    operation_id = str(uuid.uuid4())

    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')
        exclude_dirs = body.get('exclude_dirs', ["build", "test", "bin", "obj", "__pycache__", ".git"])

        if not codebase_name:
            raise HTTPException(status_code=400, detail="codebase_name required")

        # Start progress tracking
        await progress_tracker.start_operation(
            operation_id,
            1,
            f"Creating Vector Database: {codebase_name}"
        )

        await progress_tracker.update_progress(
            operation_id,
            f"Creating vector database for {codebase_name}..."
        )

        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/create_codebase", json={
            "codebase_name": codebase_name,
            "exclude_dirs": exclude_dirs
        }, timeout=300)  # 5 minute timeout for processing

        # Update progress with result
        if response.status_code == 200:
            await progress_tracker.update_progress(
                operation_id,
                f"✅ Successfully created {codebase_name}",
                completed=True,
                result={"codebase": codebase_name, "status": "success"}
            )
        else:
            await progress_tracker.update_progress(
                operation_id,
                f"❌ Failed to create {codebase_name}",
                completed=True,
                result={"codebase": codebase_name, "status": "failed", "error": response.text[:100]}
            )

        # Complete operation
        await progress_tracker.complete_operation(operation_id, "completed")

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "operation_id": operation_id
        })

    except Exception as e:
        await progress_tracker.complete_operation(operation_id, "error")
        return JSONResponse(content={"success": False, "error": str(e), "operation_id": operation_id})

@app.post("/api/vector_db/rebuild")
async def api_rebuild_vector_db(request: Request):
    """Rebuild vector database for an existing codebase with progress tracking"""
    import uuid
    operation_id = str(uuid.uuid4())

    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')
        exclude_dirs = body.get('exclude_dirs', ["build", "test", "bin", "obj", "__pycache__", ".git"])

        if not codebase_name:
            raise HTTPException(status_code=400, detail="codebase_name required")

        # Start progress tracking
        await progress_tracker.start_operation(
            operation_id,
            1,
            f"Rebuilding Vector Database: {codebase_name}"
        )

        await progress_tracker.update_progress(
            operation_id,
            f"Rebuilding vector database for {codebase_name}..."
        )

        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/rebuild_codebase", json={
            "codebase_name": codebase_name,
            "exclude_dirs": exclude_dirs
        }, timeout=300)  # 5 minute timeout for processing

        # Update progress with result
        if response.status_code == 200:
            await progress_tracker.update_progress(
                operation_id,
                f"✅ Successfully rebuilt {codebase_name}",
                completed=True,
                result={"codebase": codebase_name, "status": "success"}
            )
        else:
            await progress_tracker.update_progress(
                operation_id,
                f"❌ Failed to rebuild {codebase_name}",
                completed=True,
                result={"codebase": codebase_name, "status": "failed", "error": response.text[:100]}
            )

        # Complete operation
        await progress_tracker.complete_operation(operation_id, "completed")

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "operation_id": operation_id
        })

    except Exception as e:
        await progress_tracker.complete_operation(operation_id, "error")
        return JSONResponse(content={"success": False, "error": str(e), "operation_id": operation_id})

@app.post("/api/vector_db/delete")
async def api_delete_vector_db(request: Request):
    """Delete vector database for a codebase"""
    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')

        if not codebase_name:
            raise HTTPException(status_code=400, detail="codebase_name required")

        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/delete_codebase", json={
            "codebase_name": codebase_name
        }, timeout=60)

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "operation": "delete",
            "codebase_name": codebase_name
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e), "operation": "delete"})

@app.post("/api/vector_db/reprocess")
async def api_reprocess_vector_db(request: Request):
    """Reprocess/reindex existing codebase (same as process_codebase)"""
    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')
        exclude_dirs = body.get('exclude_dirs', ["build", "test", "bin", "obj", "__pycache__", ".git"])

        if not codebase_name:
            raise HTTPException(status_code=400, detail="codebase_name required")

        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/process_codebase", json={
            "codebase_name": codebase_name,
            "exclude_dirs": exclude_dirs
        }, timeout=300)  # 5 minute timeout for processing

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "operation": "reprocess",
            "codebase_name": codebase_name
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e), "operation": "reprocess"})

@app.post("/api/gpu/refresh")
async def api_refresh_gpu():
    """Refresh GPU infrastructure discovery"""
    try:
        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/gpu_refresh", json={}, timeout=30)

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "operation": "gpu_refresh"
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e), "operation": "gpu_refresh"})

@app.get("/api/test_questions")
async def api_get_test_questions(codebase_name: str | None = None):
    """Get preconfigured test questions for a specific codebase or all codebases"""
    try:
        import json
        import os

        # Load the generated test questions
        questions_file = "codebase_test_questions.json"

        # Try multiple possible locations
        possible_locations = [
            questions_file,  # Current directory
            os.path.join("..", questions_file),  # Parent directory
            os.path.join("code_analyzer_server", questions_file),  # Code analyzer directory
            os.path.join("..", "openwebui_rag_code_server", questions_file)  # Client directory
        ]

        questions_file_path = None
        for location in possible_locations:
            if os.path.exists(location):
                questions_file_path = location
                break

        if questions_file_path:
            with open(questions_file_path, 'r', encoding='utf-8') as f:
                questions_data = json.load(f)

            # If a specific codebase is requested, return only that codebase's questions
            if codebase_name:
                if codebase_name in questions_data:
                    data = questions_data[codebase_name]
                    analysis = data.get('analysis', {})
                    questions = data.get('questions', [])

                    return JSONResponse(content={
                        "success": True,
                        "codebase_name": codebase_name,
                        "languages": analysis.get('languages', []),
                        "file_count": analysis.get('total_files', 0),
                        "questions": questions,
                        "total_questions": len(questions)
                    })
                else:
                    return JSONResponse(content={
                        "success": False,
                        "error": f"No test questions found for codebase: {codebase_name}"
                    })

            # Format for web interface (all codebases)
            formatted_questions = {}
            for codebase, data in questions_data.items():
                analysis = data.get('analysis', {})
                questions = data.get('questions', [])

                formatted_questions[codebase] = {
                    "name": codebase,
                    "languages": analysis.get('languages', []),
                    "file_count": analysis.get('total_files', 0),
                    "line_count": analysis.get('total_lines', 0),
                    "questions": questions
                }

            return JSONResponse(content={
                "success": True,
                "codebases": formatted_questions,
                "total_codebases": len(formatted_questions),
                "total_questions": sum(len(data['questions']) for data in formatted_questions.values())
            })
        else:
            return JSONResponse(content={
                "success": False,
                "error": "Test questions file not found. Run generate_test_questions.py first."
            })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/control/select_codebase")
async def api_select_codebase(request: Request):
    """Select a codebase on the analyzer server"""
    global web_session_codebase

    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')

        if not codebase_name:
            raise HTTPException(status_code=400, detail="codebase_name required")

        # Select on the analyzer server
        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/select_codebase",
                               json={"codebase_name": codebase_name}, timeout=10)

        # If successful, also store in web session and server session
        if response.status_code == 200:
            web_session_codebase = codebase_name

            # Also set on the server session storage
            try:
                requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/set_session_codebase",
                            json={"codebase_name": codebase_name}, timeout=5)
            except Exception:
                pass  # Best effort

        return JSONResponse(content={
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "web_session_codebase": web_session_codebase
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/session/codebase")
async def api_get_session_codebase():
    """Get the current web session codebase"""
    global web_session_codebase

    # Try to get from multiple sources
    current_codebase = web_session_codebase

    # If not set locally, try to get from server session
    if not current_codebase:
        try:
            response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/get_session_codebase", timeout=5)
            if response.status_code == 200:
                data = response.json()
                current_codebase = data.get('current_codebase')
                if current_codebase:
                    web_session_codebase = current_codebase  # Update local cache
        except Exception:
            pass

    return JSONResponse(content={
        "success": True,
        "current_codebase": current_codebase,
        "web_session_codebase": web_session_codebase
    })

@app.post("/api/session/codebase")
async def api_set_session_codebase(request: Request):
    """Set the current web session codebase"""
    global web_session_codebase

    try:
        body = await request.json()
        codebase_name = body.get('codebase_name')

        web_session_codebase = codebase_name

        # Also set on the server session storage
        try:
            requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/set_session_codebase",
                        json={"codebase_name": codebase_name}, timeout=5)
        except Exception:
            pass  # Best effort

        return JSONResponse(content={
            "success": True,
            "current_codebase": codebase_name,
            "web_session_codebase": web_session_codebase
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/session/clear")
async def api_clear_session():
    """Clear all session storage (web and server)"""
    global web_session_codebase

    try:
        # Clear web session
        old_web_session = web_session_codebase
        web_session_codebase = None

        # Clear server session
        try:
            requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/set_session_codebase",
                        json={"codebase_name": None}, timeout=5)
        except Exception:
            pass  # Best effort

        return JSONResponse(content={
            "success": True,
            "message": "All session storage cleared",
            "previous_web_session": old_web_session
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/debug/codebase_selection")
async def api_debug_codebase_selection():
    """Debug endpoint to check all codebase selection states"""
    global web_session_codebase

    debug_info = {
        "web_session_codebase": web_session_codebase,
        "timestamp": datetime.now().isoformat()
    }

    # Check server session
    try:
        response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/get_session_codebase", timeout=5)
        if response.status_code == 200:
            data = response.json()
            debug_info["server_session_codebase"] = data.get('current_codebase')
            debug_info["server_session_response"] = data
        else:
            debug_info["server_session_error"] = f"HTTP {response.status_code}"
    except Exception as e:
        debug_info["server_session_exception"] = str(e)

    # Check current server state
    try:
        response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/list_codebases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            debug_info["available_codebases"] = data.get('codebases', [])
        else:
            debug_info["list_codebases_error"] = f"HTTP {response.status_code}"
    except Exception as e:
        debug_info["list_codebases_exception"] = str(e)

    return JSONResponse(content={
        "success": True,
        "debug_info": debug_info
    })

@app.websocket("/ws/progress")
async def websocket_progress(websocket: WebSocket):
    """WebSocket endpoint for real-time progress updates"""
    await websocket.accept()
    progress_tracker.add_websocket(websocket)

    try:
        # Send current active operations
        for operation_id, operation in progress_tracker.active_operations.items():
            progress_data = {
                "operation_id": operation_id,
                "operation_name": operation["operation_name"],
                "total_items": operation["total_items"],
                "completed_items": operation["completed_items"],
                "current_item": operation["current_item"],
                "status": operation["status"],
                "progress_percent": (operation["completed_items"] / operation["total_items"] * 100) if operation["total_items"] > 0 else 0,
                "results": operation["results"]
            }
            await websocket.send_text(json.dumps(progress_data))

        # Keep connection alive
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        progress_tracker.remove_websocket(websocket)

@app.post("/api/vector_db/reprocess_all")
async def api_reprocess_all_vector_dbs():
    """Reprocess embeddings for all source code repositories with progress tracking"""
    import uuid
    operation_id = str(uuid.uuid4())

    try:
        # First, get the list of all codebases
        codebases_response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/list_codebases", timeout=30)

        if codebases_response.status_code != 200:
            return JSONResponse(content={
                "success": False,
                "error": "Failed to get list of codebases",
                "status_code": codebases_response.status_code
            })

        codebases_data = codebases_response.json()

        # Extract codebase names from the response
        codebase_names = []
        if isinstance(codebases_data, dict) and 'codebases' in codebases_data:
            codebase_names = [cb.get('name') for cb in codebases_data['codebases'] if cb.get('name')]
        elif isinstance(codebases_data, list):
            codebase_names = [cb.get('name') for cb in codebases_data if cb.get('name')]
        else:
            # Fallback: scan source_code directory
            import os
            source_code_dir = "source_code"
            if os.path.exists(source_code_dir):
                codebase_names = [d for d in os.listdir(source_code_dir)
                                if os.path.isdir(os.path.join(source_code_dir, d))]

        if not codebase_names:
            return JSONResponse(content={
                "success": False,
                "error": "No codebases found to reprocess"
            })

        # Start progress tracking
        await progress_tracker.start_operation(
            operation_id,
            len(codebase_names),
            "Bulk Reprocessing All Codebases"
        )

        # Reprocess each codebase
        results = {}
        successful_count = 0
        failed_count = 0

        for codebase_name in codebase_names:
            # Update progress
            await progress_tracker.update_progress(
                operation_id,
                f"Processing {codebase_name}..."
            )
            try:
                print(f"Reprocessing codebase: {codebase_name}")

                response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/process_codebase", json={
                    "codebase_name": codebase_name,
                    "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
                }, timeout=300)  # 5 minute timeout per codebase

                if response.status_code == 200:
                    result_data = {
                        "success": True,
                        "status_code": response.status_code,
                        "response": response.json()
                    }
                    results[codebase_name] = result_data
                    successful_count += 1

                    # Update progress with success
                    await progress_tracker.update_progress(
                        operation_id,
                        f"✅ Completed {codebase_name}",
                        completed=True,
                        result={"codebase": codebase_name, "status": "success"}
                    )
                else:
                    result_data = {
                        "success": False,
                        "status_code": response.status_code,
                        "error": response.text
                    }
                    results[codebase_name] = result_data
                    failed_count += 1

                    # Update progress with failure
                    await progress_tracker.update_progress(
                        operation_id,
                        f"❌ Failed {codebase_name}",
                        completed=True,
                        result={"codebase": codebase_name, "status": "failed", "error": response.text[:100]}
                    )

            except Exception as e:
                result_data = {
                    "success": False,
                    "error": str(e)
                }
                results[codebase_name] = result_data
                failed_count += 1

                # Update progress with error
                await progress_tracker.update_progress(
                    operation_id,
                    f"❌ Error {codebase_name}",
                    completed=True,
                    result={"codebase": codebase_name, "status": "error", "error": str(e)}
                )

        # Generate summary
        total_count = len(codebase_names)
        summary = f"""🔄 **Bulk Reprocessing Complete**

**Summary:**
• Total codebases: {total_count}
• Successfully reprocessed: {successful_count}
• Failed: {failed_count}
• Success rate: {(successful_count/total_count*100):.1f}%

**Processed Codebases:**"""

        for codebase_name, result in results.items():
            status = "✅" if result.get('success') else "❌"
            summary += f"\n• {status} {codebase_name}"
            if not result.get('success'):
                error = result.get('error', 'Unknown error')
                summary += f" - {error[:100]}..."

        summary += "\n\n✅ **Bulk reprocessing operation complete!**"

        # Complete the operation
        await progress_tracker.complete_operation(operation_id, "completed")

        return JSONResponse(content={
            "success": True,
            "operation": "reprocess_all",
            "summary": summary,
            "total_codebases": total_count,
            "successful_count": successful_count,
            "failed_count": failed_count,
            "results": results
        })

    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e), "operation": "reprocess_all"})

@app.post("/api/vector_db/enhanced_bulk_rebuild")
async def api_enhanced_bulk_rebuild():
    """Enhanced bulk rebuild of all vector databases with comprehensive statistics"""
    import uuid
    operation_id = str(uuid.uuid4())

    try:
        # Start progress tracking
        await progress_tracker.start_operation(
            operation_id,
            1,
            "Enhanced Bulk Rebuild All Codebases"
        )

        await progress_tracker.update_progress(
            operation_id,
            "Starting enhanced bulk rebuild with comprehensive statistics..."
        )

        # Use the new enhanced bulk rebuild API endpoint
        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/bulk_rebuild_all_codebases", json=[
            "build", "test", "bin", "obj", "__pycache__", ".git", ".svn", "node_modules", "dist"
        ], timeout=600)  # 10 minute timeout for all codebases

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                # Extract enhanced statistics
                summary = data.get('summary', 'Bulk rebuild completed')
                total_codebases = data.get('total_codebases', 0)
                successful_count = data.get('successful_count', 0)
                failed_count = data.get('failed_count', 0)
                total_time = data.get('total_time', 0)
                results = data.get('results', [])

                # Format results for web interface
                formatted_results = {}
                for result in results:
                    codebase_name = result.get('codebase', 'unknown')
                    if result.get('success'):
                        stats = result.get('stats', {})
                        formatted_results[codebase_name] = {
                            "success": True,
                            "chunks": stats.get('chunks', 0),
                            "languages": stats.get('languages', []),
                            "tree_sitter_info": stats.get('tree_sitter_info', 'unknown'),
                            "processing_time": stats.get('processing_time', 'unknown'),
                            "chunks_deleted": stats.get('chunks_deleted', 0)
                        }
                    else:
                        formatted_results[codebase_name] = {
                            "success": False,
                            "error": result.get('error', 'Unknown error')
                        }

                # Complete the operation
                await progress_tracker.complete_operation(operation_id, "completed")

                return JSONResponse(content={
                    "success": True,
                    "operation": "enhanced_bulk_rebuild",
                    "summary": summary,
                    "total_codebases": total_codebases,
                    "successful_count": successful_count,
                    "failed_count": failed_count,
                    "total_time": total_time,
                    "results": formatted_results,
                    "enhanced_features": {
                        "tree_sitter_integration": True,
                        "function_level_chunking": True,
                        "quality_analysis": True,
                        "complexity_analysis": True,
                        "comprehensive_statistics": True
                    }
                })
            else:
                error = data.get('error', 'Unknown error')
                await progress_tracker.complete_operation(operation_id, "failed")
                return JSONResponse(content={
                    "success": False,
                    "error": f"Bulk rebuild failed: {error}",
                    "operation": "enhanced_bulk_rebuild"
                })
        else:
            await progress_tracker.complete_operation(operation_id, "failed")
            return JSONResponse(content={
                "success": False,
                "error": f"HTTP {response.status_code}",
                "operation": "enhanced_bulk_rebuild"
            })

    except requests.exceptions.Timeout:
        await progress_tracker.complete_operation(operation_id, "failed")
        return JSONResponse(content={
            "success": False,
            "error": "Timeout after 10 minutes",
            "operation": "enhanced_bulk_rebuild"
        })
    except Exception as e:
        await progress_tracker.complete_operation(operation_id, "failed")
        return JSONResponse(content={
            "success": False,
            "error": str(e),
            "operation": "enhanced_bulk_rebuild"
        })

@app.post("/api/test/query")
async def api_test_query(request: Request):
    """Test a query against the analyzer server"""
    global web_session_codebase

    try:
        body = await request.json()
        query = body.get('query', 'test query')
        codebase_name = body.get('codebase_name')  # No default - require explicit selection
        n_results = body.get('n_results', 15)  # Increased from 3 to 15 for better analysis

        # If no codebase specified, try to get from session storage
        if not codebase_name:
            print("🔧 [TEST QUERY] No codebase specified, checking sessions...", flush=True)

            # First try web session
            if web_session_codebase:
                codebase_name = web_session_codebase
                print(f"🔧 [TEST QUERY] Using web session: '{codebase_name}'", flush=True)
            else:
                # Then try server session
                try:
                    session_response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/get_session_codebase", timeout=5)
                    if session_response.status_code == 200:
                        session_data = session_response.json()
                        server_codebase = session_data.get('current_codebase')
                        print(f"🔧 [TEST QUERY] Server session has: '{server_codebase}'", flush=True)

                        if server_codebase:
                            codebase_name = server_codebase
                            web_session_codebase = server_codebase  # Update local cache
                            print(f"🔧 [TEST QUERY] Using server session: '{codebase_name}'", flush=True)
                except Exception as e:
                    print(f"🔧 [TEST QUERY] Error getting server session: {e}", flush=True)
                    pass

        # If still no codebase, return error
        if not codebase_name:
            return JSONResponse(content={
                "success": False,
                "error": "No codebase selected. Please select a codebase first.",
                "query": query,
                "suggestion": "Use the codebase selection interface to choose a codebase"
            })
        
        # Check if the codebase is already selected to avoid unnecessary reselection
        current_selection_response = requests.get(f"{CODE_ANALYZER_BASE_URL}/tools/get_session_codebase", timeout=5)
        current_codebase = None
        if current_selection_response.status_code == 200:
            current_data = current_selection_response.json()
            current_codebase = current_data.get('current_codebase')

        # Only select codebase if it's different from current selection
        if current_codebase != codebase_name:
            print(f"🔧 [TEST QUERY] Selecting codebase: '{current_codebase}' → '{codebase_name}'", flush=True)
            select_response = requests.post(f"{CODE_ANALYZER_BASE_URL}/tools/select_codebase",
                                          json={"codebase_name": codebase_name}, timeout=5)

            # Check if codebase selection was successful
            if select_response.status_code != 200:
                return JSONResponse(content={
                    "success": False,
                    "status_code": select_response.status_code,
                    "error": f"Failed to select codebase '{codebase_name}': {select_response.text}",
                    "query": query,
                    "codebase": codebase_name
            })

        # Use search endpoint to get actual code chunks
        response = requests.post(f"{CODE_ANALYZER_BASE_URL}/search", json={
            "query": query,
            "codebase_name": codebase_name,
            "n_results": n_results
        }, timeout=15)

        if response.status_code == 200:
            search_data = response.json()
            # Format the response to show actual code content
            formatted_results = []

            if 'results' in search_data:
                for result in search_data['results'][:n_results]:
                    metadata = result.get('metadata', {})

                    # Try multiple ways to get the file path
                    file_path = (
                        metadata.get('file_path') or
                        metadata.get('file') or
                        metadata.get('source_file') or
                        metadata.get('filename') or
                        'Unknown file'
                    )

                    # Try multiple ways to get the language
                    language = (
                        metadata.get('language') or
                        metadata.get('file_type') or
                        metadata.get('lang') or
                        'Unknown'
                    )

                    # Get content with smart truncation for display
                    content = result.get('content', 'No content available')

                    # For code content, show more but still limit for web display
                    if len(content) > 5000:  # Much larger limit
                        # Try to find a good break point (end of function, etc.)
                        truncate_at = 5000

                        # Look for natural break points in the last 500 chars
                        search_area = content[4500:5000]
                        for break_point in ['\n}\n', '\n\n', ';\n']:
                            pos = search_area.rfind(break_point)
                            if pos > 0:
                                truncate_at = 4500 + pos + len(break_point)
                                break

                        content = content[:truncate_at] + "\n\n... [Content truncated for display - full content available in search results] ..."

                    formatted_result = {
                        "content": content,
                        "metadata": metadata,
                        "file": file_path,
                        "language": language,
                        "raw_result": result  # Include raw result for debugging
                    }
                    formatted_results.append(formatted_result)

            return JSONResponse(content={
                "success": True,
                "status_code": response.status_code,
                "query": query,
                "codebase": codebase_name,
                "results_count": len(formatted_results),
                "results": formatted_results,
                "raw_response": search_data  # Include raw response for debugging
            })
        else:
            return JSONResponse(content={
                "success": False,
                "status_code": response.status_code,
                "error": response.text,
                "query": query,
                "codebase": codebase_name
            })

    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "error": str(e),
            "query": query if 'query' in locals() else "unknown",
            "operation": "test_query"
        })

# === HYBRID CODE ANALYSIS ENDPOINTS ===

@app.get("/api/hybrid/models")
async def api_get_hybrid_models():
    """Get available models for hybrid analysis"""
    if not HYBRID_ANALYZER_AVAILABLE or not hybrid_analyzer:
        return JSONResponse(content={
            "success": False,
            "error": "Hybrid analyzer not available",
            "models": {"local_models": [], "remote_models": []}
        })

    try:
        models = hybrid_analyzer.get_available_models()
        return JSONResponse(content={
            "success": True,
            "models": models
        })
    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "error": str(e),
            "models": {"local_models": [], "remote_models": []}
        })

@app.post("/api/hybrid/analyze")
async def api_hybrid_analyze(request: Request):
    """Perform hybrid code analysis with model selection"""
    if not HYBRID_ANALYZER_AVAILABLE or not hybrid_analyzer:
        return JSONResponse(content={
            "success": False,
            "error": "Hybrid analyzer not available"
        })

    try:
        body = await request.json()
        query = body.get('query', '')
        model = body.get('model', 'auto')
        prefer_fast = body.get('prefer_fast', False)

        if not query:
            return JSONResponse(content={
                "success": False,
                "error": "No query provided"
            })

        # Record start time
        start_time = time.time()

        # Perform analysis
        result = hybrid_analyzer.analyze_code(query, model=model, prefer_fast=prefer_fast)

        # Calculate response time
        response_time = time.time() - start_time
        result['response_time'] = round(response_time, 2)

        return JSONResponse(content=result)

    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "error": str(e)
        })

@app.post("/api/hybrid/benchmark")
async def api_hybrid_benchmark(request: Request):
    """Benchmark all available models"""
    if not HYBRID_ANALYZER_AVAILABLE or not hybrid_analyzer:
        return JSONResponse(content={
            "success": False,
            "error": "Hybrid analyzer not available"
        })

    try:
        body = await request.json()
        query = body.get('query', 'List all available codebases')

        results = hybrid_analyzer.benchmark_models(query)

        return JSONResponse(content={
            "success": True,
            "benchmark_results": results,
            "query": query
        })

    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "error": str(e)
        })


    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})

if __name__ == "__main__":
    print(f"🌐 Starting Code Analyzer Web Management Server on port {WEB_MANAGEMENT_PORT}")
    print(f"📊 Managing Code Analyzer Server at: {CODE_ANALYZER_BASE_URL}")
    print(f"🔗 Web Interface: http://localhost:{WEB_MANAGEMENT_PORT}")

    if HYBRID_ANALYZER_AVAILABLE and hybrid_analyzer:
        print(f"🚀 Hybrid Code Analysis: ENABLED")
        print(f"   • Local models via OpenWebUI")
        print(f"   • Remote models via direct Ollama")
        print(f"   • API endpoints: /api/hybrid/*")
    else:
        print(f"⚠️  Hybrid Code Analysis: DISABLED")

    uvicorn.run(app, host="0.0.0.0", port=WEB_MANAGEMENT_PORT)
