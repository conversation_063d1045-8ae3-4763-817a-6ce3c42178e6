# code_preprocessor.py - New Framework Implementation
"""
Complete replacement of CodePreprocessor using Language-Agnostic Framework
Maintains backward compatibility while providing enhanced capabilities
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, List, Any
import logging

# Import new framework components
from framework_integration import IntegratedCodeAnalysisSystem
from language_registry import create_language_registry
from chunk_system import create_default_chunk_registry, Chunk

logger = logging.getLogger(__name__)

class MultiLanguageCodeProcessor:
    """
    New implementation using Language-Agnostic Framework
    Maintains backward compatibility while providing enhanced capabilities
    """
    
    def __init__(self, repo_path):
        self.repo_path = Path(repo_path)
        
        # Initialize new framework components
        self.framework = create_language_registry()
        self.chunk_registry = create_default_chunk_registry()
        self.integrated_system = IntegratedCodeAnalysisSystem()
        
        # Initialize for backward compatibility
        self.semantic_patterns = self._init_semantic_patterns()
        self.complexity_thresholds = {
            'high_complexity_lines': 50,
            'high_complexity_params': 5,
            'high_complexity_branches': 5
        }
        
        # Print status using new framework
        supported_languages = self.framework.get_supported_languages()
        print(f"Using Language-Agnostic Framework for: {', '.join(sorted(supported_languages))}")
        print(f"Total supported languages: {len(supported_languages)}")
        
        logger.info(f"Initialized MultiLanguageCodeProcessor with new framework for {repo_path}")
    
    def process_repository(self, exclude_dirs=None):
        """Process entire repository using new Language-Agnostic Framework"""
        if exclude_dirs is None:
            exclude_dirs = {'.git', '.svn', 'build', 'dist', '__pycache__', 'node_modules', 
                           'bin', 'obj', '.vs', '.vscode', '.idea', 'packages'}
        
        # Use async processing with new framework
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            return loop.run_until_complete(self._process_repository_async(exclude_dirs))
        finally:
            # Don't close the loop if it was already running
            if not loop.is_running():
                loop.close()
    
    async def _process_repository_async(self, exclude_dirs=None):
        """Async implementation using new Language-Agnostic Framework"""
        
        logger.info(f"Starting repository analysis with new framework: {self.repo_path}")
        
        try:
            # Use new framework to analyze the codebase
            result = await self.integrated_system.analyze_codebase(
                str(self.repo_path), 
                file_patterns=["**/*"]  # Process all files
            )
            
            if not result["success"]:
                logger.error("Framework analysis failed, falling back to basic processing")
                return self._fallback_processing(exclude_dirs)
            
            # Convert framework results to old format for backward compatibility
            chunks = self._convert_framework_results_to_chunks(result)
            
            # Print summary
            print("\nProcessing complete using Language-Agnostic Framework:")
            print(f"  Total files processed: {result['total_files']}")
            print(f"  Total chunks generated: {len(chunks)}")
            print(f"  Processing time: {result['pipeline_results']['chunk_generation'].execution_time:.2f}s")
            
            # Show language breakdown
            language_stats = self._get_language_statistics(chunks)
            print(f"  Languages detected: {', '.join(language_stats.keys())}")
            
            return chunks
            
        except Exception as e:
            logger.error(f"Framework processing failed: {e}")
            print(f"Error with new framework: {e}")
            print("Falling back to basic processing...")
            return self._fallback_processing(exclude_dirs)
    
    def _convert_framework_results_to_chunks(self, framework_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert new framework results to old chunk format for backward compatibility"""
        
        chunks = []
        
        # Extract pipeline results
        pipeline_results = framework_result.get("pipeline_results", {})
        
        # Get chunk generation results
        chunk_generation = pipeline_results.get("chunk_generation")
        if chunk_generation and chunk_generation.status.value == "completed":
            generated_chunks = chunk_generation.output_data.get("generated_chunks", [])
            
            for framework_chunk in generated_chunks:
                # Convert to old format
                old_chunk = self._framework_chunk_to_old_format(framework_chunk)
                chunks.append(old_chunk)
        
        # Get code analysis results for additional chunks
        code_analysis = pipeline_results.get("code_analysis")
        if code_analysis and code_analysis.status.value == "completed":
            language_contexts = code_analysis.output_data.get("language_contexts", {})
            
            # Create additional chunks from language contexts
            for file_path, context in language_contexts.items():
                if not any(chunk["metadata"]["filepath"] == file_path for chunk in chunks):
                    # Create a basic chunk for files not covered by framework chunks
                    basic_chunk = self._create_basic_chunk_from_context(context)
                    chunks.append(basic_chunk)
        
        return chunks
    
    def _framework_chunk_to_old_format(self, framework_chunk: Chunk) -> Dict[str, Any]:
        """Convert a framework chunk to old format"""

        # Extract metadata
        metadata = framework_chunk.metadata
        content = framework_chunk.content

        # Create old-style chunk
        old_chunk: Dict[str, Any] = {
            "content": content.primary_content,
            "metadata": {
                "filepath": metadata.source_files[0] if metadata.source_files else "",
                "language": metadata.language,
                "chunk_type": metadata.chunk_type,
                "start_line": 1,  # Framework doesn't track line numbers yet
                "end_line": len(content.primary_content.splitlines()),
                "function_name": "",  # Would need extraction from content
                "class_name": "",  # Would need extraction from content
                "complexity": self._estimate_complexity_from_content(content.primary_content),
                "semantic_tags": list(metadata.tags),
                "quality_score": metadata.quality_score,
                "processing_time": metadata.processing_time,
                "enhanced": True,  # Mark as processed by new framework
                "chunk_id": metadata.chunk_id,
                "created_at": metadata.created_at.isoformat(),
                "priority": metadata.priority.value,
                "relationships": metadata.relationships,
                "custom_attributes": metadata.custom_attributes
            }
        }

        # Add summary if available
        if content.summary:
            old_chunk["metadata"]["summary"] = content.summary

        # Add keywords if available
        if content.keywords:
            old_chunk["metadata"]["keywords"] = list(content.keywords)

        return old_chunk
    
    def _create_basic_chunk_from_context(self, context) -> Dict[str, Any]:
        """Create a basic chunk from language context"""
        
        return {
            "content": context.content,
            "metadata": {
                "filepath": context.file_path,
                "language": context.language,
                "chunk_type": "code_implementation",
                "start_line": 1,
                "end_line": len(context.content.splitlines()),
                "function_name": "",
                "class_name": "",
                "complexity": self._estimate_complexity_from_content(context.content),
                "semantic_tags": ["basic_processing"],
                "quality_score": 0.5,  # Default quality score
                "processing_time": 0.1,  # Minimal processing time
                "enhanced": False,  # Basic processing
                "relationships": [rel.__dict__ for rel in context.relationships]
            }
        }
    
    def _estimate_complexity_from_content(self, content: str) -> str:
        """Estimate complexity based on content characteristics"""
        lines = len(content.splitlines())
        if lines < 20:
            return "low"
        elif lines < 100:
            return "medium"
        else:
            return "high"
    
    def _get_language_statistics(self, chunks: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get statistics about languages in chunks"""
        stats: Dict[str, int] = {}
        for chunk in chunks:
            lang = chunk["metadata"].get("language", "unknown")
            stats[lang] = stats.get(lang, 0) + 1
        return stats
    
    def _fallback_processing(self, exclude_dirs=None) -> List[Dict[str, Any]]:
        """Fallback to basic processing if framework fails"""
        logger.warning("Using fallback processing - limited functionality")
        
        # Basic file discovery
        supported_extensions = self.framework.get_supported_extensions()
        source_files = []
        
        for root, dirs, files in os.walk(self.repo_path):
            # Remove excluded directories
            if exclude_dirs:
                dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                filepath = Path(root) / file
                if filepath.suffix.lower() in supported_extensions:
                    source_files.append(filepath)
        
        # Create basic chunks
        chunks = []
        for filepath in source_files:
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Get language from framework
                processor = self.framework.get_processor_for_file(str(filepath))
                language = processor.get_language_name() if processor else "unknown"
                
                chunk = {
                    "content": content,
                    "metadata": {
                        "filepath": str(filepath),
                        "language": language,
                        "chunk_type": "fallback_processing",
                        "start_line": 1,
                        "end_line": len(content.splitlines()),
                        "function_name": "",
                        "class_name": "",
                        "complexity": self._estimate_complexity_from_content(content),
                        "semantic_tags": ["fallback"],
                        "quality_score": 0.3,
                        "processing_time": 0.05,
                        "enhanced": False
                    }
                }
                chunks.append(chunk)
                
            except Exception as e:
                logger.error(f"Error processing {filepath}: {e}")
                continue
        
        print(f"Fallback processing complete: {len(chunks)} chunks generated")
        return chunks

    # Backward compatibility methods
    def _init_semantic_patterns(self):
        """Initialize semantic patterns for backward compatibility"""
        # Import the comprehensive patterns
        from semantic_patterns import semantic_registry

        # Return the comprehensive patterns in the old format for compatibility
        return semantic_registry.patterns

    def process_file(self, filepath):
        """Process a single file - compatibility method"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Get processor for file
            processor = self.framework.get_processor_for_file(str(filepath))
            language = processor.get_language_name() if processor else "unknown"

            # Create basic chunk
            chunk = {
                "content": content,
                "metadata": {
                    "filepath": str(filepath),
                    "language": language,
                    "chunk_type": "file_processing",
                    "start_line": 1,
                    "end_line": len(content.splitlines()),
                    "function_name": "",
                    "class_name": "",
                    "complexity": self._estimate_complexity_from_content(content),
                    "semantic_tags": ["file_processing"],
                    "quality_score": 0.5,
                    "processing_time": 0.1,
                    "enhanced": False
                }
            }

            return [chunk]

        except Exception as e:
            logger.error(f"Error processing file {filepath}: {e}")
            return []

    def get_parser_for_file(self, filepath):
        """Get parser for file - compatibility method"""
        processor = self.framework.get_processor_for_file(str(filepath))
        if processor:
            return (None, None, processor.get_language_name())  # Mock parser format
        return (None, None, "unknown")

    def extract_functions(self, source_code, filepath):
        """Extract functions - compatibility method"""
        # Basic function extraction using regex
        import re

        functions = []
        lines = source_code.splitlines()

        # Simple function patterns for different languages
        patterns = {
            'python': r'^\s*def\s+(\w+)\s*\(',
            'javascript': r'^\s*function\s+(\w+)\s*\(',
            'c': r'^\s*\w+\s+(\w+)\s*\([^)]*\)\s*{',
            'cpp': r'^\s*\w+\s+(\w+)\s*\([^)]*\)\s*{',
            'java': r'^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(',
            'csharp': r'^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\('
        }

        processor = self.framework.get_processor_for_file(str(filepath))
        language = processor.get_language_name() if processor else "unknown"

        pattern = patterns.get(language, patterns.get('c'))  # Default to C pattern

        for i, line in enumerate(lines):
            match = re.match(pattern, line)
            if match:
                functions.append({
                    'name': match.group(1),
                    'content': line,
                    'start_line': i + 1,
                    'end_line': i + 1,
                    'filepath': str(filepath),
                    'metadata': {
                        'type': 'function',
                        'function_name': match.group(1),
                        'language': language,
                        'complexity': 'low'
                    }
                })

        return functions

# Maintain backward compatibility
CppCodeProcessor = MultiLanguageCodeProcessor
CodePreprocessor = MultiLanguageCodeProcessor  # Additional alias
