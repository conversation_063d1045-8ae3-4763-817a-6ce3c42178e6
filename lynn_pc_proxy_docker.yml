# Docker Compose for lynn-pc tool proxy
version: '3.8'

services:
  tool-proxy:
    image: nginx:alpine
    ports:
      - "5002:80"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - TARGET_SERVER=************:5002
    restart: unless-stopped
    container_name: lynn-pc-tool-proxy
    
  # Alternative: Python proxy
  python-proxy:
    build:
      context: .
      dockerfile: Dockerfile.proxy
    ports:
      - "5003:5002"  # Alternative port
    environment:
      - HOME_AI_SERVER=************:5002
    restart: unless-stopped
    container_name: lynn-pc-python-proxy
    profiles: ["python"]  # Only start with --profile python

---
# Dockerfile.proxy content:
# FROM python:3.9-slim
# WORKDIR /app
# COPY lynn_pc_tool_proxy.py .
# RUN pip install flask requests
# EXPOSE 5002
# CMD ["python", "lynn_pc_tool_proxy.py"]

---
# nginx-proxy.conf content:
# server {
#     listen 80;
#     location / {
#         proxy_pass http://************:5002;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     }
# }
