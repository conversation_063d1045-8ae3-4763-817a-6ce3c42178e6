# OpenWebUI Code Analyzer Tool - Testing Suite

This directory contains comprehensive testing tools for the OpenWebUI Code Analyzer Tool integration.

## 🧪 Test Files

### `test_openwebui_tool_comprehensive.py`
**Purpose**: Long-term integration and regression testing
- **Scope**: Complete end-to-end OpenWebUI tool functionality
- **Use Cases**: 
  - Continuous integration validation
  - Production readiness verification
  - Regression testing after updates
  - Long-term integration monitoring

**Test Coverage**:
- ✅ Tool availability in OpenWebUI
- ✅ Basic tool functions (list, status, framework)
- ✅ Codebase management (select, stats, multi-codebase)
- ✅ Code analysis queries (context, search, smart queries)
- ✅ Cache and performance features
- ✅ Automatic context injection
- ✅ Vector database operations
- ✅ End-to-end workflow validation

**Usage**:
```bash
python test_openwebui_tool_comprehensive.py
```

## 🔧 Configuration

### Prerequisites
- OpenWebUI server running on `http://home-ai-server.local:8080`
- Code analyzer server running on `http://home-ai-server.local:5002`
- Valid API key configured in test file
- Code analyzer tool installed in OpenWebUI

### API Configuration
Update these values in the test files if needed:
```python
self.openwebui_url = "http://home-ai-server.local:8080"
self.api_key = "sk-320242e0335e45a4b1fa4752f758f758f9ab"
self.tool_id = "code_analyzer_tool"
```

## 📊 Test Results Interpretation

### Success Criteria
- **100% Suite Success**: All test suites pass
- **>90% Individual Tests**: Most individual tests pass
- **End-to-End Workflow**: Complete workflow functions properly

### Common Issues
1. **Tool Not Found**: Code analyzer tool not installed in OpenWebUI
2. **Connection Errors**: Server connectivity issues
3. **API Key Issues**: Invalid or expired API key
4. **Server URL Problems**: Incorrect server configuration

## 🚀 Integration with CI/CD

### Automated Testing
Add to your CI/CD pipeline:
```bash
# Run comprehensive integration test
python test_openwebui_tool_comprehensive.py
if [ $? -eq 0 ]; then
    echo "✅ OpenWebUI tool integration test passed"
else
    echo "❌ OpenWebUI tool integration test failed"
    exit 1
fi
```

### Regression Testing
Run after any changes to:
- OpenWebUI tool code
- Code analyzer server
- Framework integration
- API endpoints

## 📋 Maintenance

### Regular Testing Schedule
- **Daily**: Automated CI/CD runs
- **Weekly**: Manual comprehensive testing
- **Before Releases**: Full regression testing
- **After Updates**: Integration validation

### Test Updates
Update tests when:
- New tool functions are added
- API endpoints change
- Server configuration changes
- New features are implemented

## 🎯 Best Practices

1. **Keep Tests Updated**: Maintain tests alongside code changes
2. **Monitor Results**: Track test results over time
3. **Fix Failures Quickly**: Address test failures promptly
4. **Document Changes**: Update test documentation with changes
5. **Version Control**: Keep test files in version control

## 🔗 Related Files

- `open_webui_code_analyzer_tool.py` - Main tool implementation
- `main.py` - Code analyzer server
- `web_management_server.py` - Web management interface

## 📞 Support

For issues with testing:
1. Check server connectivity
2. Verify API configuration
3. Review server logs
4. Check tool installation in OpenWebUI
5. Validate network connectivity

---

**Note**: These tests are designed for long-term maintenance and should be preserved for ongoing integration validation and regression testing.
