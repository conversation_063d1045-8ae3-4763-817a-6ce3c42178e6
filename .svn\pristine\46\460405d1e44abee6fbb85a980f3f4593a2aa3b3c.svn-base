# OpenWebUI Tool Installation Guide

## 🔧 Issue Identified
The codebase selection issue is caused by the tool not being properly installed in OpenWebUI. The tool file exists but OpenWebUI doesn't recognize it because tools must be installed through the interface.

## 📋 Installation Steps

### Step 1: Access OpenWebUI Interface
1. Open browser and go to: http://home-ai-server.local:8080
2. Log in to your OpenWebUI instance

### Step 2: Install the Tool
1. Go to **Workspace** → **Tools**
2. Click **"+"** or **"Add Tool"** button
3. Choose **"Import from File"** or **"Manual Installation"**
4. Copy the contents of `open_webui_code_analyzer_tool.py`
5. Paste into the tool editor
6. Click **"Save"** or **"Install"**

### Step 3: Enable the Tool for Models
1. Go to **Workspace** → **Models**
2. Find **llama3:latest** model
3. Click the **edit** (✏️) icon
4. Scroll to **"Tools"** section
5. Check the box for **"Code Analysis Tool"** or **"code_analyzer_tools"**
6. Click **"Save"**

### Step 4: Test the Tool
1. Start a new chat with **llama3:latest**
2. Try: `"select codebase utils"`
3. Should now work properly and return actual codebase selection

## 🔍 Verification Steps

### Check Tool Installation
- Go to Workspace → Tools
- Verify "Code Analysis Tool" appears in the list
- Status should show as "Enabled" or "Active"

### Check Model Configuration
- Go to Workspace → Models → llama3:latest
- Verify the tool is checked in the Tools section

### Test Functionality
- `"select codebase utils"` → Should select actual utils codebase
- `"list codebases"` → Should show actual indexed codebases
- `"search code memory allocation"` → Should return actual code from utils

## 🚨 Common Issues

### Tool Not Appearing
- Check if the Python syntax is valid
- Ensure the tool class is named `Tools`
- Verify the docstring header has required fields

### Tool Not Working
- Check if it's enabled for the correct model
- Verify the server URL in valves is correct
- Check OpenWebUI logs for errors

### API Calls Failing
- Ensure `tool_ids: ["code_analyzer_tool"]` matches the actual tool ID
- Check that the tool is enabled for the model being used

## 🎯 Expected Results After Fix

1. **Codebase Selection**: `"select codebase utils"` will return:
   ```
   ✅ Selected Enhanced Codebase: utils
   📊 Enhanced Statistics:
   - Status: ready_enhanced
   - Documents: 479
   - Languages: C++, C, C/C++, C#
   ```

2. **Code Search**: Will return actual code from the utils codebase instead of generic responses

3. **All Functions**: Will work properly with actual codebase data
