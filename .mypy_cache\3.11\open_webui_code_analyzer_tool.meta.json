{"data_mtime": 1752929811, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 729, 1899, 4016, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "aiohttp", "typing", "pydantic", "<PERSON><PERSON><PERSON>", "time", "pickle", "os", "threading", "json", "dataclasses", "collections", "re", "traceback", "datetime", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "_io", "_pickle", "_ssl", "_thread", "_typeshed", "abc", "aiohttp.abc", "aiohttp.client", "aiohttp.client_reqrep", "aiohttp.client_ws", "aiohttp.connector", "aiohttp.helpers", "aiohttp.http_writer", "aiohttp.tracing", "annotated_types", "asyncio", "asyncio.events", "enum", "http", "http.cookiejar", "http.cookies", "io", "json.decoder", "json.encoder", "multidict", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "requests.auth", "requests.exceptions", "requests.models", "requests.structures", "ssl", "types", "typing_extensions", "yarl", "yarl._url"], "hash": "1e5123a37b09c2a75dd2b039d01ab71c6235c771", "id": "open_webui_code_analyzer_tool", "ignore_all": false, "interface_hash": "ce6e03e85c4adaeb3b7c68a7b186fc1d36505147", "mtime": 1752929808, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\open_webui_code_analyzer_tool.py", "plugin_data": null, "size": 182933, "suppressed": [], "version_id": "1.15.0"}