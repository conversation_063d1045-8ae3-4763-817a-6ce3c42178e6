# OpenWebUI Auto-Tester Category Selection Guide

## 🎯 Overview

The updated `openwebui_auto_tester.py` now supports selective testing by category, allowing you to run specific types of tests instead of all 36 tests at once.

## 📋 Available Categories

| # | Category | Tests | Description |
|---|----------|-------|-------------|
| 1 | **System Status** | 4 | Health checks, status monitoring |
| 2 | **Management** | 5 | Codebase selection, statistics |
| 3 | **Code Search** | 5 | Basic code searches, function finding |
| 4 | **AI Analysis** | 5 | Complex AI-powered code analysis |
| 5 | **Language Search** | 4 | Language-specific searches (C++, C, etc.) |
| 6 | **Format Test** | 4 | Context format selection testing |
| 7 | **Edge Case** | 4 | Error handling, boundary conditions |
| 8 | **Quick Test** | 5 | Essential functionality verification |

**Total: 36 tests across 8 categories**

## 🚀 Usage Examples

### Run the Auto-Tester
```bash
python debug-testing/openwebui_auto_tester.py
```

**Note:** The script uses `llama3:latest` by default (the model with tool enabled). You can specify a different model when prompted.

### Selection Options

#### **Single Category**
```
Select categories to test: 1
# Runs only "System Status" (4 tests)
```

#### **Multiple Categories**
```
Select categories to test: 1,3,8
# Runs "System Status", "Code Search", and "Quick Test" (14 tests)
```

#### **Range of Categories**
```
Select categories to test: 1-4
# Runs categories 1 through 4 (19 tests)
```

#### **All Categories**
```
Select categories to test: all
# or
Select categories to test: 9
# Runs all categories (36 tests)
```

#### **Preview Before Running**
```
Select categories to test: preview 1
# Shows all prompts in "System Status" category
```

## 🔍 Category Details

### **1. System Status (4 tests)**
- Basic status check
- Detailed status  
- System health
- Comprehensive status

**Use for:** Verifying tool health monitoring

### **2. Management (5 tests)**
- List codebases
- Select codebase
- Get statistics
- Switch codebase
- Show available

**Use for:** Testing codebase management functions

### **3. Code Search (5 tests)**
- Memory allocation search
- Error handling search
- Networking search
- Security search
- Threading search

**Use for:** Basic code search functionality

### **4. AI Analysis (5 tests)**
- Memory management analysis
- Security analysis
- Architecture analysis
- Error handling analysis
- Performance analysis

**Use for:** AI-powered code analysis features

### **5. Language Search (4 tests)**
- C++ classes
- C functions
- Struct definitions
- Template code

**Use for:** Language-specific search capabilities

### **6. Format Test (4 tests)**
- Simple search (clean format)
- Basic function search
- Complex analysis (detailed format)
- Comparative analysis

**Use for:** Context format selection verification

### **7. Edge Case (4 tests)**
- Invalid codebase
- Non-code query
- Empty query
- Very long query

**Use for:** Error handling and boundary testing

### **8. Quick Test (5 tests)**
- Basic status
- List codebases
- Select utils
- Simple search
- Basic AI question

**Use for:** Fast essential functionality check

## 💡 Recommended Testing Workflows

### **🚀 Quick Verification (5 tests)**
```bash
# Run Quick Test category
Select categories to test: 8
```

### **🔧 Core Functionality (14 tests)**
```bash
# Run System Status + Management + Quick Test
Select categories to test: 1,2,8
```

### **🔍 Search Testing (9 tests)**
```bash
# Run Code Search + Language Search
Select categories to test: 3,5
```

### **🧠 AI Features (5 tests)**
```bash
# Run AI Analysis only
Select categories to test: 4
```

### **🧪 Comprehensive Testing (36 tests)**
```bash
# Run everything
Select categories to test: all
```

### **🔍 Preview First, Then Run**
```bash
# Preview a category
Select categories to test: preview 1

# Then run it
Select categories to test: 1
```

## 📊 Expected Results

### **✅ Success Indicators:**
- **System Status**: Formatted health reports with emojis
- **Management**: Codebase lists, successful selections
- **Code Search**: Relevant code snippets returned
- **AI Analysis**: Meaningful insights about code
- **Language Search**: Language-specific results
- **Format Test**: Appropriate context format selection
- **Edge Case**: Graceful error handling
- **Quick Test**: All basic functions working

### **❌ Common Issues:**
- Timeout errors (increase delay between tests)
- Connection failures (check OpenWebUI server)
- Empty responses (verify tool installation)
- Generic responses (check Code Analysis server connection)

## 🎯 Pro Tips

1. **Start with Quick Test** to verify basic functionality
2. **Use preview** to see what prompts will be sent
3. **Test incrementally** - start with 1-2 categories
4. **Check results file** for detailed analysis
5. **Adjust delays** if getting timeout errors
6. **Run Edge Cases last** to avoid affecting other tests

## 🔧 Troubleshooting

### **Connection Issues**
```bash
# Test connection first
curl http://home-ai-server.local:8080/health
```

### **Tool Not Responding**
- Verify tool is installed in OpenWebUI
- **Check tool is enabled for llama3:latest model**
- Ensure Code Analysis server is running
- Try different model if tool not enabled for current model

### **Timeout Errors**
- Increase delay between tests (default: 2s)
- Check server performance
- Run fewer tests at once

The category selection feature makes testing much more targeted and efficient! 🚀
