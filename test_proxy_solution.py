#!/usr/bin/env python3
"""
Test the proxy solution for remote tool execution
"""

import requests

def test_proxy_connectivity():
    """Test if proxy is working on lynn-pc"""
    print("🔍 Testing Proxy Connectivity")
    print("-" * 30)
    
    # Test from home-ai-server to lynn-pc proxy
    lynn_pc_proxy = "http://192.168.0.32:5002"
    
    try:
        response = requests.get(f"{lynn_pc_proxy}/health", timeout=10)
        if response.status_code == 200:
            print("✅ lynn-pc proxy is accessible from home-ai-server")
            data = response.json()
            print(f"   Proxy status: {data}")
            return True
        else:
            print(f"❌ Proxy error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot reach lynn-pc proxy: {e}")
        return False

def test_tool_with_proxy():
    """Test tool execution with proxy"""
    print(f"\n🧪 Testing Tool with Proxy")
    print("-" * 30)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test with LYNN-PC model (should now use localhost:5002 via proxy)
    payload = {
        "model": "LYNN-PC.llama3:latest",
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            # Check for success
            real_codebases = ["utils", "z80emu", "library-management-system"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            print(f"Tool calls: {len(tool_calls)}")
            print(f"Real codebases found: {found_real}")
            print(f"Content preview: {content[:150]}...")
            
            if len(tool_calls) > 0 or found_real:
                print("🎉 SUCCESS: Proxy solution works!")
                return True
            else:
                print("❌ Tool still not working with proxy")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Testing Proxy Solution")
    print("=" * 50)
    
    # Test proxy connectivity
    proxy_ok = test_proxy_connectivity()
    
    if not proxy_ok:
        print("\n❌ Proxy not accessible - set up proxy on lynn-pc first")
        print("💡 Run: python lynn_pc_tool_proxy.py (on lynn-pc)")
        return
    
    # Test tool with proxy
    tool_ok = test_tool_with_proxy()
    
    print(f"\n🎯 RESULTS")
    print("=" * 50)
    if tool_ok:
        print("🎉 PROXY SOLUTION SUCCESSFUL!")
        print("✅ Remote model can now use tools via local proxy")
        print("\n📋 Next steps:")
        print("1. Set up proxy on T5810 as well")
        print("2. Update tool configurations for remote models")
        print("3. Test with other tools")
    else:
        print("❌ Proxy solution needs refinement")
        print("🔍 Check:")
        print("1. Proxy is running on lynn-pc")
        print("2. Tool configuration points to localhost:5002")
        print("3. Network connectivity between servers")

if __name__ == "__main__":
    main()
