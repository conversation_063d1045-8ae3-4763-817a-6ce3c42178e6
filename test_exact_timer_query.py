#!/usr/bin/env python3
"""
Test the exact timer query that's hallucinating
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def test_exact_timer_query():
    """Test the exact query that's causing hallucination"""
    print("🔍 Testing Exact Timer Query")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # The exact query that's hallucinating
    query = "show timer management in utils codebase"
    
    print(f"📤 Exact query: '{query}'")
    print("-" * 50)
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📝 Result length: {len(result)}")
    print(f"📊 Result:")
    print(result)
    
    # Check for hallucination vs real code
    hallucination_indicators = [
        "import time",
        "class Timer:",
        "def start(self):",
        "def stop(self):",
        "Example usage:",
        "This code defines",
        "Note that this is just one possible implementation"
    ]
    
    real_code_indicators = [
        "tmwtimer",
        "Triangle MicroWorks",
        "/app/source_code/utils",
        "tmwtimer.c",
        "tmwtimer_cancel",
        "_decrementMinute"
    ]
    
    found_hallucination = [ind for ind in hallucination_indicators if ind in result]
    found_real_code = [ind for ind in real_code_indicators if ind in result]
    
    print(f"\n🔍 Analysis:")
    print(f"Hallucination indicators: {found_hallucination}")
    print(f"Real code indicators: {found_real_code}")
    
    if found_real_code and not found_hallucination:
        print(f"✅ SUCCESS: Shows real timer code")
        return True
    elif found_hallucination:
        print(f"❌ HALLUCINATING: Generating generic examples")
        return False
    else:
        print(f"⚠️ UNCLEAR: Neither clear hallucination nor real code")
        return False

def test_improved_timer_query():
    """Test with an improved query that should work"""
    print(f"\n🔧 Testing Improved Timer Query")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # More explicit query
    query = "Use the query_codebase tool to search the utils codebase for timer management functions. Look for tmwtimer functions and show me the actual code you find."
    
    print(f"📤 Improved query: {query[:80]}...")
    print("-" * 50)
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📝 Result length: {len(result)}")
    print(f"📊 Result preview:")
    print(result[:400] + "..." if len(result) > 400 else result)
    
    # Check for real code
    if any(term in result for term in ["tmwtimer", "Triangle MicroWorks", "/app/source_code"]):
        print(f"✅ SUCCESS: Shows real timer code")
        return True
    else:
        print(f"❌ Still not showing real code")
        return False

def test_direct_tmwtimer_query():
    """Test with direct tmwtimer query"""
    print(f"\n🎯 Testing Direct tmwtimer Query")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://192.168.0.11:11434")
    
    # Very specific query
    query = "Search utils codebase for tmwtimer functions"
    
    result = analyzer.chat_with_tools(query)
    
    print(f"Result: {result}")
    
    if "tmwtimer" in result.lower():
        print(f"✅ SUCCESS: Found tmwtimer functions")
        return True
    else:
        print(f"❌ Didn't find tmwtimer functions")
        return False

def main():
    print("🔍 Testing Exact Timer Management Query")
    print("=" * 60)
    
    # Test the exact query that's hallucinating
    exact_works = test_exact_timer_query()
    
    # Test improved query
    improved_works = test_improved_timer_query()
    
    # Test direct query
    direct_works = test_direct_tmwtimer_query()
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Exact query ('show timer management in utils codebase'): {'✅ Working' if exact_works else '❌ Hallucinating'}")
    print(f"Improved query (explicit tool instruction):              {'✅ Working' if improved_works else '❌ Not working'}")
    print(f"Direct query ('tmwtimer functions'):                     {'✅ Working' if direct_works else '❌ Not working'}")
    
    if not exact_works and (improved_works or direct_works):
        print(f"\n💡 CONCLUSION:")
        print(f"The issue is with the specific phrasing 'show timer management in utils codebase'")
        print(f"T5810 interprets this as a request for generic examples rather than a search query")
        print(f"More explicit queries that mention the tool work correctly")
        
        print(f"\n🔧 SOLUTION:")
        print(f"Use more explicit queries like:")
        print(f"• 'Find timer functions in utils codebase'")
        print(f"• 'Search utils for tmwtimer functions'")
        print(f"• 'Use query_codebase to find timer code in utils'")
    elif exact_works:
        print(f"\n✅ The exact query is now working correctly!")
    else:
        print(f"\n❌ All queries are having issues - need more investigation")

if __name__ == "__main__":
    main()
