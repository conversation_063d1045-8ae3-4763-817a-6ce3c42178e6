#!/usr/bin/env python3
"""
Test if the tool is actually working by checking response content quality
"""

import requests
import json

def test_tool_functionality():
    """Test if tool is working by analyzing response quality"""
    print("🔍 Testing Tool Functionality (Content Analysis)")
    print("=" * 60)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    models = ["llama3:latest", "T5810 - .llama3:latest", "smollm2:1.7b"]
    
    # Test queries that should show clear difference between tool vs no-tool
    test_queries = [
        {
            "query": "list codebases",
            "expect_tool": True,
            "indicators": ["utils", "z80emu", "library-management-system", "test_project"]
        },
        {
            "query": "select codebase library-management-system",
            "expect_tool": True,
            "indicators": ["selected", "library-management-system", "ready"]
        },
        {
            "query": "search for memory functions in utils",
            "expect_tool": True,
            "indicators": ["chunks", "found", "memory", "function", "tmwmem"]
        }
    ]
    
    results = {}
    
    for model in models:
        print(f"\n🧪 Testing Model: {model}")
        print("-" * 40)
        
        model_results = []
        
        for test in test_queries:
            print(f"   Query: '{test['query']}'")
            
            try:
                payload = {
                    "model": model,
                    "messages": [{"role": "user", "content": test["query"]}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False
                }
                
                response = requests.post(
                    f"{base_url}/api/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=45
                )
                
                if response.status_code == 200:
                    data = response.json()
                    message = data.get("choices", [{}])[0].get("message", {})
                    content = message.get("content", "")
                    tool_calls = message.get("tool_calls", [])
                    
                    # Analyze content quality
                    indicators_found = [ind for ind in test["indicators"] if ind.lower() in content.lower()]
                    
                    # Check if response looks like it came from the actual tool
                    tool_quality_indicators = [
                        "chunks found", "enhanced search", "selected codebase", 
                        "available codebases", "code context", "documents found",
                        "library-management-system", "utils codebase"
                    ]
                    
                    quality_score = sum(1 for ind in tool_quality_indicators if ind.lower() in content.lower())
                    
                    result = {
                        "query": test["query"],
                        "status": "success",
                        "tool_calls": len(tool_calls),
                        "content_length": len(content),
                        "indicators_found": indicators_found,
                        "quality_score": quality_score,
                        "likely_tool_response": quality_score > 0 or len(indicators_found) >= 2,
                        "content_preview": content[:150] + "..." if len(content) > 150 else content
                    }
                    
                    # Print analysis
                    status = "✅" if result["likely_tool_response"] else "❌"
                    print(f"      {status} Tool likely used: {result['likely_tool_response']}")
                    print(f"      📊 Quality score: {quality_score}, Indicators: {len(indicators_found)}")
                    print(f"      📝 Preview: {result['content_preview']}")
                    
                else:
                    result = {
                        "query": test["query"],
                        "status": "error",
                        "error": f"HTTP {response.status_code}",
                        "likely_tool_response": False
                    }
                    print(f"      ❌ Error: {result['error']}")
                
            except Exception as e:
                result = {
                    "query": test["query"],
                    "status": "exception",
                    "error": str(e),
                    "likely_tool_response": False
                }
                print(f"      ❌ Exception: {e}")
            
            model_results.append(result)
        
        results[model] = model_results
    
    # Summary analysis
    print("\n📊 SUMMARY ANALYSIS")
    print("=" * 60)
    
    for model, model_results in results.items():
        working_queries = sum(1 for r in model_results if r.get("likely_tool_response", False))
        total_queries = len(model_results)
        
        if working_queries > 0:
            print(f"✅ {model}: {working_queries}/{total_queries} queries show tool usage")
        else:
            print(f"❌ {model}: {working_queries}/{total_queries} queries show tool usage")
    
    # Detailed analysis for llama3:latest
    llama3_results = results.get("llama3:latest", [])
    if llama3_results:
        print(f"\n🔍 DETAILED ANALYSIS: llama3:latest")
        print("-" * 40)
        for result in llama3_results:
            if result.get("likely_tool_response"):
                print(f"✅ '{result['query']}' - Tool appears to be working")
                print(f"   Quality indicators found: {result.get('quality_score', 0)}")
                print(f"   Expected indicators found: {result.get('indicators_found', [])}")
            else:
                print(f"❌ '{result['query']}' - Generic response")
    
    return results

def main():
    results = test_tool_functionality()
    
    # Save detailed results
    with open("tool_functionality_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: tool_functionality_results.json")

if __name__ == "__main__":
    main()
