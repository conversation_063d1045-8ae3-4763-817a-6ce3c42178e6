#!/usr/bin/env python3
"""
Fix "show" queries to prevent hallucination
"""

from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer

def preprocess_query(query):
    """Preprocess queries to convert 'show' to 'find' to prevent hallucination"""
    
    # Convert "show X in Y codebase" to "find X in Y codebase"
    if query.lower().startswith("show ") and " in " in query.lower() and "codebase" in query.lower():
        # Replace "show" with "find" and add explicit tool instruction
        modified_query = query.replace("show ", "find ", 1)
        modified_query += ". Use the query_codebase tool to search the codebase and show actual code results."
        return modified_query
    
    return query

def test_show_query_fix():
    """Test the show query fix"""
    print("🔧 Testing Show Query Fix")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test the problematic query
    original_query = "show timer management in utils codebase"
    fixed_query = preprocess_query(original_query)
    
    print(f"📤 Original query: {original_query}")
    print(f"🔧 Fixed query: {fixed_query}")
    print("-" * 50)
    
    result = analyzer.chat_with_tools(fixed_query)
    
    print(f"📝 Result length: {len(result)}")
    print(f"📊 Result preview:")
    print(result[:400] + "..." if len(result) > 400 else result)
    
    # Check for hallucination vs real code
    hallucination_indicators = [
        "import time",
        "class Timer:",
        "def start(self):",
        "Example usage:",
        "This is a simple implementation"
    ]
    
    real_code_indicators = [
        "tmwtimer",
        "Triangle MicroWorks",
        "/app/source_code/utils",
        "tmwtimer.c"
    ]
    
    found_hallucination = [ind for ind in hallucination_indicators if ind in result]
    found_real_code = [ind for ind in real_code_indicators if ind in result]
    
    print(f"\n🔍 Analysis:")
    print(f"Hallucination indicators: {found_hallucination}")
    print(f"Real code indicators: {found_real_code}")
    
    if found_real_code and not found_hallucination:
        print(f"✅ SUCCESS: Fix worked - shows real code!")
        return True
    elif not found_hallucination and not found_real_code:
        print(f"⚠️ NEUTRAL: No hallucination but no clear real code either")
        return True
    else:
        print(f"❌ FAILURE: Still hallucinating")
        return False

def test_multiple_show_queries():
    """Test multiple show queries with the fix"""
    print(f"\n🧪 Testing Multiple Show Queries")
    print("=" * 50)
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    test_queries = [
        "show memory management in utils codebase",
        "show error handling in utils codebase", 
        "show network functions in utils codebase",
        "show data structures in utils codebase"
    ]
    
    results = []
    
    for query in test_queries:
        fixed_query = preprocess_query(query)
        
        print(f"\n📤 Original: {query}")
        print(f"🔧 Fixed: {fixed_query[:60]}...")
        
        try:
            result = analyzer.chat_with_tools(fixed_query)
            
            # Quick hallucination check
            if any(term in result for term in ["import ", "class ", "def ", "Example usage:"]):
                print(f"❌ Still hallucinating")
                results.append(False)
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code", "tmw"]):
                print(f"✅ Shows real code")
                results.append(True)
            else:
                print(f"⚠️ Unclear result")
                results.append(None)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append(False)
    
    success_rate = sum(1 for r in results if r is True) / len(results)
    print(f"\n📊 Success rate: {success_rate:.1%}")
    
    return success_rate > 0.5

class FixedDirectOllamaCodeAnalyzer(DirectOllamaCodeAnalyzer):
    """Enhanced analyzer that preprocesses queries to prevent hallucination"""
    
    def chat_with_tools(self, user_message, model="llama3.1:latest"):
        """Enhanced chat that preprocesses queries"""
        # Preprocess the query to fix "show" queries
        processed_message = preprocess_query(user_message)
        
        # Call the parent method with the processed query
        return super().chat_with_tools(processed_message, model)

def test_enhanced_analyzer():
    """Test the enhanced analyzer class"""
    print(f"\n🚀 Testing Enhanced Analyzer")
    print("=" * 50)
    
    analyzer = FixedDirectOllamaCodeAnalyzer("http://************:11434")
    
    # Test the original problematic query
    query = "show timer management in utils codebase"
    
    print(f"📤 Query: {query}")
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📝 Result preview: {result[:200]}...")
    
    if any(term in result for term in ["tmwtimer", "Triangle MicroWorks", "/app/source_code"]):
        print(f"✅ SUCCESS: Enhanced analyzer prevents hallucination!")
        return True
    else:
        print(f"❌ Still needs work")
        return False

def main():
    print("🔧 Fixing 'Show' Query Hallucination")
    print("=" * 60)
    
    # Test the basic fix
    basic_fix_works = test_show_query_fix()
    
    # Test multiple queries
    multiple_works = test_multiple_show_queries()
    
    # Test enhanced analyzer
    enhanced_works = test_enhanced_analyzer()
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Basic fix:        {'✅ Working' if basic_fix_works else '❌ Not working'}")
    print(f"Multiple queries: {'✅ Working' if multiple_works else '❌ Not working'}")
    print(f"Enhanced analyzer: {'✅ Working' if enhanced_works else '❌ Not working'}")
    
    if enhanced_works:
        print(f"\n🎉 SOLUTION FOUND!")
        print(f"Use the FixedDirectOllamaCodeAnalyzer class to automatically")
        print(f"convert 'show X in Y codebase' queries to 'find X in Y codebase'")
        print(f"This prevents T5810 from hallucinating generic examples")
    else:
        print(f"\n💡 MANUAL WORKAROUND:")
        print(f"Instead of: 'show timer management in utils codebase'")
        print(f"Use:        'find timer management in utils codebase'")

if __name__ == "__main__":
    main()
