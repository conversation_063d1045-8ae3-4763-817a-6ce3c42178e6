#!/usr/bin/env python3
"""
Test the routing fix to ensure holding register queries don't go to complexity analysis
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def test_query_routing():
    """Test that holding register queries don't get routed to complexity analysis"""
    print("🔍 Testing Query Routing Fix")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    # Test queries that should NOT go to complexity analysis
    test_queries = [
        "tell me about a modbus holding register and the functions used to manage it",
        "what is a coil in the modbus codebase",
        "show me memory management functions",
        "find timer functions in utils codebase"
    ]
    
    # Test queries that SHOULD go to complexity analysis
    complexity_queries = [
        "analyze complexity in modbus codebase",
        "code complexity analysis for utils",
        "complexity analysis of the z80emu project"
    ]
    
    print("🔧 Testing queries that should get CONTEXT INJECTION:")
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}] Query: {query}")
        
        # Check intent detection
        intent = tool._detect_query_intent(query)
        print(f"   Detected intent: {intent}")
        
        if intent == "codebase_management":
            print(f"   ❌ WRONG: Should not be management intent")
        else:
            print(f"   ✅ CORRECT: Will get context injection")
    
    print(f"\n🔧 Testing queries that SHOULD go to complexity analysis:")
    for i, query in enumerate(complexity_queries, 1):
        print(f"\n[{i}] Query: {query}")
        
        # Check intent detection
        intent = tool._detect_query_intent(query)
        print(f"   Detected intent: {intent}")
        
        if intent == "codebase_management":
            print(f"   ✅ CORRECT: Will route to complexity analysis")
        else:
            print(f"   ❌ WRONG: Should be management intent")

async def test_full_processing():
    """Test full processing of the holding register query"""
    print(f"\n🚀 Testing Full Processing After Routing Fix")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"📤 Query: {query}")
    
    try:
        result = await tool.__call__(query)
        
        print(f"📝 Result length: {len(result) if result else 0}")
        
        if result:
            # Check if it went to complexity analysis (bad)
            if "complexity indicators" in result.lower() or "nested loops" in result.lower():
                print(f"❌ FAILURE: Still routing to complexity analysis")
                print(f"📊 Result preview: {result[:300]}...")
                return False
            
            # Check if it got real code context (good)
            elif any(term in result for term in ["Triangle MicroWorks", "/app/source_code/modbus", "_processReadHoldingRegisters"]):
                print(f"✅ SUCCESS: Got real code context!")
                print(f"📊 Result preview: {result[:300]}...")
                return True
            
            # Check if it's still hallucinating (bad)
            elif any(term in result for term in ["write_holding_register()", "read_holding_register()", "hold_registers()"]):
                print(f"❌ FAILURE: Still hallucinating generic functions")
                print(f"📊 Result preview: {result[:300]}...")
                return False
            
            else:
                print(f"⚠️ UNCLEAR: Result doesn't match expected patterns")
                print(f"📊 Result preview: {result[:300]}...")
                return None
        else:
            print(f"❌ FAILURE: Empty result")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

async def main():
    print("🔧 Testing Routing Fix for Holding Register Queries")
    print("=" * 80)
    
    # Test intent detection
    await test_query_routing()
    
    # Test full processing
    full_works = await test_full_processing()
    
    print(f"\n🎯 FINAL RESULT")
    print("=" * 80)
    
    if full_works is True:
        print(f"✅ SUCCESS: Routing fix worked!")
        print(f"✅ Holding register queries now get real code context")
        print(f"✅ No more unwanted complexity analysis routing")
    elif full_works is False:
        print(f"❌ FAILURE: Still routing incorrectly")
        print(f"💡 May need to check other routing conditions")
    else:
        print(f"⚠️ UNCLEAR: Need more investigation")

if __name__ == "__main__":
    asyncio.run(main())
