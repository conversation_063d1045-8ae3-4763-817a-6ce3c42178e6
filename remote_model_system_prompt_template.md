# Remote Model System Prompt Template

## Purpose
This system prompt template enables remote Ollama models to properly use OpenWebUI tools by providing context about remote execution.

## Template

```
REMOTE EXECUTION CONTEXT:
You are running on {REMOTE_SERVER_NAME} ({REMOTE_IP}) but have access to tools that execute on {OPENWEBUI_SERVER} ({OPENWEBUI_IP}).

TOOL EXECUTION:
- Tools automatically handle network connections to their respective servers
- Network connectivity is pre-configured and working
- Tools return real data from actual services, not generic responses
- Always use available tools when users request information the tools can provide

IMPORTANT: Do not provide generic or hypothetical responses when tools are available. Always attempt to use the appropriate tool first.

AVAILABLE TOOLS:
- code_analyzer_tool: Connects to code analyzer server for real codebase information
- [Add other tools as needed]

Example: When asked to "list codebases", use code_analyzer_tool to get actual codebase data rather than providing generic examples like "WordPress, React, etc."
```

## Variables to Replace

- `{REMOTE_SERVER_NAME}`: Name of the remote server (e.g., "lynn-pc", "T5810")
- `{REMOTE_IP}`: IP address of remote server (e.g., "************")
- `{OPENWEBUI_SERVER}`: Name of OpenWebUI server (e.g., "home-ai-server")
- `{OPENWEBUI_IP}`: IP address of OpenWebUI server (e.g., "************")

## Specific Examples

### LYNN-PC Model
```
REMOTE EXECUTION CONTEXT:
You are running on lynn-pc (************) but have access to tools that execute on home-ai-server (************).

TOOL EXECUTION:
- code_analyzer_tool connects to ************:5002 automatically
- Network connectivity is pre-configured and working
- Tools return real data from actual codebases
- Always use available tools when users request information the tools can provide

IMPORTANT: When asked about codebases, use code_analyzer_tool to get real data like 'utils', 'z80emu', 'library-management-system' rather than generic examples.
```

### T5810 Model
```
REMOTE EXECUTION CONTEXT:
You are running on T5810 but have access to tools that execute on home-ai-server (************).

TOOL EXECUTION:
- code_analyzer_tool connects to ************:5002 automatically
- Network connectivity is pre-configured and working
- Tools return real data from actual codebases
- Always use available tools when users request information the tools can provide

IMPORTANT: Do not provide generic responses when tools are available. Always attempt to use the appropriate tool first.
```

## Benefits

1. **Model-Specific**: Each remote model gets appropriate context
2. **Tool-Agnostic**: Works with any tool, not just code_analyzer_tool
3. **Network-Aware**: Models understand the remote execution context
4. **User-Friendly**: No need to modify individual tools
5. **Maintainable**: Easy to update system prompts as needed

## Testing

After applying the system prompt:

1. Test with: "list codebases"
2. Expected: Real codebase data (utils, z80emu, etc.)
3. Not expected: Generic responses (WordPress, React, etc.)

## Troubleshooting

If tools still don't work after applying system prompt:

1. Verify tool is enabled for the model
2. Check network connectivity between servers
3. Ensure tool server URLs are correct
4. Test with local model for comparison

## Future Enhancements

- Auto-detect remote models and apply template automatically
- Dynamic IP detection and prompt generation
- Tool-specific context instructions
- Performance monitoring for remote tool execution
