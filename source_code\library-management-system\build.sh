#!/bin/bash
echo "Building Library Management System..."

# Create bin directory if it doesn't exist
mkdir -p bin

# Compile all Java files
javac -d bin -cp src src/com/library/*.java src/com/library/model/*.java src/com/library/service/*.java src/com/library/exception/*.java src/com/library/util/*.java

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo ""
    echo "To run the application:"
    echo "java -cp bin com.library.LibraryApplication"
    echo ""
    echo "Or run: ./run.sh"
else
    echo "Build failed!"
fi
