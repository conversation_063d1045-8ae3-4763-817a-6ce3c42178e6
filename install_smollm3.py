#!/usr/bin/env python3
"""
Install and test SmolLM3 for faster code analysis
"""

import requests
import subprocess
import time
import json

def check_ollama_connection():
    """Check if Ollama is accessible"""
    try:
        response = requests.get("http://home-ai-server.local:11434/api/tags", timeout=10)
        return response.status_code == 200
    except:
        return False

def install_smollm3():
    """Install SmolLM3 via Ollama"""
    print("🚀 Installing SmolLM3...")
    print("=" * 50)
    
    # Try different model names
    model_variants = [
        "smollm3:3b",
        "smollm3:latest", 
        "huggingfacetb/smollm3:3b"
    ]
    
    for model in model_variants:
        print(f"\n🔍 Trying to install: {model}")
        try:
            # Use subprocess to run ollama pull
            result = subprocess.run(
                ["ollama", "pull", model],
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes timeout
            )
            
            if result.returncode == 0:
                print(f"✅ Successfully installed: {model}")
                return model
            else:
                print(f"❌ Failed to install {model}")
                print(f"   Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout installing {model}")
        except Exception as e:
            print(f"❌ Exception installing {model}: {e}")
    
    print(f"\n❌ Could not install SmolLM3 with any variant")
    return None

def test_smollm3_performance(model_name):
    """Test SmolLM3 performance"""
    print(f"\n🧪 Testing {model_name} Performance")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test query
    test_query = "list codebases"
    
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": test_query}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print(f"📤 Testing query: '{test_query}'")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            # Check for real codebase data
            real_codebases = ["utils", "z80emu", "go-example-master", "TypeScript-Node-Starter-master"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            print(f"✅ Response received in {response_time:.2f}s")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content length: {len(content)} chars")
            print(f"🎯 Real codebases found: {found_real}")
            
            if found_real or len(tool_calls) > 0:
                print(f"🎉 SUCCESS: SmolLM3 is working!")
                return True, response_time
            else:
                print(f"⚠️ Model responded but may not be using tools properly")
                return False, response_time
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, 0

def compare_with_current_model():
    """Compare with current llama3:latest"""
    print(f"\n📊 Comparing with Current Model (llama3:latest)")
    print("=" * 50)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3:latest",
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"llama3:latest response time: {response_time:.2f}s")
        return response_time
        
    except Exception as e:
        print(f"❌ Could not test llama3:latest: {e}")
        return 0

def main():
    print("🎯 SmolLM3 Installation and Testing")
    print("=" * 50)
    
    # Check Ollama connection
    if not check_ollama_connection():
        print("❌ Cannot connect to Ollama server")
        print("💡 Make sure Ollama is running on home-ai-server")
        return
    
    print("✅ Ollama server is accessible")
    
    # Install SmolLM3
    installed_model = install_smollm3()
    
    if not installed_model:
        print(f"\n💡 Alternative: You can use your existing models:")
        print("• deepseek-coder:6.7b (46% faster than llama3)")
        print("• smollm2:1.7b (86% faster, lower quality)")
        return
    
    # Test SmolLM3
    success, smollm3_time = test_smollm3_performance(installed_model)
    
    if success:
        # Compare with current model
        llama3_time = compare_with_current_model()
        
        print(f"\n🎯 PERFORMANCE COMPARISON")
        print("=" * 50)
        print(f"SmolLM3:        {smollm3_time:.2f}s")
        print(f"llama3:latest:   {llama3_time:.2f}s")
        
        if llama3_time > 0:
            speedup = (llama3_time - smollm3_time) / llama3_time * 100
            print(f"Speedup:         {speedup:.1f}% faster")
        
        print(f"\n🎉 RECOMMENDATION:")
        print(f"Switch to SmolLM3 for:")
        print("• Faster responses")
        print("• Better code understanding")
        print("• Long context support (128k)")
        print("• Reasoning capabilities")
        
    else:
        print(f"\n⚠️ SmolLM3 installed but needs configuration")
        print("💡 Make sure to enable code_analyzer_tool for this model")

if __name__ == "__main__":
    main()
