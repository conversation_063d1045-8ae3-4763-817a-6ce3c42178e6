#!/usr/bin/env python3
"""
Debug the exact query that's causing issues in OpenWebUI
"""

import asyncio
from open_webui_code_analyzer_tool import Tools

async def debug_exact_query():
    """Debug the exact query step by step"""
    print("🔍 Debugging Exact Query Processing")
    print("=" * 60)
    
    tool = Tools()
    tool.valves.current_codebase = "modbus"
    
    # The exact query from OpenWebUI
    query = "tell me about a modbus holding register and the functions used to manage it"
    
    print(f"📤 Query: {query}")
    print(f"🎯 Current codebase: {tool.valves.current_codebase}")
    
    # Step 1: Check intent detection
    print(f"\n🔍 Step 1: Intent Detection")
    intent = tool._detect_query_intent(query)
    print(f"   Detected intent: {intent}")
    
    # Step 2: Check if it's code-related
    print(f"\n🔍 Step 2: Code-Related Check")
    is_code_related = tool._detect_code_related_query(query)
    print(f"   Is code-related: {is_code_related}")
    
    # Step 3: Check management phrases specifically
    print(f"\n🔍 Step 3: Management Phrase Check")
    query_lower = query.lower()
    management_phrases = [
        'list codebase', 'show codebase', 'available codebase',
        'select codebase', 'choose codebase', 'switch codebase',
        'process codebase', 'index codebase', 'delete codebase',
        'stats', 'statistics', 'status',
        'get stats', 'show stats', 'codebase stats',
        'get statistics', 'show statistics', 'codebase statistics',
        'analyze complexity', 'code complexity analysis', 'complexity analysis'
    ]
    
    found_management_phrases = [phrase for phrase in management_phrases if phrase in query_lower]
    print(f"   Found management phrases: {found_management_phrases}")
    
    # Step 4: Check complexity phrases specifically
    print(f"\n🔍 Step 4: Complexity Phrase Check")
    complexity_phrases = ['analyze complexity', 'code complexity analysis', 'complexity analysis']
    found_complexity_phrases = [phrase for phrase in complexity_phrases if phrase in query_lower]
    print(f"   Found complexity phrases: {found_complexity_phrases}")
    
    # Step 5: Simulate the routing logic
    print(f"\n🔍 Step 5: Routing Logic Simulation")
    
    if intent == "codebase_management":
        print(f"   ✅ Would route to _handle_management_query")
        
        # Check what would happen in _handle_management_query
        if any(phrase in query_lower for phrase in complexity_phrases):
            print(f"   ✅ Would route to analyze_complexity")
        elif 'stats' in query_lower or 'statistics' in query_lower:
            print(f"   ✅ Would route to get_codebase_stats")
        elif 'status' in query_lower:
            print(f"   ✅ Would route to check_system_status")
        else:
            print(f"   ⚠️ Would fall through to other management routing")
            
    elif intent == "code_analysis":
        print(f"   ✅ Would route to context injection")
    else:
        print(f"   ⚠️ Unexpected routing: {intent}")

async def test_word_by_word():
    """Test each word in the query to see what triggers management intent"""
    print(f"\n🔍 Testing Word-by-Word Triggers")
    print("=" * 60)
    
    tool = Tools()
    
    # Break down the query
    query = "tell me about a modbus holding register and the functions used to manage it"
    words = query.split()
    
    print(f"Original query: {query}")
    print(f"Intent: {tool._detect_query_intent(query)}")
    
    # Test removing words one by one
    for i, word in enumerate(words):
        test_query = " ".join(words[:i] + words[i+1:])
        intent = tool._detect_query_intent(test_query)
        print(f"Without '{word}': {intent} | Query: {test_query}")

async def test_minimal_triggers():
    """Test minimal phrases that trigger management intent"""
    print(f"\n🔍 Testing Minimal Management Triggers")
    print("=" * 60)
    
    tool = Tools()
    
    test_queries = [
        "manage it",
        "functions used to manage",
        "to manage it", 
        "manage",
        "management",
        "modbus holding register",
        "holding register functions",
        "functions used",
        "tell me about modbus"
    ]
    
    for query in test_queries:
        intent = tool._detect_query_intent(query)
        is_code = tool._detect_code_related_query(query)
        print(f"'{query}' -> Intent: {intent}, Code-related: {is_code}")

async def main():
    print("🔍 Deep Debug of Query Routing Issue")
    print("=" * 70)
    
    await debug_exact_query()
    await test_word_by_word()
    await test_minimal_triggers()
    
    print(f"\n💡 ANALYSIS:")
    print("If the intent is 'codebase_management' but no management phrases are found,")
    print("then there's a bug in the _detect_query_intent function.")

if __name__ == "__main__":
    asyncio.run(main())
