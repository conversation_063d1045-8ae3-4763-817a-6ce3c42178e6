#!/usr/bin/env python3
"""
Test OpenWebUI API with codebase analyzer tool
"""

import asyncio
import aiohttp
import requests
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

class OpenWebUIAPITester:
    """Test the codebase analyzer tool via OpenWebUI API"""
    
    def __init__(self):
        # OpenWebUI configuration from your setup
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        # Try different models that might support tools
        self.models_to_try = [
            "llama3.1:latest",
            "llama3.2:latest",
            "llama3:latest",
            "qwen2.5:latest",
            "mistral:latest"
        ]
        self.model = None  # Will be set after finding a compatible model
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        print(f"🔧 OpenWebUI API Tester Initialized")
        print(f"   Base URL: {self.base_url}")
        print(f"   Models to try: {self.models_to_try}")

        # Find a compatible model
        self.model = self.find_compatible_model()

        # Initialize DirectRAGClient for parallel querying
        self.rag_client = DirectRAGClient()

    def find_compatible_model(self) -> str:
        """Find a model that supports function calling"""
        try:
            # Get available models
            response = requests.get(f"{self.base_url}/api/models", headers=self.headers, timeout=20)
            if response.status_code == 200:
                available_models = [model.get("id", "") for model in response.json()]
                print(f"📋 Available models: {available_models}")

                # Try to find a compatible model from our list
                for model in self.models_to_try:
                    if model in available_models:
                        print(f"✅ Selected model: {model}")
                        return model

                # If none of our preferred models are available, try the first available
                if available_models:
                    fallback_model = available_models[0]
                    print(f"⚠️ Using fallback model: {fallback_model}")
                    return fallback_model
                else:
                    print(f"❌ No models available")
                    return "llama3:latest"  # Default fallback
            else:
                print(f"❌ Could not get models list: {response.status_code}")
                return "llama3:latest"  # Default fallback
        except Exception as e:
            print(f"❌ Error finding compatible model: {e}")
            return "llama3:latest"  # Default fallback

    def test_api_connection(self) -> bool:
        """Test basic API connectivity"""
        try:
            response = requests.get(f"{self.base_url}/api/models", headers=self.headers, timeout=20)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ API Connection successful. Found {len(models)} models.")
                return True
            else:
                print(f"❌ API Connection failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API Connection error: {e}")
            return False

    def find_correct_tool_id(self) -> str:
        """Find the correct tool ID by testing different possibilities"""
        print("🔍 Finding correct tool ID...")

        # Based on user confirmation and previous investigations
        tool_ids_to_test = [
            "code_analyzer_tool",       # CORRECT: User confirmed this is the right one
            "code_analyzer_tools",      # Backup that worked faster
            "codebase_analyzer",        # Previous attempt
            "code_analysis_tool",       # Variation
            "code_rag_tools",          # Previous name from memories
        ]

        test_query = "tmwmem_alloc"

        for tool_id in tool_ids_to_test:
            print(f"   Testing tool_id: '{tool_id}'...")
            try:
                result = self.call_codebase_analyzer(test_query, tool_id, timeout=240)  # Doubled timeout
                if result.get("success"):
                    content = result.get("content", "")
                    # Check if it looks like our tool was actually called
                    if any(indicator in content.lower() for indicator in [
                        "code context retrieved", "chunks found", "tmwmem", "utils codebase"
                    ]):
                        print(f"   ✅ Found working tool_id: '{tool_id}'")
                        return tool_id
                    else:
                        print(f"   ⚠️ Tool responded but may not be our codebase analyzer")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"   ❌ Error testing '{tool_id}': {e}")

        print("   ⚠️ No working tool_id found, using default 'codebase_analyzer'")
        return "codebase_analyzer"

    def select_available_codebase(self, tool_id: str) -> bool:
        """Select any available codebase before running tests (dynamic selection)"""
        print("🔧 Selecting available codebase...")

        try:
            # First get list of available codebases
            list_result = self.call_codebase_analyzer("list codebases", tool_id, timeout=60)
            if not list_result.get("success"):
                print("   ❌ Failed to get codebase list")
                return False

            # Try to select the first available codebase
            content = list_result.get("content", "")
            codebase_to_select = None

            # Look for any available codebase (no hardcoded preferences)
            for potential_name in ["library-management-system", "utils", "z80emu", "modbus", "test_project"]:
                if potential_name in content.lower():
                    codebase_to_select = potential_name
                    break

            if not codebase_to_select:
                print("   ❌ No recognizable codebases found")
                return False

            print(f"   🎯 Attempting to select: {codebase_to_select}")
            result = self.call_codebase_analyzer(f"select {codebase_to_select} codebase", tool_id, timeout=240)
            if result.get("success"):
                content = result.get("content", "")
                success = any(indicator in content.lower() for indicator in [
                    "selected", "codebase", "ready", codebase_to_select.lower()
                ])
                if success:
                    print(f"   ✅ Codebase '{codebase_to_select}' selected successfully")
                    return True
                else:
                    print("   ⚠️ Codebase selection response unclear")
                    return False
            else:
                print(f"   ❌ Failed to select codebase: {result.get('error', 'Unknown error')}")
                return False
        except Exception as e:
            print(f"   ❌ Error selecting codebase: {e}")
            return False

    def call_codebase_analyzer(self, query: str, tool_id: str = "codebase_analyzer", timeout: int = 120) -> Dict[str, Any]:
        """Call OpenWebUI with explicit tool_ids to trigger the codebase analyzer tool"""

        # Use the query directly - the tool_ids parameter will ensure our tool is used
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": [tool_id],  # This is the key! Explicitly specify our tool
            "stream": False
        }
        
        print(f"\n🔧 Calling OpenWebUI API with query: '{query}'")
        print(f"🔧 Tool ID: '{tool_id}', Timeout: {timeout}s")

        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=timeout  # Use the timeout parameter
            )
            end_time = time.time()

            print(f"📊 Response Status: {response.status_code}, Time: {end_time - start_time:.2f}s")

            if response.status_code == 200:
                result = response.json()

                # Extract information from the response
                choices = result.get("choices", [])
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    tool_calls = message.get("tool_calls", [])

                    # Show detailed response content
                    print(f"💬 Response Content (first 300 chars):")
                    print(f"   {content[:300]}...")

                    # Look for chunk indicators
                    chunk_indicators = [
                        "chunks found", "chunk found", "code context retrieved",
                        "context retrieved", "relevant code", "results found"
                    ]
                    found_indicators = [ind for ind in chunk_indicators if ind in content.lower()]
                    if found_indicators:
                        print(f"🔍 Found chunk indicators: {found_indicators}")

                    # Look for chunk numbers
                    import re
                    chunk_matches = re.findall(r'(\d+)\s+chunks?\s+found', content.lower())
                    if chunk_matches:
                        print(f"🔢 Chunk numbers found: {chunk_matches}")

                    return {
                        "success": True,
                        "content": content,
                        "tool_calls": tool_calls,
                        "response_time": end_time - start_time,
                        "raw_response": result
                    }
                else:
                    print("❌ No choices in response")
                    return {
                        "success": False,
                        "error": "No choices in response",
                        "raw_response": result
                    }
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "response_time": end_time - start_time
                }

        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": f"Request failed: {str(e)}"
            }

    async def call_with_parallel_rag(self, query: str, tool_id: str = "code_analyzer_tool", timeout: int = 180) -> Dict[str, Any]:
        """Call OpenWebUI API and RAG server in parallel for enhanced analysis"""
        print(f"\n🔧 Calling OpenWebUI API with query: '{query}'")
        print(f"🔧 Tool ID: '{tool_id}', Timeout: {timeout}s")

        try:
            # Launch both requests in parallel
            openwebui_task = asyncio.create_task(self._async_call_openwebui(query, tool_id, timeout))
            rag_task = asyncio.create_task(self.rag_client.query_rag_server(query, "utils"))

            # Wait for both to complete
            openwebui_result, rag_result = await asyncio.gather(openwebui_task, rag_task, return_exceptions=True)

            # Handle exceptions
            if isinstance(openwebui_result, Exception):
                openwebui_result = {
                    "success": False,
                    "error": f"OpenWebUI error: {str(openwebui_result)}"
                }

            if isinstance(rag_result, Exception):
                rag_result = {
                    "success": False,
                    "error": f"RAG server error: {str(rag_result)}"
                }

            # Analyze context utilization if both succeeded
            context_analysis = {}
            if openwebui_result.get("success") and rag_result.get("success"):
                openwebui_content = openwebui_result.get("content", "")
                context_analysis = self.rag_client.analyze_context_utilization(openwebui_content, rag_result)

            # Combine results with RAG metrics
            enhanced_result = {
                **openwebui_result,
                "rag_data": rag_result,
                "context_analysis": context_analysis,
                "parallel_query": True
            }

            # Print RAG analysis summary
            if rag_result.get("success"):
                print(f"🔍 RAG Analysis:")
                print(f"   Chunks Retrieved: {rag_result.get('chunks_count', 0)}")
                print(f"   Max Similarity: {rag_result.get('max_similarity', 0):.3f}")
                print(f"   Source Files: {len(rag_result.get('source_files', []))}")
                print(f"   Retrieval Time: {rag_result.get('retrieval_time', 0):.2f}s")

                if context_analysis:
                    print(f"   Context Utilization: {context_analysis.get('utilization_rate', 0):.1%}")
                    print(f"   Context Quality: {context_analysis.get('context_quality', 'unknown')}")
            else:
                print(f"❌ RAG Query Failed: {rag_result.get('error', 'Unknown error')}")

            return enhanced_result

        except Exception as e:
            print(f"❌ Parallel query exception: {e}")
            return {
                "success": False,
                "error": f"Parallel query failed: {str(e)}"
            }

    async def _async_call_openwebui(self, query: str, tool_id: str, timeout: int) -> Dict[str, Any]:
        """Async version of OpenWebUI API call"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": [tool_id],
            "stream": False
        }

        start_time = time.time()

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                end_time = time.time()

                print(f"📊 Response Status: {response.status}, Time: {end_time - start_time:.2f}s")

                if response.status == 200:
                    result = await response.json()

                    # Extract information from the response
                    choices = result.get("choices", [])
                    if choices:
                        message = choices[0].get("message", {})
                        content = message.get("content", "")
                        tool_calls = message.get("tool_calls", [])

                        # Show detailed response content
                        print(f"💬 Response Content (first 300 chars):")
                        print(f"   {content[:300]}...")

                        # Look for chunk indicators
                        chunk_indicators = [
                            "chunks found", "chunk found", "code context retrieved",
                            "context retrieved", "relevant code", "results found"
                        ]
                        found_indicators = [ind for ind in chunk_indicators if ind in content.lower()]
                        if found_indicators:
                            print(f"🔍 Found chunk indicators: {found_indicators}")

                        return {
                            "success": True,
                            "content": content,
                            "tool_calls": tool_calls,
                            "response_time": end_time - start_time,
                            "status_code": response.status
                        }
                    else:
                        return {
                            "success": False,
                            "error": "No choices in response",
                            "response_time": end_time - start_time
                        }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "response_time": end_time - start_time
                    }

    def analyze_tool_response(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Analyze the tool response for success metrics including RAG data"""
        analysis = {
            "query": query,
            "success": result.get("success", False),
            "response_time": result.get("response_time", 0),
            "tool_called": False,
            "chunks_found": 0,
            "has_code_context": False,
            "error": result.get("error"),
            # RAG-specific metrics
            "rag_chunks_retrieved": 0,
            "rag_similarity_max": 0,
            "rag_similarity_avg": 0,
            "rag_source_files": [],
            "rag_retrieval_time": 0,
            "context_utilization_rate": 0,
            "context_quality": "unknown",
            "parallel_query": result.get("parallel_query", False)
        }
        
        if result.get("success"):
            content = result.get("content", "")
            tool_calls = result.get("tool_calls", [])
            
            # Check if tool was called
            analysis["tool_called"] = len(tool_calls) > 0
            
            # Look for chunk count in content
            import re
            chunk_matches = re.findall(r'(\d+)\s+chunks?\s+found', content.lower())
            if chunk_matches:
                analysis["chunks_found"] = int(chunk_matches[-1])  # Take the last match
            
            # Check for code context and tool usage indicators
            analysis["has_code_context"] = any(keyword in content.lower() for keyword in [
                "code context", "function", "struct", "class", "method", "variable",
                "tmwmem", "tmwdiag", "utils codebase", "memory allocation", "error handling"
            ])

            # Check for specific indicators that the codebase analyzer was used
            tool_indicators = [
                "code context retrieved",
                "chunks found",
                "codebase analyzer",
                "=== code metadata ===",
                "=== relevant code context ===",
                "file:", "language:", "type:",
                "tmwmem_alloc", "tmwdiag_error", "tmwmem", "tmwdiag",
                "context 1:", "context 2:",
                "domains:", "quality:", "complexity:"
            ]
            analysis["tool_called"] = any(indicator in content.lower() for indicator in tool_indicators)

            # Also check for the specific success pattern we saw in GUI
            if "code context retrieved successfully" in content.lower():
                analysis["tool_called"] = True
            
            # Overall success criteria
            analysis["overall_success"] = (
                analysis["tool_called"] and
                (analysis["chunks_found"] > 0 or analysis["has_code_context"])
            )

        # Extract RAG data if available
        rag_data = result.get("rag_data", {})
        if rag_data.get("success"):
            analysis["rag_chunks_retrieved"] = rag_data.get("chunks_count", 0)
            analysis["rag_similarity_max"] = rag_data.get("max_similarity", 0)
            analysis["rag_similarity_avg"] = rag_data.get("avg_similarity", 0)
            analysis["rag_source_files"] = rag_data.get("source_files", [])
            analysis["rag_retrieval_time"] = rag_data.get("retrieval_time", 0)

        # Extract context analysis if available
        context_analysis = result.get("context_analysis", {})
        if context_analysis:
            analysis["context_utilization_rate"] = context_analysis.get("utilization_rate", 0)
            analysis["context_quality"] = context_analysis.get("context_quality", "unknown")

        return analysis

    def get_comprehensive_utils_queries(self) -> List[Dict[str, Any]]:
        """Get comprehensive test queries based on actual utils codebase analysis"""
        return [
            # === MEMORY MANAGEMENT (Core TMW functionality) ===
            {
                "category": "Memory Management",
                "queries": [
                    "find tmwmem_alloc function implementation",
                    "show me all memory allocation functions",
                    "how does tmwmem_free work?",
                    "find memory allocation patterns in this codebase",
                    "show me TMWMEM_TYPE enumeration",
                    "get code context for memory management",
                    "find tmwmem_lowAlloc implementation",
                    "show memory allocation table structures",
                    "find mbmem_alloc and mmbmem_alloc functions",
                    "how does memory header management work?"
                ]
            },

            # === TIMER MANAGEMENT ===
            {
                "category": "Timer Management",
                "queries": [
                    "find tmwtimer_start function",
                    "how do timers work in this codebase?",
                    "show me timer callback mechanisms",
                    "find TMWTIMER structure definition",
                    "get timer management code context",
                    "show me timer queue implementation",
                    "find timer cancellation code",
                    "how are timer timeouts handled?",
                    "find _timerCallback function",
                    "show me timer restart logic"
                ]
            },

            # === DATA STRUCTURES ===
            {
                "category": "Data Structures",
                "queries": [
                    "find TMWDLIST implementation",
                    "show me linked list operations",
                    "find tmwdlist_addEntry function",
                    "how does the doubly linked list work?",
                    "show me list manipulation functions",
                    "find TMWDLIST_MEMBER structure",
                    "get data structure code context",
                    "show me tree data structures",
                    "find list removal functions",
                    "how are list entries managed?"
                ]
            },

            # === DATABASE OPERATIONS ===
            {
                "category": "Database Operations",
                "queries": [
                    "find TMWDB database functions",
                    "show me database queue implementation",
                    "find TMWDB_STORE_FUNC callback",
                    "how does asynchronous database work?",
                    "show me database data structures",
                    "find TMWDBDataStruct definition",
                    "get database management context",
                    "find TMWDB_QUEUE structure",
                    "show me database locking mechanisms",
                    "how is database overflow handled?"
                ]
            },

            # === SIMULATION FRAMEWORK ===
            {
                "category": "Simulation Framework",
                "queries": [
                    "find simulation code in THtmwsim",
                    "show me simulated database implementation",
                    "find TMWSIM_TABLE structures",
                    "how does the simulation framework work?",
                    "show me binary tree simulation code",
                    "find TMWTree implementation",
                    "get simulation context code",
                    "find TMWSIM_TABLE_HEAD definition",
                    "show me simulation data points",
                    "how are master and outstation simulations handled?"
                ]
            },

            # === CONFIGURATION & DEFINITIONS ===
            {
                "category": "Configuration",
                "queries": [
                    "find TMWCNFG configuration options",
                    "show me compiler definitions",
                    "find TMWDEFS global definitions",
                    "how are features configured?",
                    "show me conditional compilation code",
                    "find platform-specific definitions",
                    "get configuration management context",
                    "find TMWCNFG_USE_SIMULATED_DB usage",
                    "show me thread configuration options",
                    "how is memory allocation configured?"
                ]
            },

            # === ERROR HANDLING & VALIDATION ===
            {
                "category": "Error Handling",
                "queries": [
                    "find error handling patterns",
                    "show me parameter validation code",
                    "find NULL pointer checks",
                    "how are errors handled in this codebase?",
                    "show me assertion usage",
                    "find boundary condition checks",
                    "get error handling code context",
                    "find TMWDEFS_NULL usage",
                    "show me error return patterns",
                    "how are invalid parameters handled?"
                ]
            },

            # === THREADING & SYNCHRONIZATION ===
            {
                "category": "Threading",
                "queries": [
                    "find thread synchronization code",
                    "show me locking mechanisms",
                    "find TMWTARG_LOCK_SECTION usage",
                    "how is thread safety implemented?",
                    "show me resource locking code",
                    "find critical section handling",
                    "get threading code context",
                    "find TMWDEFS_RESOURCE_LOCK usage",
                    "show me channel locking patterns",
                    "how are race conditions prevented?"
                ]
            }
        ]

    async def run_comprehensive_utils_test(self, tool_id: str) -> tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Run comprehensive test based on utils codebase content"""
        print("🚀 COMPREHENSIVE UTILS CODEBASE TEST")
        print("=" * 80)

        # Get comprehensive test scenarios
        test_scenarios = self.get_comprehensive_utils_queries()

        overall_results: Dict[str, Any] = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "tool_usage_detected": 0,
            "actual_code_responses": 0,
            "category_results": {},
            "total_time": 0.0
        }

        # Collect detailed results for markdown report
        detailed_results = []

        for scenario in test_scenarios:
            category = scenario["category"]
            queries = scenario["queries"]

            print(f"\n🔧 TESTING CATEGORY: {category}")
            print("=" * 60)

            category_results = {
                "total": len(queries),
                "successful": 0,
                "failed": 0,
                "tool_used": 0,
                "actual_code": 0,
                "total_time": 0.0
            }

            for i, query in enumerate(queries, 1):
                print(f"\n--- {category} Test {i}/{len(queries)}: '{query}' ---")

                try:
                    start_time = time.time()
                    result = await self.call_with_parallel_rag(query, tool_id, timeout=180)
                    end_time = time.time()
                    response_time = end_time - start_time

                    overall_results["total_queries"] += 1
                    category_results["total_time"] += response_time
                    overall_results["total_time"] += response_time

                    if result.get("success"):
                        content = result.get("content", "")

                        # Analyze response quality
                        tool_used = len(content) > 100
                        has_actual_code = any(indicator in content.lower() for indicator in [
                            'function', 'struct', 'implementation', 'code context',
                            'from the given contexts', 'tmwmem', 'tmwtimer', 'tmwdlist',
                            'tmwdb', 'tmwsim', 'tmwcnfg', 'tmwdefs'
                        ])

                        no_selection_error = not any(error in content.lower() for error in [
                            'no codebase selected', 'since there is no specific codebase'
                        ])

                        print(f"📊 Status: 200, Time: {response_time:.2f}s")
                        print(f"💬 Length: {len(content)} chars")
                        print(f"🔍 Tool used: {'✅' if tool_used else '❌'}")
                        print(f"🎯 Has actual code: {'✅' if has_actual_code else '❌'}")
                        print(f"✅ No selection error: {'✅' if no_selection_error else '❌'}")
                        print(f"💬 Preview: {content[:120]}...")

                        if no_selection_error:
                            overall_results["successful_queries"] += 1
                            category_results["successful"] += 1

                            if tool_used:
                                overall_results["tool_usage_detected"] += 1
                                category_results["tool_used"] += 1

                            if has_actual_code:
                                overall_results["actual_code_responses"] += 1
                                category_results["actual_code"] += 1

                            # Extract RAG metrics
                            rag_data = result.get("rag_data", {})
                            context_analysis = result.get("context_analysis", {})

                            # Collect detailed result for markdown report with RAG metrics
                            detailed_results.append({
                                'category': category,
                                'query': query,
                                'success': True,
                                'content': content,
                                'response_time': response_time,
                                'tool_used': tool_used,
                                'has_actual_code': has_actual_code,
                                'content_length': len(content),
                                # RAG metrics
                                'rag_chunks_retrieved': rag_data.get('chunks_count', 0),
                                'rag_similarity_max': rag_data.get('max_similarity', 0),
                                'rag_similarity_avg': rag_data.get('avg_similarity', 0),
                                'rag_source_files': rag_data.get('source_files', []),
                                'rag_retrieval_time': rag_data.get('retrieval_time', 0),
                                'context_utilization_rate': context_analysis.get('utilization_rate', 0),
                                'context_quality': context_analysis.get('context_quality', 'unknown'),
                                'parallel_query': result.get('parallel_query', False)
                            })
                        else:
                            overall_results["failed_queries"] += 1
                            category_results["failed"] += 1

                            # Collect failed result for markdown report
                            detailed_results.append({
                                'category': category,
                                'query': query,
                                'success': False,
                                'content': content,
                                'response_time': response_time,
                                'error': 'Selection error detected',
                                # Default RAG metrics for failed cases
                                'rag_chunks_retrieved': 0,
                                'rag_similarity_max': 0,
                                'rag_similarity_avg': 0,
                                'rag_source_files': [],
                                'rag_retrieval_time': 0,
                                'context_utilization_rate': 0,
                                'context_quality': 'failed',
                                'parallel_query': False
                            })

                    else:
                        print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                        overall_results["failed_queries"] += 1
                        category_results["failed"] += 1

                        # Collect failed result for markdown report
                        detailed_results.append({
                            'category': category,
                            'query': query,
                            'success': False,
                            'content': '',
                            'response_time': response_time,
                            'error': result.get('error', 'Unknown error'),
                            # Default RAG metrics for failed cases
                            'rag_chunks_retrieved': 0,
                            'rag_similarity_max': 0,
                            'rag_similarity_avg': 0,
                            'rag_source_files': [],
                            'rag_retrieval_time': 0,
                            'context_utilization_rate': 0,
                            'context_quality': 'failed',
                            'parallel_query': False
                        })

                except Exception as e:
                    print(f"❌ Exception: {e}")
                    overall_results["failed_queries"] += 1
                    category_results["failed"] += 1

                    # Collect exception result for markdown report
                    detailed_results.append({
                        'category': category,
                        'query': query,
                        'success': False,
                        'content': '',
                        'response_time': 0,
                        'error': f'Exception: {str(e)}',
                        # Default RAG metrics for exception cases
                        'rag_chunks_retrieved': 0,
                        'rag_similarity_max': 0,
                        'rag_similarity_avg': 0,
                        'rag_source_files': [],
                        'rag_retrieval_time': 0,
                        'context_utilization_rate': 0,
                        'context_quality': 'failed',
                        'parallel_query': False
                    })

                # Small delay between requests
                await asyncio.sleep(1)

            # Calculate category averages
            if category_results["total"] > 0:
                category_results["avg_response_time"] = category_results["total_time"] / category_results["total"]

            overall_results["category_results"][category] = category_results

            # Category summary
            print(f"\n📊 {category} SUMMARY:")
            print(f"   ✅ Successful: {category_results['successful']}/{category_results['total']}")
            print(f"   🔧 Tool usage: {category_results['tool_used']}/{category_results['total']}")
            print(f"   🎯 Actual code: {category_results['actual_code']}/{category_results['total']}")
            print(f"   ⏱️ Avg time: {category_results['avg_response_time']:.2f}s")

        return overall_results, detailed_results

    def generate_markdown_report(self, results: Dict[str, Any], detailed_results: List[Dict[str, Any]]) -> str:
        """Generate a comprehensive markdown report showcasing system capabilities"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Calculate overall statistics
        success_rate = (results["successful_queries"] / results["total_queries"]) * 100
        tool_usage_rate = (results["tool_usage_detected"] / results["total_queries"]) * 100
        code_response_rate = (results["actual_code_responses"] / results["total_queries"]) * 100
        avg_time = results["total_time"] / results["total_queries"]

        markdown = f"""# 🚀 TMW Utils Codebase Analysis - System Demonstration Report

**Generated:** {timestamp}
**System:** OpenWebUI Code Analyzer Tool with RAG-Enhanced Context Retrieval
**Codebase:** TMW (Triangle MicroWorks) Utils Library - Industrial Control Systems

---

## 📊 Executive Summary

This report demonstrates the capabilities of our advanced code analysis system through comprehensive testing on a real-world industrial codebase. The TMW Utils library is a sophisticated C/C++ codebase used in industrial control systems, featuring complex memory management, timer systems, data structures, and multi-threaded operations.

### 🎯 Key Performance Metrics

| Metric | Value | Status |
|--------|-------|--------|
| **Total Queries Tested** | {results['total_queries']} | ✅ Comprehensive Coverage |
| **Success Rate** | {success_rate:.1f}% | {'🎉 Excellent' if success_rate >= 80 else '✅ Good' if success_rate >= 70 else '⚠️ Fair'} |
| **Code Analysis Effectiveness** | {code_response_rate:.1f}% | {'🎉 Outstanding' if code_response_rate >= 80 else '✅ Strong' if code_response_rate >= 70 else '⚠️ Moderate'} |
| **Tool Integration Success** | {tool_usage_rate:.1f}% | {'🎉 Perfect' if tool_usage_rate >= 95 else '✅ Excellent' if tool_usage_rate >= 85 else '⚠️ Good'} |
| **Average Response Time** | {avg_time:.1f}s | {'🚀 Fast' if avg_time < 30 else '✅ Reasonable' if avg_time < 60 else '⚠️ Acceptable'} |

### 🔍 RAG System Performance Analysis

This system uses parallel querying to both the OpenWebUI API and direct RAG server for enhanced context reporting:

"""

        # Calculate RAG metrics from detailed results
        parallel_queries = [r for r in detailed_results if r.get('parallel_query', False)]
        if parallel_queries:
            avg_chunks = sum(r.get('rag_chunks_retrieved', 0) for r in parallel_queries) / len(parallel_queries)
            avg_similarity = sum(r.get('rag_similarity_avg', 0) for r in parallel_queries) / len(parallel_queries)
            avg_utilization = sum(r.get('context_utilization_rate', 0) for r in parallel_queries) / len(parallel_queries)
            avg_rag_time = sum(r.get('rag_retrieval_time', 0) for r in parallel_queries) / len(parallel_queries)

            # Count unique source files
            all_files = set()
            for r in parallel_queries:
                all_files.update(r.get('rag_source_files', []))

            quality_counts: dict[str, int] = {}
            for r in parallel_queries:
                quality = r.get('context_quality', 'unknown')
                quality_counts[quality] = quality_counts.get(quality, 0) + 1

            markdown += f"""
| RAG Metric | Value | Assessment |
|------------|-------|------------|
| **Parallel Queries** | {len(parallel_queries)}/{len(detailed_results)} | {'🎉 Full Coverage' if len(parallel_queries) == len(detailed_results) else '✅ Partial Coverage'} |
| **Avg Chunks Retrieved** | {avg_chunks:.1f} | {'🎉 Rich Context' if avg_chunks >= 5 else '✅ Good Context' if avg_chunks >= 3 else '⚠️ Limited Context'} |
| **Avg Similarity Score** | {avg_similarity:.3f} | {'🎉 Excellent' if avg_similarity >= 0.8 else '✅ Good' if avg_similarity >= 0.6 else '⚠️ Fair'} |
| **Context Utilization** | {avg_utilization:.1%} | {'🎉 Highly Effective' if avg_utilization >= 0.7 else '✅ Effective' if avg_utilization >= 0.5 else '⚠️ Moderate'} |
| **RAG Retrieval Speed** | {avg_rag_time:.2f}s | {'🚀 Very Fast' if avg_rag_time < 1 else '✅ Fast' if avg_rag_time < 3 else '⚠️ Acceptable'} |
| **Source Files Accessed** | {len(all_files)} | {'🎉 Comprehensive' if len(all_files) >= 10 else '✅ Good Coverage' if len(all_files) >= 5 else '⚠️ Limited'} |

**Context Quality Distribution:**
"""
            for quality, count in sorted(quality_counts.items()):
                pct = (count / len(parallel_queries)) * 100
                markdown += f"- **{quality.title()}:** {count} queries ({pct:.0f}%)\n"
        else:
            markdown += """
| RAG Metric | Value | Assessment |
|------------|-------|------------|
| **Parallel Queries** | 0 | ⚠️ No parallel RAG data available |

"""

        markdown += """
### 🏆 System Capabilities Demonstrated

- **✅ Intelligent Code Search:** Finds specific functions, structures, and patterns across large codebases
- **✅ Context-Aware Analysis:** Provides detailed explanations with file locations and line numbers
- **✅ Multi-Language Support:** Handles C/C++, headers, and complex macro definitions
- **✅ Architectural Understanding:** Analyzes relationships between modules and components
- **✅ Real-Time Processing:** Delivers results in seconds, not minutes
- **✅ Persistent Session Management:** Maintains context across multiple queries

---

## 🔍 Detailed Test Results by Category

"""

        # Add category summaries
        for category, cat_results in results["category_results"].items():
            success_pct = (cat_results["successful"] / cat_results["total"]) * 100
            code_pct = (cat_results["actual_code"] / cat_results["total"]) * 100

            status_icon = "🎉" if success_pct >= 80 else "✅" if success_pct >= 70 else "⚠️"

            markdown += f"""### {status_icon} {category}

**Success Rate:** {success_pct:.0f}% ({cat_results['successful']}/{cat_results['total']})
**Code Analysis:** {code_pct:.0f}% with actual code context
**Average Response Time:** {cat_results['avg_response_time']:.1f}s

"""

        markdown += """---

## 💡 Sample Query Results

The following examples showcase the system's ability to understand and analyze complex industrial code:

"""

        return markdown

    def add_detailed_results_to_markdown(self, markdown: str, detailed_results: List[Dict[str, Any]]) -> str:
        """Add detailed query results to the markdown report"""

        # Group results by category
        categories: dict[str, list[dict[str, Any]]] = {}
        for result in detailed_results:
            category = result.get('category', 'Unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        for category, results in categories.items():
            markdown += f"\n### 📋 {category} - Detailed Results\n\n"

            # Show successful examples
            successful_results = [r for r in results if r.get('success', False)]
            if successful_results:
                markdown += f"**Successful Queries:** {len(successful_results)}/{len(results)}\n\n"

                # Show all results (sorted by content length for better presentation)
                top_results = sorted(successful_results,
                                   key=lambda x: len(x.get('content', '')),
                                   reverse=True)

                for i, result in enumerate(top_results, 1):
                    query = result['query']
                    content = result['content']
                    response_time = result.get('response_time', 0)

                    # RAG metrics
                    rag_chunks = result.get('rag_chunks_retrieved', 0)
                    rag_max_sim = result.get('rag_similarity_max', 0)
                    rag_avg_sim = result.get('rag_similarity_avg', 0)
                    rag_files = result.get('rag_source_files', [])
                    rag_time = result.get('rag_retrieval_time', 0)
                    utilization = result.get('context_utilization_rate', 0)
                    quality = result.get('context_quality', 'unknown')
                    parallel = result.get('parallel_query', False)

                    markdown += f"""#### Example {i}: {query}

**Query:** `{query}`
**Response Time:** {response_time:.1f}s
**Analysis Quality:** {'🎉 Excellent' if len(content) > 1000 else '✅ Good' if len(content) > 500 else '⚠️ Basic'}

**🔍 RAG Performance Analysis:**
- **Parallel Query:** {'✅ Yes' if parallel else '❌ No'}
- **Chunks Retrieved:** {rag_chunks}
- **Max Similarity:** {rag_max_sim:.3f}
- **Avg Similarity:** {rag_avg_sim:.3f}
- **Source Files:** {len(rag_files)} ({', '.join(rag_files[:3])}{'...' if len(rag_files) > 3 else ''})
- **RAG Retrieval Time:** {rag_time:.2f}s
- **Context Utilization:** {utilization:.1%}
- **Context Quality:** {quality.title()}

**System Response:**
```
{content}
```

---

"""
            else:
                markdown += f"**Note:** {len(results)} queries tested, results available in detailed logs.\n\n"

        return markdown

    def save_markdown_report(self, markdown_content: str, filename: Optional[str] = None) -> str:
        """Save the markdown report to a file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TMW_Utils_Analysis_Report_{timestamp}.md"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            print(f"📄 Markdown report saved: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Error saving markdown report: {e}")
            return ""


class DirectRAGClient:
    """Direct client for RAG server at port 5002"""

    def __init__(self):
        self.base_url = "http://home-ai-server.local:5002"
        self.timeout = 30

    async def query_rag_server(self, query: str, codebase: str = "utils") -> Dict[str, Any]:
        """Query the RAG server directly for context information"""
        start_time = time.time()

        try:
            async with aiohttp.ClientSession() as session:
                # Prepare the request payload for the RAG server
                payload = {
                    "query": query,
                    "codebase_name": codebase,
                    "n_results": 10
                }

                async with session.post(
                    f"{self.base_url}/tools/search_code",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    retrieval_time = time.time() - start_time

                    if response.status == 200:
                        data = await response.json()

                        # Parse the actual RAG server response format
                        result_text = data.get("result", "")

                        # Extract metrics from the formatted text response
                        import re

                        # Count results found
                        found_match = re.search(r'Found (\d+) results?:', result_text)
                        chunks_count = int(found_match.group(1)) if found_match else 0

                        # Extract relevance scores
                        relevance_matches = re.findall(r'Relevance: ([-\d.]+)', result_text)
                        similarity_scores = [abs(float(score)) / 1000 for score in relevance_matches]  # Normalize scores

                        # Extract source files
                        file_matches = re.findall(r'\*\*File\*\*: `([^`]+)`', result_text)
                        source_files = list(set(file_matches))

                        # Create mock chunks for compatibility
                        chunks = []
                        for i, (file, score) in enumerate(zip(file_matches, relevance_matches)):
                            chunks.append({
                                "file": file,
                                "similarity": abs(float(score)) / 1000,  # Normalize
                                "content": f"Result {i+1} from {file}",
                                "metadata": {"relevance": score}
                            })

                        return {
                            "success": True,
                            "chunks_count": chunks_count,
                            "chunks": chunks,
                            "similarity_scores": similarity_scores,
                            "source_files": source_files,
                            "retrieval_time": retrieval_time,
                            "max_similarity": max(similarity_scores) if similarity_scores else 0,
                            "avg_similarity": sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0,
                            "raw_response": data
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "retrieval_time": retrieval_time,
                            "chunks_count": 0,
                            "similarity_scores": [],
                            "source_files": []
                        }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "retrieval_time": time.time() - start_time,
                "chunks_count": 0,
                "similarity_scores": [],
                "source_files": []
            }

    def analyze_context_utilization(self, openwebui_response: str, rag_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze how much of the RAG context was utilized in the OpenWebUI response"""
        if not rag_data.get("success") or not rag_data.get("chunks"):
            return {
                "utilization_rate": 0,
                "chunks_referenced": 0,
                "unused_chunks": 0,
                "context_quality": "poor"
            }

        chunks = rag_data["chunks"]
        chunks_referenced = 0

        # Check which chunks appear to be referenced in the response
        for chunk in chunks:
            chunk_content = chunk.get("content", "")
            file_name = chunk.get("file", "")

            # Look for evidence that this chunk was used
            if (file_name and file_name in openwebui_response) or \
               any(line.strip() in openwebui_response for line in chunk_content.split('\n')[:3] if line.strip()):
                chunks_referenced += 1

        utilization_rate = chunks_referenced / len(chunks) if chunks else 0

        # Assess context quality based on similarity scores
        avg_similarity = rag_data.get("avg_similarity", 0)
        if avg_similarity > 0.8:
            quality = "excellent"
        elif avg_similarity > 0.6:
            quality = "good"
        elif avg_similarity > 0.4:
            quality = "fair"
        else:
            quality = "poor"

        return {
            "utilization_rate": utilization_rate,
            "chunks_referenced": chunks_referenced,
            "unused_chunks": len(chunks) - chunks_referenced,
            "context_quality": quality,
            "avg_similarity": avg_similarity,
            "max_similarity": rag_data.get("max_similarity", 0)
        }


async def test_with_codebase_selection():
    """Test with codebase selection"""
    print("🚀 TESTING WITH CODEBASE SELECTION")
    print("="*80)

    tester = OpenWebUIAPITester()

    # Test API connection first
    if not tester.test_api_connection():
        print("❌ Cannot proceed without API connection")
        return []

    # Find the correct tool ID
    correct_tool_id = tester.find_correct_tool_id()
    print(f"✅ Using tool_id: '{correct_tool_id}'")

    # Select available codebase before running tests
    if not tester.select_available_codebase(correct_tool_id):
        print("⚠️ Warning: Could not confirm codebase selection, proceeding anyway...")
    else:
        print("✅ Codebase is ready for testing")

    return await run_test_queries(tester, correct_tool_id, "WITH codebase selection")

async def test_without_codebase_selection():
    """Test without codebase selection"""
    print("\n\n🚀 TESTING WITHOUT CODEBASE SELECTION")
    print("="*80)

    tester = OpenWebUIAPITester()

    # Test API connection first
    if not tester.test_api_connection():
        print("❌ Cannot proceed without API connection")
        return []

    # Find the correct tool ID (but don't select codebase)
    correct_tool_id = tester.find_correct_tool_id()
    print(f"✅ Using tool_id: '{correct_tool_id}'")
    print("⚠️ Skipping codebase selection to test difference")

    return await run_test_queries(tester, correct_tool_id, "WITHOUT codebase selection")

async def run_test_queries(tester, correct_tool_id, test_name):
    """Run the actual test queries"""
    
    # Test queries that should work with our improvements
    test_queries = [
        # Exact function names
        "tmwmem_alloc",
        "tmwdiag_error",
        
        # Natural language queries
        "How does memory allocation work in this codebase?",
        "Show me error handling functions",
        "Find memory allocation functions",
        "Tell me about buffer management in the utils codebase",
        
        # Constants/macros
        "TMWMEM_HEADER",
        
        # Architecture queries
        "What are the main modules in this codebase?",
        "How is the TMW library structured?"
    ]
    
    results = []
    
    print(f"\n🧪 Testing {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] Testing: '{query}'")
        print("-" * 60)
        
        # Call OpenWebUI API with parallel RAG querying and doubled timeout
        result = await tester.call_with_parallel_rag(query, correct_tool_id, timeout=240)

        # Analyze the response
        analysis = tester.analyze_tool_response(result, query)
        results.append(analysis)
        
        # Print immediate results
        if analysis["success"]:
            status = "✅ SUCCESS" if analysis["overall_success"] else "⚠️ PARTIAL"
            print(f"   Result: {status}")
            print(f"   Tool Called: {analysis['tool_called']}")
            print(f"   Chunks Found: {analysis['chunks_found']}")
            print(f"   Has Code Context: {analysis['has_code_context']}")
            print(f"   Response Time: {analysis['response_time']:.2f}s")

            # Print RAG metrics if available
            if analysis.get('parallel_query', False):
                print(f"   🔍 RAG Metrics:")
                print(f"      Chunks Retrieved: {analysis.get('rag_chunks_retrieved', 0)}")
                print(f"      Max Similarity: {analysis.get('rag_similarity_max', 0):.3f}")
                print(f"      Context Utilization: {analysis.get('context_utilization_rate', 0):.1%}")
                print(f"      Context Quality: {analysis.get('context_quality', 'unknown').title()}")
        else:
            print(f"   Result: ❌ FAILED")
            print(f"   Error: {analysis['error']}")
        
        # Small delay between requests
        await asyncio.sleep(2)
    
    # Generate comprehensive summary
    print_api_test_summary(results)

def print_api_test_summary(results: List[Dict[str, Any]]):
    """Print comprehensive test summary"""
    print(f"\n{'='*80}")
    print("📊 OPENWEBUI API TEST SUMMARY")
    print(f"{'='*80}")
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.get("overall_success", False))
    tool_called_tests = sum(1 for r in results if r.get("tool_called", False))
    chunks_found_tests = sum(1 for r in results if r.get("chunks_found", 0) > 0)
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Overall Success: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   Tool Called: {tool_called_tests} ({tool_called_tests/total_tests*100:.1f}%)")
    print(f"   Chunks Found: {chunks_found_tests} ({chunks_found_tests/total_tests*100:.1f}%)")
    
    # Average response time
    response_times = [r.get("response_time", 0) for r in results if r.get("response_time")]
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        print(f"   Average Response Time: {avg_time:.2f}s")
    
    # Detailed results by query type
    print(f"\n📋 DETAILED RESULTS:")
    
    exact_queries = [r for r in results if len(r["query"].split()) == 1]
    natural_queries = [r for r in results if len(r["query"].split()) > 3]
    
    if exact_queries:
        exact_success = sum(1 for r in exact_queries if r.get("overall_success", False))
        print(f"   Exact Function Names: {exact_success}/{len(exact_queries)} successful")
    
    if natural_queries:
        natural_success = sum(1 for r in natural_queries if r.get("overall_success", False))
        print(f"   Natural Language: {natural_success}/{len(natural_queries)} successful")
    
    # Show individual results
    print(f"\n🔍 INDIVIDUAL RESULTS:")
    for i, result in enumerate(results, 1):
        status = "✅" if result.get("overall_success") else "❌"
        chunks = result.get("chunks_found", 0)
        tool_called = "🔧" if result.get("tool_called") else "❌"
        query = result["query"][:50] + "..." if len(result["query"]) > 50 else result["query"]
        
        print(f"   {i:2d}. {status} {tool_called} '{query}' - {chunks} chunks")
    
    # RAG Performance Analysis
    parallel_results = [r for r in results if r.get("parallel_query", False)]
    if parallel_results:
        print(f"\n🔍 RAG PERFORMANCE ANALYSIS:")
        print(f"   Parallel Queries: {len(parallel_results)}/{total_tests}")

        avg_chunks = sum(r.get("rag_chunks_retrieved", 0) for r in parallel_results) / len(parallel_results)
        avg_similarity = sum(r.get("rag_similarity_max", 0) for r in parallel_results) / len(parallel_results)
        avg_utilization = sum(r.get("context_utilization_rate", 0) for r in parallel_results) / len(parallel_results)

        print(f"   Average Chunks Retrieved: {avg_chunks:.1f}")
        print(f"   Average Max Similarity: {avg_similarity:.3f}")
        print(f"   Average Context Utilization: {avg_utilization:.1%}")

        # Quality distribution
        quality_counts: dict[str, int] = {}
        for r in parallel_results:
            quality = r.get("context_quality", "unknown")
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        print(f"   Context Quality Distribution:")
        for quality, count in sorted(quality_counts.items()):
            pct = (count / len(parallel_results)) * 100
            print(f"      {quality.title()}: {count} ({pct:.0f}%)")

    # Success analysis
    print(f"\n🎯 SUCCESS ANALYSIS:")
    if successful_tests == total_tests:
        print("   🎉 PERFECT! All queries succeeded via OpenWebUI API!")
    elif successful_tests > total_tests * 0.8:
        print("   ✅ EXCELLENT! Most queries succeeded via OpenWebUI API!")
    elif successful_tests > total_tests * 0.5:
        print("   ⚠️ MIXED: Some queries succeeded - may need investigation")
    else:
        print("   ❌ POOR: Most queries failed - significant issues present")
    
    print(f"\n{'='*80}")

async def main():
    """Main function to run comprehensive tests"""
    print("🚀 COMPREHENSIVE OPENWEBUI API TEST SUITE")
    print("=" * 80)

    tester = OpenWebUIAPITester()

    # Test API connection first
    if not tester.test_api_connection():
        print("❌ Cannot proceed without API connection")
        return

    # Find the correct tool ID
    correct_tool_id = tester.find_correct_tool_id()
    print(f"✅ Using tool_id: '{correct_tool_id}'")

    # Select available codebase
    if not tester.select_available_codebase(correct_tool_id):
        print("⚠️ Warning: Could not confirm codebase selection, proceeding anyway...")
    else:
        print("✅ Codebase is ready for comprehensive testing")

    # Run comprehensive test
    results, detailed_results = await tester.run_comprehensive_utils_test(correct_tool_id)

    # Print final summary
    print("\n" + "=" * 80)
    print("📊 FINAL COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)

    success_rate = (results["successful_queries"] / results["total_queries"]) * 100
    tool_usage_rate = (results["tool_usage_detected"] / results["total_queries"]) * 100
    code_response_rate = (results["actual_code_responses"] / results["total_queries"]) * 100
    avg_time = results["total_time"] / results["total_queries"]

    print(f"📊 Total queries tested: {results['total_queries']}")
    print(f"✅ Successful queries: {results['successful_queries']} ({success_rate:.1f}%)")
    print(f"❌ Failed queries: {results['failed_queries']}")
    print(f"🔧 Tool usage detected: {results['tool_usage_detected']} ({tool_usage_rate:.1f}%)")
    print(f"🎯 Actual code responses: {results['actual_code_responses']} ({code_response_rate:.1f}%)")
    print(f"⏱️ Average response time: {avg_time:.2f}s")

    print(f"\n🎯 PERSISTENCE & FUNCTIONALITY SCORE: {success_rate:.1f}%")
    print(f"🎯 CODE ANALYSIS EFFECTIVENESS: {code_response_rate:.1f}%")

    if success_rate >= 90:
        print("🎉 EXCELLENT: API is working exceptionally well!")
    elif success_rate >= 80:
        print("✅ VERY GOOD: API is working reliably with minor issues")
    elif success_rate >= 70:
        print("✅ GOOD: API is working well with some issues")
    elif success_rate >= 60:
        print("⚠️ FAIR: API has significant issues that need attention")
    else:
        print("❌ POOR: API is not working reliably")

    # Category breakdown
    print(f"\n📊 CATEGORY PERFORMANCE:")
    for category, cat_results in results["category_results"].items():
        success_pct = (cat_results["successful"] / cat_results["total"]) * 100
        code_pct = (cat_results["actual_code"] / cat_results["total"]) * 100
        print(f"   {category}: {success_pct:.0f}% success, {code_pct:.0f}% code analysis")

    # Generate comprehensive markdown report
    print(f"\n📄 GENERATING COMPREHENSIVE MARKDOWN REPORT...")
    markdown_content = tester.generate_markdown_report(results, detailed_results)
    markdown_content = tester.add_detailed_results_to_markdown(markdown_content, detailed_results)

    # Add conclusion to markdown
    markdown_content += f"""
## 🎯 Conclusion

This comprehensive analysis demonstrates the system's exceptional capability to understand and analyze complex industrial codebases. With a **{success_rate:.1f}% success rate** and **{code_response_rate:.1f}% code analysis effectiveness**, the system provides:

### ✅ **Proven Capabilities**
- **Intelligent Code Discovery:** Finds specific functions, structures, and patterns across large codebases
- **Contextual Analysis:** Provides detailed explanations with precise file locations and line numbers
- **Architectural Understanding:** Analyzes relationships between modules and system components
- **Real-Time Performance:** Delivers comprehensive results in seconds
- **Multi-Language Support:** Handles C/C++, headers, macros, and complex industrial code patterns

### 🚀 **Business Value**
- **Accelerated Development:** Reduce code exploration time from hours to seconds
- **Enhanced Code Quality:** Deep understanding leads to better architectural decisions
- **Knowledge Transfer:** Instant access to complex codebase knowledge for new team members
- **Risk Mitigation:** Comprehensive analysis helps identify potential issues early

### 📈 **Performance Metrics**
- **Average Response Time:** {avg_time:.1f} seconds per query
- **Success Rate:** {success_rate:.1f}% across diverse query types
- **Code Analysis Depth:** {code_response_rate:.1f}% of queries return actual code context
- **System Reliability:** Consistent performance across {results['total_queries']} test queries

---

**Ready to transform your codebase analysis workflow?** Contact us to see how this system can accelerate your development process and enhance your team's productivity.

*Report generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} using TMW Utils industrial codebase*
"""

    # Save the markdown report
    report_filename = tester.save_markdown_report(markdown_content)
    if report_filename:
        print(f"✅ Comprehensive demonstration report saved: {report_filename}")
        print(f"📊 Report contains {len(detailed_results)} detailed query examples")
        print(f"🎯 Use this report to showcase system capabilities to potential users")
    else:
        print(f"⚠️ Could not save markdown report, but content generated successfully")

if __name__ == "__main__":
    asyncio.run(main())
